from fastapi import Depends

from src.agents.DiagnosticAgentStreaming import DiagnosticAgentStreaming
from src.agents.todos_agent import TodosAgent
from src.agents.utils.PromptLoader import PromptLoader
from src.dependencies import get_job_service, get_appliance_service, get_property_service
from src.services.jobs import JobService


prompt_loader = PromptLoader(default_prompt_version="DiagnosticAgent_v6.txt")

# Load the system prompt using the PromptLoader
_SYSTEM_PROMPT = prompt_loader.load_system_prompt()
_TODOS_AGENT_PROMPT = prompt_loader.load_system_prompt(prompt_version="todos_extraction_prompt.txt")


def get_diagnostic_agent_streaming(
    job_service: JobService = Depends(get_job_service),
    appliance_service=Depends(get_appliance_service),
    property_service=Depends(get_property_service),
):
    # Create a new instance with the injected job_service
    return DiagnosticAgentStreaming(
        job_service=job_service,
        appliance_service=appliance_service,
        property_service=property_service,
        agent_prompt=_SYSTEM_PROMPT,
    )


def get_todos_agent():
    return TodosAgent(agent_prompt=_TODOS_AGENT_PROMPT)
