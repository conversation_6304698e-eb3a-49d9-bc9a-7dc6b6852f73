import pytest
from unittest.mock import AsyncMock, patch

from fastapi.testclient import TestClient

from src.schemas import FindTodosRe<PERSON>, FindTodosResponse


TEST_API_KEY = "test-api-key-123"
DUMMY_API_KEY = "dummy-key-for-testing"

MOCK_USER_ID = 1
MOCK_CHAT_ID = 101
MOCK_REQUEST = FindTodosRequest(
    userId=MOCK_USER_ID,
    chatId=MOCK_CHAT_ID,
    messages=[],
)

@pytest.fixture
def todos_agent_mock():
    return AsyncMock()


@pytest.fixture
def client(todos_agent_mock):
    from src.dependencies_agents import get_todos_agent
    with patch(
        'src.agents.utils.PromptLoader.PromptLoader.load_system_prompt', return_value="Mock system prompt for testing"
    ):
        from src.main import app
        app.dependency_overrides[get_todos_agent] = lambda: todos_agent_mock
        with TestClient(app) as c:
            yield c

        app.dependency_overrides.clear()


@pytest.fixture(autouse=True)
def override_dependencies_and_env(monkeypatch):
    monkeypatch.setenv("AI_ENGINE_API_KEY", TEST_API_KEY)


@pytest.mark.asyncio
async def test_find_todos(client: TestClient, todos_agent_mock):
    headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
    response = client.post("/todos/find", headers=headers, json=MOCK_REQUEST.model_dump())
    assert response.status_code == 200
    todos_agent_mock.extract_todos.assert_called_once_with(MOCK_REQUEST.messages)


@pytest.mark.asyncio
async def test_find_todos_unauthorized(client: TestClient):
    headers = {"Authorization": "Bearer invalid-key"}
    response = client.post("/todos/find", headers=headers, json=MOCK_REQUEST.model_dump())
    assert response.status_code == 401
