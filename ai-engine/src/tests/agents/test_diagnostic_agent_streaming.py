import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch, call

from src.agents.DiagnosticAgentStreaming import DiagnosticAgentStreaming
from src.schemas import ImageUrl, JobSummary, ParseFileRequest
from src.services.jobs import JobService

TEST_USER_ID = "test_user_123"
TEST_CHAT_ID = "test_chat_789"
TEST_INPUT = "My faucet is leaking from multiple places"


# Mock chunk class to simulate LLM chunk responses
class MockChunk:
    def __init__(self, content):
        self.content = content
        self.tool_call_chunks = None
        self.tool_calls = None


# symuluje odpowiedzi LLMa
async def async_generator_from_list(items):
    for item in items:
        yield MockChunk(item)


@pytest.mark.asyncio
# To patchuje uzycia w metodzie __init__
@patch("src.agents.DiagnosticAgentStreaming.QdrantDAO", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.JobSummaryExtractor", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.ImageAgentGemini", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.FileParser", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.RAGAgent", autospec=True)
@patch("langsmith.Client")
@patch("langsmith.run_helpers.get_run_tree_context")  # Mock Langsmith context
async def test_options_image_command_across_chunks_calls_image_agent(
        mock_get_run_tree_context,
        mock_langsmith_client,
        MockRAGAgent,
        MockFileParser,
        MockImageAgentGemini,
        MockJobSummaryExtractor,
        MockQdrantDAO,
        mocker,  # Use pytest-mock fixture for easier patching
):
    """
    Test that when [OptionsImage:...] is split across chunks,
    handle_search_request is called for each option.
    """

    # 1. Mock JobService dependency for constructor
    mock_job_service = MagicMock(spec=JobService)
    mock_appliance_service = MagicMock()
    mock_property_service = MagicMock()
    # Mock the async method called within the agent
    mock_job_service.get_last_job_for_user_and_chat = AsyncMock(return_value=None)

    # 2. Use mocker fixture for patching methods on the instance later
    mocker.patch.object(DiagnosticAgentStreaming, "_process_single_attachment", new_callable=AsyncMock,
                        return_value="mock image description")

    # 3. Instantiate the Agent with mocked dependencies
    # Mocks for classes used internally will be created automatically byautospec=True
    # but we need instances for configuration
    mock_qdrant_instance = MockQdrantDAO.return_value
    mock_summary_instance = MockJobSummaryExtractor.return_value
    mock_image_agent_instance = MockImageAgentGemini.return_value
    mock_rag_agent_instance = MockRAGAgent.return_value

    mock_qdrant_instance.get_chat_history = AsyncMock(return_value=[])  # No history
    mock_qdrant_instance.update_chat_search_on_history_change = AsyncMock()
    mock_summary_instance.extract_job_summary.return_value = ("Final text", None)  # No JobSummary

    # NAJWAZNIEJSZE: ImageAgent's search method
    # Needs to return a tuple like the real method for _handle_options_image_command
    mock_image_agent_instance.handle_search_request = AsyncMock(
        side_effect=lambda query, max_images: (
            query,  # Simulate returning the original query
            [ImageUrl(imageUrl=f"http://example.com/{query.replace(' ', '_')}.jpg", description=query)]
        )
    )

    mock_run_context = MagicMock()
    mock_run_context.metadata = {}
    mock_get_run_tree_context.return_value = mock_run_context

    # Instantiate the agent AFTER patching __init__ dependencies
    agent = DiagnosticAgentStreaming(
        job_service=mock_job_service,
        appliance_service=mock_appliance_service,
        property_service=mock_property_service,
        agent_prompt="TEST"
    )

    # 4. Define the simulated LLM Chunks
    llm_chunks_content = [
        "Okay, I understand. It sounds like the leak could be from a few places. ",
        "[OptionsImage: leaking from the base; leaking",
        " from the spout; other]",
        " Let me know which one it is."
    ]

    # Mock the LLM with tools
    mock_llm_with_tools = MagicMock()
    mock_llm_with_tools.astream = MagicMock(
        return_value=async_generator_from_list(llm_chunks_content)
    )

    # Mock the _create_llm_with_tools method to return our mock
    mocker.patch.object(agent, '_create_llm_with_tools', return_value=mock_llm_with_tools)

    output_chunks = []
    async for chunk in agent.process_next_message_stream(
            user_input=TEST_INPUT,
            user_id=TEST_USER_ID,
            chat_id=TEST_CHAT_ID,
            attachments=None
    ):
        output_chunks.append(chunk)

    # 1. Verify image_agent.handle_search_request calls
    mock_image_agent = agent.image_agent
    assert mock_image_agent.handle_search_request.call_count == 3

    expected_calls = [
        call("leaking from the base", max_images=1),
        call("leaking from the spout", max_images=1),
        call("other", max_images=1),
    ]

    # sany_order=True - due to asyncio.gather, the order might not be guaranteed
    mock_image_agent.handle_search_request.assert_has_calls(expected_calls, any_order=True)

    # 2. Verify content of yielded chunks
    final_data = None
    yielded_content = ""
    for chunk_str in output_chunks:
        if chunk_str.startswith("data:"):
            try:
                data = json.loads(chunk_str.split("data: ", 1)[1])
                if data.get("type") == "content":
                    yielded_content += data.get("data", "")
                elif data.get("type") == "final_data":
                    final_data = data.get("data")
            except json.JSONDecodeError:
                pass  # Ignore non-json data like end events

    # Assert that the command token is not present in the yielded text content
    assert "[OptionsImage:" not in yielded_content
    assert "leaking from the base; leaking from the spout; other" not in yielded_content
    assert "Okay, I understand" in yielded_content  # Check other text is present
    assert "Let me know which one it is" in yielded_content

    assert final_data is not None
    assert "imageClickableUrls" in final_data
    clickable_urls = final_data["imageClickableUrls"]
    assert len(clickable_urls) == 3
    assert any(url['description'] == 'leaking from the base' for url in clickable_urls)
    assert any(url['description'] == 'leaking from the spout' for url in clickable_urls)
    assert any(url['description'] == 'other' for url in clickable_urls)
    assert all("http://example.com/" in url['imageUrl'] for url in clickable_urls)

    # 3. Verify qdrant call
    mock_qdrant_instance.update_chat_search_on_history_change.assert_called_once()


@pytest.mark.asyncio
# To patchuje uzycia w metodzie __init__
@patch("src.agents.DiagnosticAgentStreaming.QdrantDAO", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.JobSummaryExtractor", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.ImageAgentGemini", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.FileParser", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.RAGAgent", autospec=True)
@patch("langsmith.Client")
@patch("langsmith.run_helpers.get_run_tree_context")  # Mock Langsmith context
async def test_web_search_command_across_chunks_calls_image_agent(
        mock_get_run_tree_context,
        mock_langsmith_client,
        MockRAGAgent,
        MockFileParser,
        MockImageAgentGemini,
        MockJobSummaryExtractor,
        MockQdrantDAO,
        mocker,  # Use pytest-mock fixture for easier patching
):
    """
    Test that when [web_search:...] is split across chunks,
    handle_search_request is called for each option.
    """

    # 1. Mock JobService dependency for constructor
    mock_job_service = MagicMock(spec=JobService)
    mock_appliance_service = MagicMock()
    mock_property_service = MagicMock()
    # Mock the async method called within the agent
    mock_job_service.get_last_job_for_user_and_chat = AsyncMock(return_value=None)

    # 2. Use mocker fixture for patching methods on the instance later
    mocker.patch.object(DiagnosticAgentStreaming, "_process_single_attachment", new_callable=AsyncMock,
                        return_value="mock image description")

    # 3. Instantiate the Agent with mocked dependencies
    # Mocks for classes used internally will be created automatically byautospec=True
    # but we need instances for configuration
    mock_qdrant_instance = MockQdrantDAO.return_value
    mock_summary_instance = MockJobSummaryExtractor.return_value
    mock_image_agent_instance = MockImageAgentGemini.return_value
    mock_rag_agent_instance = MockRAGAgent.return_value

    mock_qdrant_instance.get_chat_history = AsyncMock(return_value=[])  # No history
    mock_qdrant_instance.update_chat_search_on_history_change = AsyncMock()
    mock_summary_instance.extract_job_summary.return_value = ("Final text", None)  # No JobSummary

    # NAJWAZNIEJSZE: ImageAgent's search method
    # Needs to return a tuple like the real method for _handle_options_image_command
    mock_image_agent_instance.handle_search_request = AsyncMock(
        side_effect=lambda query, max_images: (
            query,  # Simulate returning the original query
            [ImageUrl(imageUrl=f"http://example.com/{query.replace(' ', '_')}.jpg", description=query)]
        )
    )

    mock_run_context = MagicMock()
    mock_run_context.metadata = {}
    mock_get_run_tree_context.return_value = mock_run_context

    # Instantiate the agent AFTER patching __init__ dependencies
    agent = DiagnosticAgentStreaming(
        job_service=mock_job_service,
        appliance_service=mock_appliance_service,
        property_service=mock_property_service,
        agent_prompt="TEST"
    )

    # 4. Define the simulated LLM Chunks
    llm_chunks_content = [
        "Okay, I understand. It sounds like you have a combi boiler. ",
        "[web search: combi",
        " boiler]",
        " Right?"
    ]

    # Mock the LLM with tools
    mock_llm_with_tools = MagicMock()
    mock_llm_with_tools.astream = MagicMock(
        return_value=async_generator_from_list(llm_chunks_content)
    )

    # Mock the _create_llm_with_tools method to return our mock
    mocker.patch.object(agent, '_create_llm_with_tools', return_value=mock_llm_with_tools)

    output_chunks = []
    async for chunk in agent.process_next_message_stream(
            user_input=TEST_INPUT,
            user_id=TEST_USER_ID,
            chat_id=TEST_CHAT_ID,
            attachments=None
    ):
        output_chunks.append(chunk)

    # 1. Verify image_agent.handle_search_request calls
    mock_image_agent = agent.image_agent
    assert mock_image_agent.handle_search_request.call_count == 1

    expected_calls = [
        call("combi boiler", max_images=1),
    ]

    # sany_order=True - due to asyncio.gather, the order might not be guaranteed
    mock_image_agent.handle_search_request.assert_has_calls(expected_calls, any_order=True)

    # 2. Verify content of yielded chunks
    final_data = None
    yielded_content = ""
    for chunk_str in output_chunks:
        if chunk_str.startswith("data:"):
            try:
                data = json.loads(chunk_str.split("data: ", 1)[1])
                if data.get("type") == "content":
                    yielded_content += data.get("data", "")
                elif data.get("type") == "final_data":
                    final_data = data.get("data")
            except json.JSONDecodeError:
                pass  # Ignore non-json data like end events

    # Assert that the command token is not present in the yielded text content
    assert "[web search:" not in yielded_content
    assert "Right?" in yielded_content  # Check other text is present

    assert final_data is not None
    assert "imageUrls" in final_data
    clickable_urls = final_data["imageUrls"]
    assert len(clickable_urls) == 1
    assert any(url['description'] == 'combi boiler' for url in clickable_urls)
    assert all("http://example.com/" in url['imageUrl'] for url in clickable_urls)

    # 3. Verify qdrant call
    mock_qdrant_instance.update_chat_search_on_history_change.assert_called_once()


@pytest.mark.asyncio
# To patchuje uzycia w metodzie __init__
@patch("src.agents.DiagnosticAgentStreaming.QdrantDAO", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.JobSummaryExtractor", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.ImageAgentGemini", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.FileParser", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.RAGAgent", autospec=True)
@patch("langsmith.Client")
@patch("langsmith.run_helpers.get_run_tree_context")  # Mock Langsmith context
async def test_web_search_command_across_chunks_with_square_bracket_as_a_last_char(
        mock_get_run_tree_context,
        mock_langsmith_client,
        MockRAGAgent,
        MockFileParser,
        MockImageAgentGemini,
        MockJobSummaryExtractor,
        MockQdrantDAO,
        mocker,  # Use pytest-mock fixture for easier patching
):
    """
    Test that when [web_search:...] is split across chunks,
    handle_search_request is called for each option.
    """

    # 1. Mock JobService dependency for constructor
    mock_job_service = MagicMock(spec=JobService)
    mock_appliance_service = MagicMock()
    mock_property_service = MagicMock()
    # Mock the async method called within the agent
    mock_job_service.get_last_job_for_user_and_chat = AsyncMock(return_value=None)

    # 2. Use mocker fixture for patching methods on the instance later
    mocker.patch.object(DiagnosticAgentStreaming, "_process_single_attachment", new_callable=AsyncMock,
                        return_value="mock image description")

    # 3. Instantiate the Agent with mocked dependencies
    # Mocks for classes used internally will be created automatically byautospec=True
    # but we need instances for configuration
    mock_qdrant_instance = MockQdrantDAO.return_value
    mock_summary_instance = MockJobSummaryExtractor.return_value
    mock_image_agent_instance = MockImageAgentGemini.return_value
    mock_rag_agent_instance = MockRAGAgent.return_value

    mock_qdrant_instance.get_chat_history = AsyncMock(return_value=[])  # No history
    mock_qdrant_instance.update_chat_search_on_history_change = AsyncMock()
    mock_summary_instance.extract_job_summary.return_value = ("Final text", None)  # No JobSummary

    # NAJWAZNIEJSZE: ImageAgent's search method
    # Needs to return a tuple like the real method for _handle_options_image_command
    mock_image_agent_instance.handle_search_request = AsyncMock(
        side_effect=lambda query, max_images: (
            query,  # Simulate returning the original query
            [ImageUrl(imageUrl=f"http://example.com/{query.replace(' ', '_')}.jpg", description=query)]
        )
    )

    mock_run_context = MagicMock()
    mock_run_context.metadata = {}
    mock_get_run_tree_context.return_value = mock_run_context

    # Instantiate the agent AFTER patching __init__ dependencies
    agent = DiagnosticAgentStreaming(
        job_service=mock_job_service,
        appliance_service=mock_appliance_service,
        property_service=mock_property_service,
        agent_prompt="TEST"
    )

    # 4. Define the simulated LLM Chunks
    llm_chunks_content = [
        "Okay, I understand. It sounds like you have a combi boiler. [we",
        "b search: combi",
        " boiler]",
        " Right?"
    ]

    # Mock the LLM with tools
    mock_llm_with_tools = MagicMock()
    mock_llm_with_tools.astream = MagicMock(
        return_value=async_generator_from_list(llm_chunks_content)
    )

    # Mock the _create_llm_with_tools method to return our mock
    mocker.patch.object(agent, '_create_llm_with_tools', return_value=mock_llm_with_tools)

    output_chunks = []
    async for chunk in agent.process_next_message_stream(
            user_input=TEST_INPUT,
            user_id=TEST_USER_ID,
            chat_id=TEST_CHAT_ID,
            attachments=None
    ):
        output_chunks.append(chunk)

    # 1. Verify image_agent.handle_search_request calls
    mock_image_agent = agent.image_agent
    assert mock_image_agent.handle_search_request.call_count == 1

    expected_calls = [
        call("combi boiler", max_images=1),
    ]

    # sany_order=True - due to asyncio.gather, the order might not be guaranteed
    mock_image_agent.handle_search_request.assert_has_calls(expected_calls, any_order=True)

    # 2. Verify content of yielded chunks
    final_data = None
    yielded_content = ""
    for chunk_str in output_chunks:
        if chunk_str.startswith("data:"):
            try:
                data = json.loads(chunk_str.split("data: ", 1)[1])
                if data.get("type") == "content":
                    yielded_content += data.get("data", "")
                elif data.get("type") == "final_data":
                    final_data = data.get("data")
            except json.JSONDecodeError:
                pass  # Ignore non-json data like end events

    # Assert that the command token is not present in the yielded text content
    assert "[web search:" not in yielded_content
    assert "Right?" in yielded_content  # Check other text is present

    assert final_data is not None
    assert "imageUrls" in final_data
    clickable_urls = final_data["imageUrls"]
    assert len(clickable_urls) == 1
    assert any(url['description'] == 'combi boiler' for url in clickable_urls)
    assert all("http://example.com/" in url['imageUrl'] for url in clickable_urls)

    # 3. Verify qdrant call
    mock_qdrant_instance.update_chat_search_on_history_change.assert_called_once()


@pytest.mark.asyncio
# To patchuje uzycia w metodzie __init__
@patch("src.agents.DiagnosticAgentStreaming.QdrantDAO", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.ImageAgentGemini", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.FileParser", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.RAGAgent", autospec=True)
@patch("langsmith.Client")
@patch("langsmith.run_helpers.get_run_tree_context")  # Mock Langsmith context
async def test_extraction_of_job_summary_form_stream_with_real_extractor(
        mock_get_run_tree_context,
        mock_langsmith_client,
        MockRAGAgent,
        MockFileParser,
        MockImageAgentGemini,
        MockQdrantDAO,
        mocker,  # Use pytest-mock fixture for easier patching
):
    """
    Test that when
     [[
        jobSummary = {
         jobHeadline: "e.g. No hot water",
         jobSubTitle: "e.g. Gas Boiler Maxol Micro Turbo Not Working",
         jobDetails: "e.g. Seeking a Gas Safe registered engineer ....",
         jobDate: " e.g. Urgent (within 48 hours)",
         jobTimeOfDay: "e.g. Tuesdays and Thursdays after 3 PM or weekends"
        }
        ]]
    is split across chunks using the REAL JobSummaryExtractor.
    """

    # 1. Mock JobService dependency for constructor
    mock_job_service = MagicMock(spec=JobService)
    mock_appliance_service = MagicMock()
    mock_property_service = MagicMock()
    # Mock the async method called within the agent
    mock_job_service.get_last_job_for_user_and_chat = AsyncMock(return_value=None)

    # 2. Use mocker fixture for patching methods on the instance later
    mocker.patch.object(DiagnosticAgentStreaming, "_process_single_attachment", new_callable=AsyncMock,
                        return_value="mock image description")

    # 3. Instantiate the Agent with mocked dependencies
    # NOTE: We are NOT mocking JobSummaryExtractor anymore - it will use the real one
    mock_qdrant_instance = MockQdrantDAO.return_value
    mock_image_agent_instance = MockImageAgentGemini.return_value
    mock_rag_agent_instance = MockRAGAgent.return_value

    mock_qdrant_instance.get_chat_history = AsyncMock(return_value=[])  # No history
    mock_qdrant_instance.update_chat_search_on_history_change = AsyncMock()

    # IMPORTANT: ImageAgent's search method
    # Needs to return a tuple like the real method for _handle_options_image_command
    mock_image_agent_instance.handle_search_request = AsyncMock(
        side_effect=lambda query, max_images: (
            query,  # Simulate returning the original query
            [ImageUrl(imageUrl=f"http://example.com/{query.replace(' ', '_')}.jpg", description=query)]
        )
    )

    mock_run_context = MagicMock()
    mock_run_context.metadata = {}
    mock_get_run_tree_context.return_value = mock_run_context

    # Instantiate the agent AFTER patching __init__ dependencies
    # The JobSummaryExtractor will be the real one now
    agent = DiagnosticAgentStreaming(
        job_service=mock_job_service,
        appliance_service=mock_appliance_service,
        property_service=mock_property_service,
        agent_prompt="TEST"
    )

    # 4. Define the simulated LLM Chunks with a complete job summary
    llm_chunks_content = [
        '| Weekdays after 6 PM |',
        '\n\n[',
        '[\njobSummary = {\n  jobHeadline: \"Leaking Kitchen Tap\",\n  jobSubTitle: \"Kitchen Mixer Tap Leaking from Base\",\n  jobDetails: \"Kitchen mixer tap (2-5 years old) is constantly leaking from the base even when turned off. This suggests worn internal components such as washers or O-rings that need replacement. The leak is persistent and requires professional attention.\",\n  jobDate: \"Urgent (within 24-48 hours)\",\n  jobTimeOfDay: \"Weekdays after 6 PM\"\n}\n]]'
    ]

    # Mock the LLM with tools
    mock_llm_with_tools = MagicMock()
    mock_llm_with_tools.astream = MagicMock(
        return_value=async_generator_from_list(llm_chunks_content)
    )

    # Mock the _create_llm_with_tools method to return our mock
    mocker.patch.object(agent, '_create_llm_with_tools', return_value=mock_llm_with_tools)

    output_chunks = []
    async for chunk in agent.process_next_message_stream(
            user_input=TEST_INPUT,
            user_id=TEST_USER_ID,
            chat_id=TEST_CHAT_ID,
            attachments=None
    ):
        print(f"chunk: {chunk}")
        output_chunks.append(chunk)

    # 1. Verify content of yielded chunks
    final_data = None
    yielded_content = ""
    for chunk_str in output_chunks:
        if chunk_str.startswith("data:"):
            try:
                data = json.loads(chunk_str.split("data: ", 1)[1])
                if data.get("type") == "content":
                    yielded_content += data.get("data", "")
                elif data.get("type") == "final_data":
                    final_data = data.get("data")
            except json.JSONDecodeError:
                pass  # Ignore non-json data like end events

    # 2. Assert that the command token is not present in the yielded text content
    # Static strings for assertions
    JOB_SUMMARY_TOKEN_THAT_SHOULD_BE_CUT = "jobSummary"
    EXPECTED_TEXT_CONTENT = "| Weekdays after 6 PM |"
    JOB_SUMMARY_KEY = "jobSummary"

    assert JOB_SUMMARY_TOKEN_THAT_SHOULD_BE_CUT not in yielded_content
    assert EXPECTED_TEXT_CONTENT in yielded_content  # Check other text is present

    assert final_data is not None
    assert JOB_SUMMARY_KEY in final_data

    # 3. Verify the actual job summary content was extracted correctly by the real extractor
    job_summary_data = final_data[JOB_SUMMARY_KEY]
    assert job_summary_data is not None
    assert job_summary_data["jobHeadline"] == "Leaking Kitchen Tap"
    assert job_summary_data["jobSubTitle"] == "Kitchen Mixer Tap Leaking from Base"
    assert "Kitchen mixer tap" in job_summary_data["jobDetails"]
    assert job_summary_data["jobDate"] == "Urgent (within 24-48 hours)"
    assert job_summary_data["jobTimeOfDay"] == "Weekdays after 6 PM"

    # 4. Verify qdrant call
    mock_qdrant_instance.update_chat_search_on_history_change.assert_called_once()


@pytest.mark.asyncio
# To patchuje uzycia w metodzie __init__
@patch("src.agents.DiagnosticAgentStreaming.QdrantDAO", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.ImageAgentGemini", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.FileParser", autospec=True)
@patch("src.agents.DiagnosticAgentStreaming.RAGAgent", autospec=True)
@patch("langsmith.Client")
@patch("langsmith.run_helpers.get_run_tree_context")  # Mock Langsmith context
async def test_extraction_of_job_summary_form_stream_problem2_with_real_extractor(
        mock_get_run_tree_context,
        mock_langsmith_client,
        MockRAGAgent,
        MockFileParser,
        MockImageAgentGemini,
        MockQdrantDAO,
        mocker,  # Use pytest-mock fixture for easier patching
):
    """
    Test the second job summary extraction scenario using the REAL JobSummaryExtractor.
    """

    # 1. Mock JobService dependency for constructor
    mock_job_service = MagicMock(spec=JobService)
    mock_appliance_service = MagicMock()
    mock_property_service = MagicMock()
    # Mock the async method called within the agent
    mock_job_service.get_last_job_for_user_and_chat = AsyncMock(return_value=None)

    # 2. Use mocker fixture for patching methods on the instance later
    mocker.patch.object(DiagnosticAgentStreaming, "_process_single_attachment", new_callable=AsyncMock,
                        return_value="mock image description")

    # 3. Instantiate the Agent with mocked dependencies
    # NOTE: We are NOT mocking JobSummaryExtractor anymore
    mock_qdrant_instance = MockQdrantDAO.return_value
    mock_image_agent_instance = MockImageAgentGemini.return_value
    mock_rag_agent_instance = MockRAGAgent.return_value

    mock_qdrant_instance.get_chat_history = AsyncMock(return_value=[])  # No history
    mock_qdrant_instance.update_chat_search_on_history_change = AsyncMock()

    # IMPORTANT: ImageAgent's search method
    mock_image_agent_instance.handle_search_request = AsyncMock(
        side_effect=lambda query, max_images: (
            query,  # Simulate returning the original query
            [ImageUrl(imageUrl=f"http://example.com/{query.replace(' ', '_')}.jpg", description=query)]
        )
    )

    mock_run_context = MagicMock()
    mock_run_context.metadata = {}
    mock_get_run_tree_context.return_value = mock_run_context

    # Instantiate the agent AFTER patching __init__ dependencies
    agent = DiagnosticAgentStreaming(
        job_service=mock_job_service,
        appliance_service=mock_appliance_service,
        property_service=mock_property_service,
        agent_prompt="TEST"
    )

    # 4. Define the simulated LLM Chunks
    llm_chunks_content = [
        ' **Job Time of Day**    | Week',
        'days after 6 PM |\n\n[',
        '[\njobSummary = {\n  jobHeadline: "Leaking Kitchen Tap",\n  jobSubTitle: "Kitchen Mixer Tap Leaking from Base",\n  jobDetails: "Kitchen mixer tap (2-5 years old) is constantly leaking from the base even when turned off. This suggests worn internal components such as washers or O-rings that need replacement. The leak is persistent and requires professional attention.",\n  jobDate: "Urgent (within 24-48 hours)",\n  jobTimeOfDay: "Weekdays after 6 PM"\n}\n]]',
    ]

    # Mock the LLM with tools
    mock_llm_with_tools = MagicMock()
    mock_llm_with_tools.astream = MagicMock(
        return_value=async_generator_from_list(llm_chunks_content)
    )

    # Mock the _create_llm_with_tools method to return our mock
    mocker.patch.object(agent, '_create_llm_with_tools', return_value=mock_llm_with_tools)

    output_chunks = []
    async for chunk in agent.process_next_message_stream(
            user_input=TEST_INPUT,
            user_id=TEST_USER_ID,
            chat_id=TEST_CHAT_ID,
            attachments=None
    ):
        print(f"chunk: {chunk}")
        output_chunks.append(chunk)

    # 1. Verify content of yielded chunks
    final_data = None
    yielded_content = ""
    for chunk_str in output_chunks:
        if chunk_str.startswith("data:"):
            try:
                data = json.loads(chunk_str.split("data: ", 1)[1])
                if data.get("type") == "content":
                    yielded_content += data.get("data", "")
                elif data.get("type") == "final_data":
                    final_data = data.get("data")
            except json.JSONDecodeError:
                pass  # Ignore non-json data like end events

    # 2. Assert that the command token is not present in the yielded text content
    JOB_SUMMARY_TOKEN_THAT_SHOULD_BE_CUT = "jobSummary"
    EXPECTED_TEXT_CONTENT = "Job Time of"
    JOB_SUMMARY_KEY = "jobSummary"

    assert JOB_SUMMARY_TOKEN_THAT_SHOULD_BE_CUT not in yielded_content
    assert EXPECTED_TEXT_CONTENT in yielded_content  # Check other text is present

    assert final_data is not None
    assert JOB_SUMMARY_KEY in final_data

    # 3. Verify the actual job summary content was extracted correctly by the real extractor
    job_summary_data = final_data[JOB_SUMMARY_KEY]
    assert job_summary_data is not None
    assert job_summary_data["jobHeadline"] == "Leaking Kitchen Tap"
    assert job_summary_data["jobSubTitle"] == "Kitchen Mixer Tap Leaking from Base"
    assert "Kitchen mixer tap" in job_summary_data["jobDetails"]
    assert job_summary_data["jobDate"] == "Urgent (within 24-48 hours)"
    assert job_summary_data["jobTimeOfDay"] == "Weekdays after 6 PM"

    # 4. Verify qdrant call
    mock_qdrant_instance.update_chat_search_on_history_change.assert_called_once()
    