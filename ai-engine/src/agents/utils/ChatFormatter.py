import logging

from src.schemas import ChatMessage


class ChatFormatter:

    def __init__(self, default_prompt_version=None):
        self.logger = logging.getLogger("uvicorn")

    @staticmethod
    def format_conversation_for_context(conversation_history, number_of_recent_msg_to_include: int = 80):
        """Format conversation history for the LLM in a way that encourages active responses"""
        if not conversation_history:
            return ""

        formatted_history = "Previous conversation:\n"
        for msg in conversation_history[-number_of_recent_msg_to_include:]:  # Only include last 10 messages for context
            formatted_history += f"Human: {msg[0]}\nAssistant's response: {msg[1]}\n\n"
        return formatted_history

    @staticmethod
    def format_conversation_from_list_of_messages(
        messages: list[ChatMessage], number_of_recent_msg_to_include: int | None = None
    ):
        """Format conversation history for the LLM"""
        convo = ""
        if number_of_recent_msg_to_include is not None:
            messages = messages[-number_of_recent_msg_to_include:]

        for message in messages:
            convo += f"{message.senderType}: {message.content}\n\n"

        return convo
