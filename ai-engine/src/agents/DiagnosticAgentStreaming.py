import asyncio
import json
import logging
import os
import re
import traceback
import uuid
from typing import List, Union

from langchain_anthropic import ChatAnthropic
# from langchain_together import Together
from langsmith import Client
import langsmith as ls
from langsmith.run_helpers import get_run_tree_context

from src.agents.ImageAgentGemini import ImageAgentGemini
from src.agents.JobSummaryExtractor import JobSummaryExtractor
from src.agents.StreamingChunkHandler import Command, Tool, StreamingChunkHandler
from src.agents.rag.RAGAgent import RAGAgent, create_rag_tool
from src.agents.utils.ChatFormatter import ChatFormatter
from src.dao.QdrantDAO import QdrantDAO
from src.schemas import ParseFileRequest, ImageUrl, ErrorDetail
from src.services.FileParser import FileParser
from src.services.appliances import ApplianceService
from src.services.jobs import JobService
from src.services.property import PropertyService

session_id = "agents-playground-session" + str(uuid.uuid4())
thread_id = uuid.uuid4()
conversation_id = uuid.uuid4()


class DiagnosticAgentStreaming:
    def __init__(self, job_service: JobService, appliance_service: ApplianceService,
                 property_service: PropertyService, agent_prompt):
        # Initialize LangSmith
        self._together_ai_api_key = os.environ["TOGETHER_AI_API_KEY"]
        self.anthropic_api_key = os.environ["ANTHROPIC_API_KEY"]
        self._langsmith_key = os.environ["LANGSMITH_API_KEY"]
        self.IS_LOCAL_TESTING = os.environ.get("IS_LOCAL_TESTING", "false")
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        os.environ["LANGSMITH_ENDPOINT"] = "https://eu.api.smith.langchain.com"

        # Check if the variable is not present in the environment
        if "LANGCHAIN_PROJECT" not in os.environ:
            # Set the environment variable
            os.environ["LANGCHAIN_PROJECT"] = "property_manager"

        self.logger = logging.getLogger("uvicorn")
        self._langsmith_client = Client()
        # Option 1: Using session_id

        self.job_summary_extractor = JobSummaryExtractor()
        self.job_summary = None

        self.file_parser_instance = FileParser()
        self.qdrant_dao = QdrantDAO()
        self.image_agent = ImageAgentGemini()
        self.rag_agent = RAGAgent(job_service, appliance_service, property_service, self.qdrant_dao)
        self._job_service: JobService = job_service

        self.diagnostic_llm = ChatAnthropic(model="claude-sonnet-4-20250514", max_tokens=20000)

        self._configure_commands_and_tools()
        self.system_prompt = agent_prompt + """

        IMPORTANT: You have access to a powerful context search tool called 'search_property_context'.
        
        **CONTEXT MANAGEMENT:**
        - A summary of relevant property information might be provided under the heading "PREVIOUSLY FETCHED CONTEXT". Always refer to this information first.
        - **DO NOT** use the `search_property_context` tool if the user's question can be answered using this provided context.
        - **ONLY** use the `search_property_context` tool if:
        1. No context has been provided yet.
        2. The user's question shifts to a **completely different topic** not covered by the existing context (e.g., switching from a boiler repair to a question about selling the property).
        3. When you get message in prompt "PREVIOUSLY FETCHED CONTEXT: TOOLS CACHE EXPIRED" please call `search_property_context` to refresh the context. (Context expires after 2 hours)

        Before using this tool please notify the user that you will check the property records and search for relevant data.

        ONLY use this tool when you need specific information about:
        - User's appliances (what appliances they have, brands, models, manuals)
        - Past repairs or maintenance history (previous jobs, similar issues)
        - Property details (number of rooms, property type, size, etc.)
        - Property documents (gas certificates, bills, insurance docs, house surveys, inventory, appliance manuals)
        - Previous conversations about similar issues

        DO NOT use the search_property_context tool for:
        - General greetings or small talk
        - Simple questions you can answer without context
        - When you already have sufficient information to help

        Examples of when TO use the search_property_context tool:
        - User asks "What appliances do I have?"
        - User mentions an issue with heating/plumbing/electrical and you need appliance details
        - User asks "When was my boiler last serviced?"
        - User asks "How many rooms do I have?"
        - You need to check if they've had similar issues before
        
        Additional examples of user questions when TO use the search_property_context tool:
            "I have not hot water"
            “The washing machine is leaking again”
            “The smoke alarm won’t stop beeping.”
            “I need to repaint the hallway—do you remember what colour we used last time?”
            “It’s probably time for the annual boiler service—can you check?”
            "Can I get a smart meter?"
            "What's my property's Energy Performance Certificate (EPC) rating and how can I improve it?"
            "Are there any restrictive covenants on my property I should know about?"
            "What's my insurance excess and does it vary by claim type?"
            "Do I need planning permission for a loft conversion, extension, or basement dig?"
            "Do I need Building Control approval for my renovation work?"
            "Who's responsible for repairs in my leasehold flat - me or the freeholder?"
            "There's a leak from the flat above - whose insurance pays and who arranges repairs?"
            "How much would it cost for me to install an A/C?"
            "How much would it cost to refurbish my kitchen?"

        Examples of when NOT to use the search_property_context tool:
        - User says "Hello" or "How are you?"
        - User asks "What can you help me with?"
        - You can provide a helpful response based on the user's description alone

        Call the tool early when relevant, but avoid unnecessary calls.
        """

    def _configure_commands_and_tools(self):
        """Defines the command patterns, tool handlers, and managers."""
        self.stateless_commands_config = [
            Command(re.compile(r"\[\s*web\s*search\s*:\s*(.*?)\s*\]", re.DOTALL),
                    lambda q: asyncio.create_task(self.image_agent.handle_search_request(q, max_images=1))),
            Command(re.compile(r"\[\s*OptionsImage\s*:\s*(.*?)\s*\]", re.DOTALL),
                    lambda o: asyncio.create_task(self._handle_options_image_command(o))),
        ]
        self.job_summary_pattern = re.compile(r"\[\[([\s\S]*?)jobSummary\s*=\s*\{([\s\S]*?)\}([\s\S]*?)\]\]")

        rag_tool = Tool(name="search_property_context", handler=self._handle_rag_tool)
        self.tools = [rag_tool]

    @ls.traceable(name="RAG_Agent_Tool_Handler", run_type="chain")
    async def _handle_rag_tool(self, user_id: int, chat_id: int, last_messages: list, first_message: str) -> str:
        """Handler for the 'search_property_context' tool."""
        self.logger.info(f"Executing RAG tool for user_id={user_id} with context: {last_messages[-5:]}")
        rag_context = await self.rag_agent.search_and_summarize(user_id, last_messages[-5:], first_message)

        self.logger.info(f"Updating tools cache for user_id={user_id} with context: {last_messages[-5:]}")
        await self.qdrant_dao.upsert_tools_cache(str(user_id), str(chat_id),
                                                 "'search_property_context' latest tool invocation data:\n\n"+rag_context.summary)

        run_tree = get_run_tree_context()
        if run_tree:
            run_tree.metadata.update({
                "rag_context_length": len(rag_context.summary),
                "appliances_count": len(rag_context.appliances),
                "jobs_count": len(rag_context.jobs),
                "documents_count": len(rag_context.documents)
            })

        self.logger.info(f"RAG Agent returned: {rag_context.summary}")
        return f"RELEVANT CONTEXT FROM PROPERTY SEARCH:\n{rag_context.summary}"

    @staticmethod
    def map_to_image_urls(query, image_data: List[str]) -> List[ImageUrl]:
        return [ImageUrl(imageUrl=url, description=query) for url in image_data]

    @staticmethod
    def format_image_descriptions(image_data: list[str]) -> str:
        """
        Converts a list of image descriptions into a formatted string.

        Args:
            image_data: List of image description strings

        Returns:
            str: Formatted string with numbered image descriptions

        Example:
            >>> descriptions = ["A cat sleeping", "A dog running"]
            >>> format_image_descriptions(descriptions)
            'IMAGE 1 DESCRIPTION:\nA cat sleeping\n\nIMAGE 2 DESCRIPTION:\nA dog running'
        """
        if not image_data:
            return ""

        formatted_descriptions = []
        for index, description in enumerate(image_data, 1):
            formatted_descriptions.append(f"IMAGE {index} DESCRIPTION:\n{description}")

        return "\n\n".join(formatted_descriptions)

    def _create_llm_with_tools(self, user_id: int):
        rag_tool = create_rag_tool(self.rag_agent, user_id)
        return self.diagnostic_llm.bind_tools([rag_tool], tool_choice="auto")


    @ls.traceable(
        run_type="llm",
        name="Process Query Stream Call Decorator",
        tags=["process_user_msg_stream"],
        metadata={"session_id": session_id, "thread_id": thread_id, "conversation_id": conversation_id},
    )
    async def process_query_stream(self, user_input, user_id, chat_id, image_data: list[str]):
        """Process user query with optional image data, streaming the response and handling image commands."""

        self.logger.info(f"process_query_stream(): user_id={user_id}, chat_id={chat_id} user_input={user_input}")

        job_id = None
        if not self.IS_LOCAL_TESTING:
            job = await self._job_service.get_last_job_for_user_and_chat(user_id, chat_id)
            if job:
                job_id = job.id

        run_context = get_run_tree_context()
        if run_context:
            run_context.metadata["session_id"] = f"chat_id: {chat_id}"
            run_context.metadata["user_id"] = f"user_id: {user_id}"


        def handle_job_summary(match: str) -> None:
            _, summary = self.job_summary_extractor.extract_job_summary(match)
            if summary:
                handler.job_summary = summary

        commands = self.stateless_commands_config + [Command(self.job_summary_pattern, handle_job_summary)]
        handler = StreamingChunkHandler(commands, self.tools)  # Pass self.tools to the handler

        image_context = self.format_image_descriptions(image_data)
        conversation_history = await self.qdrant_dao.get_chat_history(user_id, chat_id)
        tools_cache = await self.qdrant_dao.get_tools_cache(user_id, chat_id)
        formatted_history = ChatFormatter.format_conversation_for_context(conversation_history)
        last_messages = [msg for pair in conversation_history[-5:] for msg in pair] + [user_input]
        first_message = conversation_history[0][0] if conversation_history else "No message available"

        self.logger.info(f"\n\nPREVIOUSLY FETCHED CONTEXT: {tools_cache}\n\n")

        full_prompt = (f"{self.system_prompt}\n\n{formatted_history}"
                       f"{image_context and f'\nAttached images analysis: {image_context}\n'}"
                       f"\n\nPREVIOUSLY FETCHED CONTEXT: {tools_cache}\n\n"
                       f"Current user message: {user_input}\n\nRespond directly...")

        llm_with_tools = self._create_llm_with_tools(user_id)

        try:
            # --- PHASE 1: Await LLM response, checking for tool calls ---
            # Now, StreamingChunkHandler processes the raw chunk and accumulates tool calls
            # The loop will still break if tool calls are detected to trigger immediate execution.
            async for chunk in llm_with_tools.astream(full_prompt):
                self.logger.info(f"llm_chunk={chunk}")
                async for content_event in handler.process_llm_chunk(chunk):
                    yield content_event

                # Check if this chunk contained tool calls
                if hasattr(chunk, 'tool_calls') and chunk.tool_calls:
                    break  # Exit loop to execute tools

            # --- PHASE 2: Execute tools if any, then stream final response ---
            # Retrieve accumulated tool calls from the handler
            tool_calls_to_execute = handler.get_detected_tool_calls()

            if tool_calls_to_execute:
                self.logger.info("Executing tools and generating enhanced response.")
                # Now call execute_tool_calls directly on the handler instance
                enhanced_context = await handler.execute_tool_calls(
                    tool_calls_to_execute, user_id=user_id, chat_id=chat_id, last_messages=last_messages, first_message=first_message
                )
                enhanced_prompt = f"{full_prompt}\n\n{enhanced_context}\n\nUse this new context to provide a more informed response."

                # Stream the final response using the enhanced prompt
                async for enhanced_chunk in self.diagnostic_llm.astream(enhanced_prompt):
                    self.logger.info(f"llm_enh_chunk={enhanced_chunk}")
                    async for content_event in handler.process_llm_chunk(enhanced_chunk):  # Use new handler method
                        yield content_event

            # --- Finalization ---
            async for final_event in handler.finalize():
                yield final_event

            await self.qdrant_dao.update_chat_search_on_history_change(user_id, chat_id, conversation_history + [
                (user_input, handler.full_response_for_history)])

            job_summary = handler.job_summary
            if job_summary and job_id:
                job_summary = job_summary.model_copy(update={"jobId": job_id})

            final_data = {
                "jobSummary": job_summary.model_dump(exclude_none=True) if job_summary else None,
                "imageUrls": [img.model_dump(exclude_none=True) for img in handler.accumulated_image_urls],
                "imageClickableUrls": [img.model_dump(exclude_none=True) for img in handler.accumulated_clickable_urls],
            }
            yield f"data: {json.dumps({'type': 'final_data', 'data': {k: v for k, v in final_data.items() if v}})}\n\n"
            yield f"event: end\ndata: Stream finished\n\n"

        except Exception as e:
            self.logger.exception(f"Error during streaming query: {e}\nStacktrace: {traceback.format_exc()}")
            error_str = str(e).lower()
            error_code, msg = ("INTERNAL_ERROR", "An unexpected error occurred during streaming")
            if any(s in error_str for s in ["529", "500", "429", "overload", "ratelimiterror", "resourceexhausted"]):
                error_code, msg = ("LLM_OVERLOADED", "The LLM service is currently overloaded or rate limited")
            error_payload = ErrorDetail(code=error_code, message=msg, details={"original_error": str(e)}).model_dump()
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield f"event: end\ndata: Stream finished with error\n\n"

    async def _handle_options_image_command(self, options_string: str) -> Union[List[ImageUrl], str]:
        """
        Handles the [OptionsImage:...] command asynchronously.
        Returns List[ImageUrl] on success (images found for all options).
        Returns str "[Options: original_query]" if any option fails to find an image.
        """
        options = [opt.strip() for opt in options_string.split(";") if opt.strip()]
        self.logger.info(f"Handling OptionsImage command async with options: {options}")

        if not options:
            return []  # Success case technically, no options provided -> empty list

        tasks = []
        for option in options:
            tasks.append(asyncio.create_task(self.image_agent.handle_search_request(option, max_images=1)))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        was_one_empty = False
        temp_urls = []
        for i, result in enumerate(results):
            option = options[i]
            if isinstance(result, Exception):
                self.logger.exception(f"Failed search for option '{option}': {result}")
                was_one_empty = True
                break
            elif isinstance(result, tuple) and len(result) == 2:
                _, urls = result
                if isinstance(urls, list) and len(urls) > 0:
                    urls[0].description = option
                    temp_urls.append(urls[0])
                else:
                    self.logger.warning(f"No image found for option: '{option}'")
                    was_one_empty = True
                    break
            else:
                self.logger.warning(f"Unexpected result type for option '{option}': {type(result)}")
                was_one_empty = True
                break

        if not was_one_empty:
            self.logger.info(f"Successfully gathered {len(temp_urls)} clickable images for options.")
            return temp_urls  # List[ImageUrl]
        else:
            self.logger.warning("Failed to get images for all options, returning fallback string.")
            fallback_string = f"[Options: {options_string}]"
            return fallback_string  # str

    async def process_next_message_stream(self, user_input, user_id, chat_id,
                                          attachments: list[ParseFileRequest] = None):
        image_data: list[str] = []
        appliance_prompt = (
            "Analyze this image and describe any relevant details about the appliance or issue shown. "
            "Try to identify appliance type and make and model if possible"
        )
        try:
            if attachments:
                async with asyncio.timeout(120):
                    attachment_tasks = []
                    for file in attachments:
                        attachment_tasks.append(self._process_single_attachment(file, appliance_prompt))

                    results = await asyncio.gather(*attachment_tasks, return_exceptions=True)

                    for result in results:
                        if isinstance(result, Exception):
                            self.logger.exception(f"Error processing attachment: {result}")
                            error_payload = ErrorDetail(
                                code="ATTACHMENT_ERROR",
                                message="Failed to process an attached file.",
                                #details={"original_error": str(result)} # hidden not to show to the user
                                details={"original_error": "truncated"}
                            ).model_dump()
                            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
                        elif isinstance(result, str):
                            image_data.append(result)

            async for chunk in self.process_query_stream(user_input, user_id, chat_id, image_data):
                yield chunk

        except asyncio.TimeoutError:
            self.logger.exception(f"Timeout processing attachments for user_id={user_id}, chat_id={chat_id}")
            error_payload = ErrorDetail(
                code="ATTACHMENT_TIMEOUT",
                message="Processing attached files took too long.",
                details={}
            ).model_dump()
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield f"event: end\ndata: Stream finished with attachment error\n\n"
        except Exception as e:
            self.logger.exception(
                f"Error processing attachments or initiating stream for user_id={user_id}, chat_id={chat_id}: {str(e)}\nStacktrace: {traceback.format_exc()}")
            error_payload = ErrorDetail(
                code="STREAM_INIT_ERROR",
                message="Failed to process attachments or start response stream.",
                #details={"original_error": str(e)}
                details = {"original_error": str(e)}
            ).model_dump()
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield f"event: end\ndata: Stream finished with init error\n\n"

    async def _process_single_attachment(self, file: ParseFileRequest, prompt: str) -> str:
        """Helper to process a single attachment asynchronously."""
        if file.base64:
            return await self.file_parser_instance.process_file_for_appliance_analysis_base64(
                file.base64, prompt
            )
        else:
            return await self.file_parser_instance.process_file_for_appliance_analysis(
                file.userId,
                file.documentId,
                file.documentS3Key,
                file.documentS3Bucket,
                file.type,
                prompt,
            )
