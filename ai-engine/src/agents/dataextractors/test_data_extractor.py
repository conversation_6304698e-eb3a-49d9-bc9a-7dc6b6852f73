import asyncio
import os
import json
import logging
import pytest
from pathlib import Path
from typing import Any
from unittest.mock import AsyncMock, MagicMock, patch

from src.agents.dataextractors.DataExtractorService import DataExtractorService
from src.integrations.backend import BackendAPI, UpdateDocumentStatusRequest, CreateApplianceRequest, \
    UpdateApplianceRequest, ApplianceData, UpdatePropertyDetailsRequest
from src.services.appliances import ApplianceService
from src.schemas import RelevantDocumentClassification, NotRelevantDocumentClassification
from src.services.property import PropertyService

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_data_extractor")


class TestDataExtractorService:

    @pytest.fixture(scope="session", autouse=True)
    def set_required_env_vars_for_imports(self):
        variables_to_set = {
            "ANTHROPIC_API_KEY": "dummy_anthropic_key_for_import",
            "GEMINI_API_KEY": "dummy_gemini_key_for_import",
            "GOOGLE_API_KEY": "dummy_gemini_key_for_import",
            "PSE_API_KEY": "dummy_pse_key_for_import",
            "PSE_CX": "dummy_pse_cx_for_import",
            "IS_AWS": "true",
            "LANGSMITH_API_KEY": "dummy_langsmith_key_for_import",
            "TOGETHER_AI_API_KEY": "dummy_together_key_for_import",
            "AI_ENGINE_API_KEY": "dummy_ai_engine_key_for_import",
            "OPEN_AI_API_KEY": "dummy_ai_engine_key_for_import",
            "QDRANT_DB_URL": "dummy_ai_engine_key_for_import",
            "QDRANT_API_KEY": "dummy_ai_engine_key_for_import",
            "QDRANT_COLLECTION_NAME": "dummy_collection"
        }

        original_values = {}
        for key, value in variables_to_set.items():
            original_values[key] = os.environ.get(key)
            os.environ[key] = value
            # print(f"  Set {key}") # Optional: reduce verbosity

        print("Environment variables set for import.")

    @pytest.fixture
    def mock_backend_api(self):
        """Create mock backend API"""
        mock = MagicMock(spec=BackendAPI)
        mock.update_document_status = AsyncMock()
        mock.create_appliance = AsyncMock()
        mock.update_appliance = AsyncMock()
        mock.update_property_details = AsyncMock()
        return mock

    @pytest.fixture
    def mock_appliance_service(self):
        """Create mock appliance service"""
        mock = MagicMock(spec=ApplianceService)
        mock.get_appliance_by_type_or_brand_or_model = AsyncMock()
        return mock

    @pytest.fixture
    def mock_property_service(self):
        """Create mock property service"""
        mock = MagicMock(spec=PropertyService)
        mock.get_user_primary_property = AsyncMock()
        return mock

    @pytest.fixture
    def service(self, mock_backend_api, mock_appliance_service, mock_property_service):
        """Create DataExtractorService instance with mocked dependencies"""
        logger.info("Creating DataExtractorService instance")
        service = DataExtractorService(
            backend_api=mock_backend_api,
            appliance_service=mock_appliance_service,
            property_service=mock_property_service
        )

        # Ensure the service logger is configured properly
        service.logger.setLevel(logging.DEBUG)
        logger.info("DataExtractorService instance created successfully")
        return service

    @pytest.mark.asyncio
    async def test_process_file_relevant_document_with_two_appliances_inserts(self, service, mock_backend_api,
                                                                        mock_appliance_service):
        """Test processing a relevant document that results in 2 appliance insertion"""
        logger.info("Testing process_file with relevant document - appliance insert")

        test_file = Path("test-pdf/gas_certificate.pdf")
        document_id = 123
        user_id = 456

        # Mock the document labeling to return relevant classification
        relevant_classification = RelevantDocumentClassification(
            category="Bills",
            label="Gas Certificates",
            isNewLabel=False
        )

        # Mock the extraction data
        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Appliances"],
            "extracted_data": {
                "Appliances": {
                    "appliances": [
                        {
                            "appliance_type": "Boiler",
                            "brand": "Maxol",
                            "model": "Micro turbo",
                            "serial_number": "12345",
                            "warranty_details": "2 years",
                            "other_details": "Kitchen location"
                        },
                        {
                            "appliance_type": "Washing Machine",
                            "brand": "Samsung",
                            "model": "LL2000",
                            "serial_number": "12345",
                            "warranty_details": "2 years",
                            "other_details": "Kitchen location"
                        }
                    ]
                }
            }
        }

        # Mock appliances manager response for insert
        mock_appliance_command = {"command": "insert"}

        # Mock existing appliances (empty list for insert case)
        mock_existing_appliances = []

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Gas Certificate"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.appliances_manager, 'insert_or_update_appliance',
                             return_value=mock_appliance_command):
            # Setup mock responses
            mock_appliance_service.get_appliance_by_type_or_brand_or_model.return_value = mock_existing_appliances
            mock_backend_api.create_appliance.return_value = {"id": 789}

            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True
            assert content == "Mock content"

            # Verify status updates
            assert mock_backend_api.update_document_status.call_count == 3

            # Verify first status update (processing)
            first_call = mock_backend_api.update_document_status.call_args_list[0]
            assert first_call[0][0] == document_id
            assert first_call[0][1].status == "processing"

            # Verify second status update (completed)
            second_call = mock_backend_api.update_document_status.call_args_list[1]
            assert second_call[0][0] == document_id
            assert second_call[0][1].status == "processing"
            assert second_call[0][1].category == "billsAndSubscriptions"
            assert second_call[0][1].label == "Gas Certificates"

            third_call = mock_backend_api.update_document_status.call_args_list[2]
            assert third_call[0][1].status == "processingCompleted"

            # Verify appliance creation
            assert mock_backend_api.create_appliance.call_count == 2

            # Check first appliance
            first_appliance_call = mock_backend_api.create_appliance.call_args_list[0]
            first_appliance_request = first_appliance_call[0][0]  # Get the actual request object
            assert isinstance(first_appliance_request, CreateApplianceRequest)
            assert first_appliance_request.appliance.brand == "Maxol"
            assert first_appliance_request.appliance.type == "Boiler"
            assert first_appliance_request.srcType == "document"
            assert first_appliance_request.srcId == document_id

            # Check second appliance
            second_appliance_call = mock_backend_api.create_appliance.call_args_list[1]
            second_appliance_request = second_appliance_call[0][0]  # Get the actual request object
            print(f"Second appliance insert: {second_appliance_request}")
            assert isinstance(second_appliance_request, CreateApplianceRequest)
            assert second_appliance_request.appliance.brand == "Samsung"
            assert second_appliance_request.appliance.type == "Washing Machine"
            assert second_appliance_request.srcType == "document"
            assert second_appliance_request.srcId == document_id

        logger.info("PASS Test passed: process_file with relevant document - appliance insert")

    @pytest.mark.asyncio
    async def test_process_file_relevant_document_with_appliance_insert(self, service, mock_backend_api,
                                                                        mock_appliance_service):
        """Test processing a relevant document that results in appliance insertion"""
        logger.info("Testing process_file with relevant document - appliance insert")

        test_file = Path("test-pdf/gas_certificate.pdf")
        document_id = 123
        user_id = 456

        # Mock the document labeling to return relevant classification
        relevant_classification = RelevantDocumentClassification(
            category="Bills",
            label="Gas Certificates",
            isNewLabel=False
        )

        # Mock the extraction data
        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Appliances"],
            "extracted_data": {
                "Appliances": {
                    "appliances": [
                        {
                            "appliance_type": "Boiler",
                            "brand": "Maxol",
                            "model": "Micro turbo",
                            "serial_number": "12345",
                            "warranty_details": "2 years",
                            "other_details": "Kitchen location"
                        }
                    ]
                }
            }
        }

        # Mock appliances manager response for insert
        mock_appliance_command = {"command": "insert"}

        # Mock existing appliances (empty list for insert case)
        mock_existing_appliances = []

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Gas Certificate"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.appliances_manager, 'insert_or_update_appliance',
                             return_value=mock_appliance_command):
            # Setup mock responses
            mock_appliance_service.get_appliance_by_type_or_brand_or_model.return_value = mock_existing_appliances
            mock_backend_api.create_appliance.return_value = {"id": 789}

            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True
            assert content == "Mock content"

            # Verify status updates
            assert mock_backend_api.update_document_status.call_count == 3

            # Verify first status update (processing)
            first_call = mock_backend_api.update_document_status.call_args_list[0]
            assert first_call[0][0] == document_id
            assert first_call[0][1].status == "processing"

            # Verify second status update (completed)
            second_call = mock_backend_api.update_document_status.call_args_list[1]
            assert second_call[0][0] == document_id
            assert second_call[0][1].status == "processing"
            assert second_call[0][1].category == "billsAndSubscriptions"
            assert second_call[0][1].label == "Gas Certificates"

            third_call = mock_backend_api.update_document_status.call_args_list[2]
            assert third_call[0][1].status == "processingCompleted"

            # Verify appliance creation
            mock_backend_api.create_appliance.assert_called_once()
            create_call = mock_backend_api.create_appliance.call_args[0][0]
            assert isinstance(create_call, CreateApplianceRequest)
            assert create_call.srcType == "document"
            assert create_call.srcId == document_id

        logger.info("PASS Test passed: process_file with relevant document - appliance insert")

    @pytest.mark.asyncio
    async def test_process_file_relevant_document_with_appliance_update(self, service, mock_backend_api,
                                                                        mock_appliance_service):
        """Test processing a relevant document that results in appliance update"""
        logger.info("=== Starting test_process_file_relevant_document_with_appliance_update ===")

        test_file = Path("test-pdf/gas_certificate.pdf")
        document_id = 123
        user_id = 456

        # Mock existing appliance
        existing_appliance = MagicMock()
        existing_appliance.id = 999
        existing_appliance.type = "Boiler"
        existing_appliance.brand = "Maxol"
        existing_appliance.model = None
        existing_appliance.serialNumber = None
        existing_appliance.warranty = None
        existing_appliance.otherDetails = None
        existing_appliance.propertyId = 1

        relevant_classification = RelevantDocumentClassification(
            category="Bills",
            label="Gas Certificates",
            isNewLabel=False
        )

        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Appliances"],
            "extracted_data": {
                "Appliances": {
                    "appliances": [
                        {
                            "appliance_type": "Boiler",
                            "brand": "Maxol",
                            "model": "Micro turbo",
                            "serial_number": "12345",
                            "warranty_details": "2 years",
                            "other_details": "Kitchen location"
                        }
                    ]
                }
            }
        }

        # Mock appliances manager response for update
        mock_appliance_command = {
            "command": "update",
            "id": 999,
            "fields": ["model", "serialNumber", "warranty", "otherDetails"]
        }

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Gas Certificate"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.appliances_manager, 'insert_or_update_appliance',
                             return_value=mock_appliance_command), \
                patch.object(service, '_create_partial_appliance_data') as mock_create_partial:
            # Setup mock responses
            mock_appliance_service.get_appliance_by_type_or_brand_or_model.return_value = [existing_appliance]

            # FIX: Create ApplianceData with userId field
            mock_partial_appliance = ApplianceData(
                type="Boiler",
                brand="Maxol",
                model="Micro turbo",
                serialNumber="12345",
                userId=user_id  # Add the missing userId field
            )
            mock_create_partial.return_value = mock_partial_appliance

            logger.info("Starting process_file execution")

            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Print debug info
            logger.info(
                f"Test execution completed - Result: {result}, Content length: {len(content) if content else 0}")
            logger.info(f"Backend update_appliance call count: {mock_backend_api.update_appliance.call_count}")
            logger.info(f"Backend create_appliance call count: {mock_backend_api.create_appliance.call_count}")

            # Assertions
            assert result is True
            assert content == "Mock content"

            update_call_args = mock_backend_api.update_appliance.call_args
            assert update_call_args[0][0] == 999  # appliance_id
            assert isinstance(update_call_args[0][1], UpdateApplianceRequest)
            assert update_call_args[0][1].srcType == "document"
            assert update_call_args[0][1].srcId == document_id

            logger.info("PASS Test completed successfully")

    @pytest.mark.asyncio
    async def test_process_file_relevant_document_appliance_do_nothing(self, service, mock_backend_api,
                                                                       mock_appliance_service):
        """Test processing a relevant document where appliance already exists with same data"""
        logger.info("Testing process_file with relevant document - appliance do nothing")

        test_file = Path("test-pdf/gas_certificate.pdf")
        document_id = 123
        user_id = 456

        relevant_classification = RelevantDocumentClassification(
            category="Bills",
            label="Gas Certificates",
            isNewLabel=False
        )

        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Appliances"],
            "extracted_data": {
                "Appliances": {
                    "appliances": [
                        {
                            "appliance_type": "Boiler",
                            "brand": "Maxol",
                            "model": "Micro turbo",
                            "serial_number": "12345",
                            "warranty_details": "2 years",
                            "other_details": "Kitchen location"
                        }
                    ]
                }
            }
        }

        # Mock appliances manager response for do_nothing
        mock_appliance_command = {"command": "do_nothing"}

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Gas Certificate"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.appliances_manager, 'insert_or_update_appliance',
                             return_value=mock_appliance_command):
            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True
            assert content == "Mock content"

            # Verify no appliance operations were called
            mock_backend_api.create_appliance.assert_not_called()
            mock_backend_api.update_appliance.assert_not_called()

        logger.info("PASS Test passed: process_file with relevant document - appliance do nothing")

    @pytest.mark.asyncio
    async def test_process_file_irrelevant_document(self, service, mock_backend_api, mock_appliance_service):
        """Test processing an irrelevant document"""
        logger.info("Testing process_file with irrelevant document")

        test_file = Path("test-pdf/irrelevant_document.pdf")
        document_id = 123
        user_id = 456

        # Mock irrelevant classification
        irrelevant_classification = NotRelevantDocumentClassification()

        with patch.object(service.document_labeling_agent, 'process_file', return_value=irrelevant_classification):
            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is False
            assert content is None

            # Verify status updates
            assert mock_backend_api.update_document_status.call_count == 2

            # Verify first status update (processing)
            first_call = mock_backend_api.update_document_status.call_args_list[0]
            assert first_call[0][1].status == "processing"

            # Verify second status update (irrelevant)
            second_call = mock_backend_api.update_document_status.call_args_list[1]
            assert second_call[0][1].status == "irrelevant"

        logger.info("PASS Test passed: process_file with irrelevant document")

    @pytest.mark.asyncio
    async def test_process_file_relevant_document_with_property_update(self, service, mock_backend_api,
                                                                       mock_appliance_service, mock_property_service):
        """Test processing a relevant document that results in property update"""
        logger.info("Testing process_file with relevant document - property update")

        test_file = Path("test-pdf/property_document.pdf")
        document_id = 123
        user_id = 456

        # Mock existing property
        existing_property = MagicMock()
        existing_property.id = 999
        existing_property.type = None
        existing_property.tenureType = None
        existing_property.sizeInSqft = None
        existing_property.numberOfBedrooms = None
        existing_property.numberOfBathrooms = None
        existing_property.condition = None
        existing_property.architecturalType = None
        existing_property.address = None
        existing_property.hasBalconyTerrace = None
        existing_property.balconyTerraceDetails = None
        existing_property.hasGarden = None
        existing_property.gardenDetails = None
        existing_property.hasSwimmingPool = None
        existing_property.swimmingPoolDetails = None
        existing_property.onFloorLevel = None
        existing_property.numberOfFloors = None
        existing_property.lastSoldPriceInGbp = None
        existing_property.yearsOfOwnership = None
        existing_property.valuationInGbp = None
        existing_property.conservationStatus = None
        existing_property.typeOfLock = None
        existing_property.typeOfConstruction = None
        existing_property.proportionOfFlatRoof = None
        existing_property.epcRating = None

        relevant_classification = RelevantDocumentClassification(
            category="Property",
            label="Property Details",
            isNewLabel=False
        )

        # Updated mock extracted data with address object
        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Property"],
            "extracted_data": {
                "Property": {
                    "address": {
                        "streetLine1": "123 Main Street",
                        "streetLine2": "Flat 4B",
                        "townOrCity": "London",
                        "postcode": "SW1A 1AA",
                        "houseAccess": "Main entrance",
                        "parkingInstructions": "Visitor parking available"
                    },
                    "type": "flat",
                    "tenureType": "leasehold",
                    "sizeInSqft": 850,
                    "numberOfBedrooms": 2,
                    "numberOfBathrooms": 1,
                    "condition": "good",
                    "architecturalType": "Victorian"
                }
            }
        }

        # Mock property manager response for update with address object
        mock_property_command = {
            "command": "update",
            "id": 999,
            "fields": ["address", "type", "tenureType", "sizeInSqft", "numberOfBedrooms", "numberOfBathrooms",
                       "condition", "architecturalType"],
            "update_data": {
                "address": {
                    "streetLine1": "123 Main Street",
                    "streetLine2": "Flat 4B",
                    "townOrCity": "London",
                    "postcode": "SW1A 1AA",
                    "houseAccess": "Main entrance",
                    "parkingInstructions": "Visitor parking available"
                },
                "type": "flat",
                "tenureType": "leasehold",
                "sizeInSqft": 850,
                "numberOfBedrooms": 2,
                "numberOfBathrooms": 1,
                "condition": "good",
                "architecturalType": "Victorian",
                "userId": user_id,  # Added userId
                "propertyId": 999  # Added propertyId
            }
        }

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Property Document"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.property_details_manager, 'update_property_details',
                             return_value=mock_property_command):
            # Setup mock responses
            mock_property_service.get_user_primary_property.return_value = existing_property

            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True
            assert content == "Mock content"

            # Verify property update was called
            update_call_args = mock_backend_api.update_property_details.call_args
            assert update_call_args[0][0] == 999  # property_id
            assert isinstance(update_call_args[0][1], UpdatePropertyDetailsRequest)
            assert update_call_args[0][1].srcType == "document"
            assert update_call_args[0][1].srcId == document_id

            # Verify address object structure in the property details
            property_details = update_call_args[0][1].propertyDetails
            assert hasattr(property_details, 'address')
            if property_details.address:
                assert property_details.address.streetLine1 == "123 Main Street"
                assert property_details.address.townOrCity == "London"
                assert property_details.address.postcode == "SW1A 1AA"

        logger.info("PASS Test passed: process_file with relevant document - property update")

    @pytest.mark.asyncio
    async def test_process_file_relevant_document_property_do_nothing(self, service, mock_backend_api,
                                                                      mock_appliance_service, mock_property_service):
        """Test processing a relevant document where property already has same data"""
        logger.info("Testing process_file with relevant document - property do nothing")

        test_file = Path("test-pdf/property_document.pdf")
        document_id = 123
        user_id = 456

        # Mock existing property with complete data
        existing_property = MagicMock()
        existing_property.id = 999

        relevant_classification = RelevantDocumentClassification(
            category="Property",
            label="Property Details",
            isNewLabel=False
        )

        # Updated mock extracted data with address object
        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Property"],
            "extracted_data": {
                "Property": {
                    "address": {
                        "streetLine1": "123 Main Street",
                        "townOrCity": "London",
                        "postcode": "SW1A 1AA"
                    },
                    "type": "flat",
                    "numberOfBedrooms": 2
                }
            }
        }

        # Mock property manager response for do_nothing
        mock_property_command = {"command": "do_nothing"}

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Property Document"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.property_details_manager, 'update_property_details',
                             return_value=mock_property_command):
            # Setup mock responses
            mock_property_service.get_user_primary_property.return_value = existing_property

            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True
            assert content == "Mock content"

            # Verify no property update was called
            mock_backend_api.update_property_details.assert_not_called()

        logger.info("PASS Test passed: process_file with relevant document - property do nothing")

    @pytest.mark.asyncio
    async def test_process_file_property_with_concatenation(self, service, mock_backend_api,
                                                            mock_appliance_service, mock_property_service):
        """Test processing property data where string fields need concatenation"""
        logger.info("Testing process_file with property data concatenation")

        test_file = Path("test-pdf/property_document.pdf")
        document_id = 123
        user_id = 456

        # Mock existing property with some existing details
        existing_property = MagicMock()
        existing_property.id = 999

        relevant_classification = RelevantDocumentClassification(
            category="Property",
            label="Property Details",
            isNewLabel=False
        )

        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Property"],
            "extracted_data": {
                "Property": {
                    "gardenDetails": "Large back garden with patio",
                    "architecturalType": "Victorian terrace"
                }
            }
        }

        # Mock property manager response with concatenated values
        mock_property_command = {
            "command": "update",
            "id": 999,
            "fields": ["gardenDetails", "architecturalType"],
            "update_data": {
                "gardenDetails": "Small front garden; Large back garden with patio",
                "architecturalType": "Period property; Victorian terrace",
                "userId": user_id,  # Added userId
                "propertyId": 999  # Added propertyId
            }
        }

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Property Document"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.property_details_manager, 'update_property_details',
                             return_value=mock_property_command):
            # Setup mock responses
            mock_property_service.get_user_primary_property.return_value = existing_property

            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True
            assert content == "Mock content"

            # Verify property update was called with concatenated data
            update_call_args = mock_backend_api.update_property_details.call_args
            assert update_call_args[0][0] == 999
            property_details = update_call_args[0][1].propertyDetails
            assert "Large back garden with patio" in property_details.gardenDetails
            assert "Victorian terrace" in property_details.architecturalType

        logger.info("PASS Test passed: process_file with property data concatenation")

    @pytest.mark.asyncio
    async def test_process_file_property_with_address_update(self, service, mock_backend_api,
                                                             mock_appliance_service, mock_property_service):
        """Test processing property data with address object updates"""
        logger.info("Testing process_file with property address object update")

        test_file = Path("test-pdf/property_document.pdf")
        document_id = 123
        user_id = 456

        # Mock existing property
        existing_property = MagicMock()
        existing_property.id = 999

        relevant_classification = RelevantDocumentClassification(
            category="Property",
            label="Property Details",
            isNewLabel=False
        )

        # Mock extracted data with address components
        mock_extracted_data = {
            "status": "SUCCESS",
            "relevant_tables": ["Property"],
            "extracted_data": {
                "Property": {
                    "address": {
                        "streetLine1": "456 Oak Avenue",
                        "streetLine2": "Unit 2B",
                        "townOrCity": "Manchester",
                        "postcode": "M1 2AB",
                        "houseAccess": "Side entrance",
                        "parkingInstructions": "Street parking only"
                    },
                    "numberOfBedrooms": 3
                }
            }
        }

        # Mock property manager response for address update
        mock_property_command = {
            "command": "update",
            "id": 999,
            "fields": ["address", "numberOfBedrooms"],
            "update_data": {
                "address": {
                    "streetLine1": "456 Oak Avenue",
                    "streetLine2": "Unit 2B",
                    "townOrCity": "Manchester",
                    "postcode": "M1 2AB",
                    "houseAccess": "Side entrance",
                    "parkingInstructions": "Street parking only"
                },
                "numberOfBedrooms": 3,
                "userId": user_id,  # Added userId
                "propertyId": 999  # Added propertyId
            }
        }

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Property Document"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data), \
                patch.object(service.property_details_manager, 'update_property_details',
                             return_value=mock_property_command):
            # Setup mock responses
            mock_property_service.get_user_primary_property.return_value = existing_property

            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True
            assert content == "Mock content"

            # Verify property update was called with address object
            update_call_args = mock_backend_api.update_property_details.call_args
            assert update_call_args[0][0] == 999
            property_details = update_call_args[0][1].propertyDetails

            # Verify the address object structure
            assert hasattr(property_details, 'address')
            if property_details.address:
                assert property_details.address.streetLine1 == "456 Oak Avenue"
                assert property_details.address.streetLine2 == "Unit 2B"
                assert property_details.address.townOrCity == "Manchester"
                assert property_details.address.postcode == "M1 2AB"
                assert property_details.address.houseAccess == "Side entrance"
                assert property_details.address.parkingInstructions == "Street parking only"

        logger.info("PASS Test passed: process_file with property address object update")

    @pytest.mark.asyncio
    async def test_process_file_no_relevant_tables(self, service, mock_backend_api, mock_appliance_service):
        """Test processing a document with no relevant data tables"""
        logger.info("Testing process_file with no relevant tables")

        test_file = Path("test-pdf/gas_certificate.pdf")
        document_id = 123
        user_id = 456

        relevant_classification = RelevantDocumentClassification(
            category="Bills",
            label="Gas Certificates",
            isNewLabel=False
        )

        # Mock extraction result with no relevant tables
        mock_extracted_data = {
            "status": "NOT_RELEVANT",
            "message": "No relevant data tables found"
        }

        with patch.object(service.document_labeling_agent, 'process_file', return_value=relevant_classification), \
                patch.object(service.document_summarization_service, 'process_file_gemini',
                             return_value="Mock content"), \
                patch.object(service.document_summarization_service, 'get_ai_short_description',
                             return_value="Gas Certificate"), \
                patch.object(service, 'extract_data_from_file', return_value=mock_extracted_data):
            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is True  # Still returns True because document was processed successfully
            assert content == "Mock content"

            # Verify status was updated to completed
            completed_calls = [call for call in mock_backend_api.update_document_status.call_args_list
                               if call[0][1].status == "processing"]
            assert len(completed_calls) == 2

        logger.info("PASS Test passed: process_file with no relevant tables")

    @pytest.mark.asyncio
    async def test_process_file_error_handling(self, service, mock_backend_api, mock_appliance_service):
        """Test process_file error handling"""
        logger.info("Testing process_file error handling")

        test_file = Path("test-pdf/gas_certificate.pdf")
        document_id = 123
        user_id = 456

        # Mock an exception during processing
        with patch.object(service.document_labeling_agent, 'process_file', side_effect=Exception("Test error")):
            # Execute test
            result, content = await service.process_file(test_file, document_id, user_id)

            # Assertions
            assert result is False
            assert content is None

            # Verify error status was set
            error_calls = [call for call in mock_backend_api.update_document_status.call_args_list
                           if call[0][1].status == "error"]
            assert len(error_calls) == 1
            assert "Test error" in error_calls[0][0][1].errorMessage

        logger.info("PASS Test passed: process_file error handling")


@pytest.mark.asyncio
@pytest.mark.integration
async def test_extract_data_from_file_integration():
    """Integration test for extract_data_from_file method"""
    # Skip if GEMINI_API_KEY is not set
    if "GEMINI_API_KEY" not in os.environ:
        pytest.skip("GEMINI_API_KEY environment variable is not set")

    try:
        agent = DataExtractorService(backend_api=BackendAPI("", ""), appliance_service=Any)
        logger.info("Successfully initialized DataExtractorService")
    except KeyError as e:
        pytest.skip(f"Failed to initialize DataExtractorService: {e}")

    project_dir = Path(os.getcwd())
    test_dir = project_dir / "test-pdf"
    test_file = test_dir / "gas_certificate.pdf"

    if not test_file.exists():
        pytest.skip(f"Test file not found: {test_file}")

    logger.info(f"Testing file: {test_file}")

    result_json = await agent.extract_data_from_file(test_file, "")
    logger.info(f"Extracted data: {result_json}")

    # Assertions
    assert result_json.get("status") == "SUCCESS"
    assert "extracted_data" in result_json

    extracted_data = result_json["extracted_data"]

    # Check for both possible table types
    if "Appliances" in extracted_data:
        appliance_data = extracted_data["Appliances"]
        assert appliance_data.get("appliance_type") == "Boiler"
        assert appliance_data.get("brand") == "Maxol"

    if "Property" in extracted_data:
        property_data = extracted_data["Property"]
        # Check if address is extracted as object structure
        if property_data.get("address"):
            address = property_data["address"]
            # Address should be an object with components, not a string
            assert isinstance(address, dict)
            # Check for expected address components
            expected_fields = ["streetLine1", "streetLine2", "townOrCity", "postcode", "houseAccess",
                               "parkingInstructions"]
            for field in expected_fields:
                assert field in address

    logger.info("PASS Integration test passed: extract_data_from_file")


# For backward compatibility - allow running as script
async def run_legacy_tests():
    """Run tests in legacy format for backward compatibility"""
    logger.info("Running legacy test format...")

    if "GEMINI_API_KEY" not in os.environ:
        logger.exception("GEMINI_API_KEY environment variable is not set!")
        logger.exception("Please set this variable and try again:")
        logger.exception("  export GEMINI_API_KEY='your-api-key'")
        return False

    try:
        await test_extract_data_from_file_integration()
        logger.info("PASS Legacy tests passed!")
        return True
    except Exception as e:
        logger.exception(f"✗ Legacy tests failed: {str(e)}")
        return False


async def main():
    """Main function for running tests as script"""
    logger.info("Starting DataExtractorService test suite...")
    logger.info("For pytest compatibility, run: pytest test_data_extractor.py")
    logger.info("Running legacy integration test...")

    success = await run_legacy_tests()

    if success:
        logger.info("PASS Legacy integration test passed!")
        exit(0)
    else:
        logger.exception("FAIL Legacy integration test failed!")
        exit(1)


if __name__ == "__main__":
    # TO run TESTS
    # python -m src.agents.dataextractors.test_data_extractor
    # OR
    # pytest src/agents/dataextractors/test_data_extractor.py
    asyncio.run(main())