import logging
from typing import Dict, List, Any, Optional
from src.db_models.appliance import Appliance


class AppliancesManager:
    """
    Non-LLM version of AppliancesManagerAgent that uses deterministic logic.
    """

    def __init__(self):
        self.logger = logging.getLogger("uvicorn")

    def _normalize_string(self, value: Any) -> Optional[str]:
        if value is None:
            return None
        if isinstance(value, str):
            stripped = value.strip()
            return stripped if stripped else None
        return str(value).strip() if str(value).strip() else None

    def _extract_non_null_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        result = {}
        for key, value in data.items():
            normalized_value = self._normalize_string(value)
            if normalized_value is not None:
                result[key] = normalized_value
        return result

    def _fields_match(self, extracted_value: Any, db_value: Any) -> bool:
        norm_extracted = self._normalize_string(extracted_value)
        norm_db = self._normalize_string(db_value)

        # If extracted is None it dosn't conflict with anything
        if norm_extracted is None:
            return True

        # If db is None but extracted has value they dont match
        if norm_db is None:
            return False

        return norm_extracted == norm_db

    def _get_appliance_dict(self, appliance: Appliance) -> Dict[str, Any]:
        warranty = None
        if appliance.warranty:
            # Sprawdz czy jest stringiem czy data
            if hasattr(appliance.warranty, 'isoformat'):
                warranty = appliance.warranty.isoformat()
            else:
                warranty = str(appliance.warranty)

        return {
            "id": appliance.id,
            "propertyId": appliance.propertyId,
            "type": appliance.type,
            "brand": appliance.brand,
            "model": appliance.model,
            "warranty": warranty,
            "serialNumber": appliance.serialNumber,
            "otherDetails": appliance.otherDetails
        }

    def _map_extracted_data(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "type": extracted_data.get("appliance_type"),
            "brand": extracted_data.get("brand"),
            "model": extracted_data.get("model"),
            "serialNumber": extracted_data.get("serial_number"),
            "warranty": extracted_data.get("warranty_details"),
            "otherDetails": extracted_data.get("other_details")
        }

    def _find_matching_appliance(self,
                                 extracted_data: Dict[str, Any],
                                 similar_appliances: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        extracted_type = self._normalize_string(extracted_data.get("type"))
        extracted_brand = self._normalize_string(extracted_data.get("brand"))

        # Type and Brand are primary matching fields
        if not extracted_type or not extracted_brand:
            return None

        for db_appliance in similar_appliances:
            db_type = self._normalize_string(db_appliance.get("type"))
            db_brand = self._normalize_string(db_appliance.get("brand"))

            # Check if type and brand match
            if db_type == extracted_type and db_brand == extracted_brand:

                extracted_model = self._normalize_string(extracted_data.get("model"))
                extracted_serial = self._normalize_string(extracted_data.get("serialNumber"))
                db_model = self._normalize_string(db_appliance.get("model"))
                db_serial = self._normalize_string(db_appliance.get("serialNumber"))

                # Check for model conflict
                if (extracted_model is not None and db_model is not None and
                        extracted_model != db_model):
                    continue

                # Check for serial number conflict
                if (extracted_serial is not None and db_serial is not None and
                        extracted_serial != db_serial):
                    continue

                return db_appliance

        return None

    def _get_fields_to_update(self,
                              extracted_data: Dict[str, Any],
                              db_appliance: Dict[str, Any]) -> List[str]:
        fields_to_update = []

        # Check each field
        for field, extracted_value in extracted_data.items():
            if field in ["id", "propertyId"]:
                continue

            normalized_extracted = self._normalize_string(extracted_value)
            if normalized_extracted is None:
                continue

            db_value = db_appliance.get(field)
            normalized_db = self._normalize_string(db_value)

            self.logger.info(f"Checking field '{field}': extracted='{normalized_extracted}', db='{normalized_db}'")

            # If DB doesn't have this field update
            # (commented update of existing field that has different value)
            if normalized_db is None:  # or normalized_db != normalized_extracted:
                fields_to_update.append(field)
                self.logger.info(f"Field '{field}' marked for update")

        return fields_to_update

    def _is_data_already_present(self,
                                 extracted_data: Dict[str, Any],
                                 db_appliance: Dict[str, Any]) -> bool:
        extracted_non_null = self._extract_non_null_fields(extracted_data)

        for field, extracted_value in extracted_non_null.items():
            if field in ["id", "propertyId"]:
                continue

            db_value = self._normalize_string(db_appliance.get(field))
            extracted_value_norm = self._normalize_string(extracted_value)

            if db_value != extracted_value_norm:
                return False

        return True

    async def insert_or_update_appliance(self,
                                         extracted_appliance_data: Dict[str, Any],
                                         similar_appliances_in_db: List[Appliance]) -> Dict[str, Any]:
        """
        Determine if an extracted appliance should be inserted, updated, or ignored.

        Args:
            extracted_appliance_data: Dictionary containing the extracted appliance data
            similar_appliances_in_db: List of similar appliances from the database

        Returns:
            Dict with command ('do_nothing', 'insert', or 'update') and fields to update if applicable
        """
        try:
            # Map extracted data to database field names
            mapped_extracted_data = self._map_extracted_data(extracted_appliance_data)

            # Convert database appliances to dictionaries
            similar_appliances_dicts = [self._get_appliance_dict(app) for app in similar_appliances_in_db]

            self.logger.info(f"Mapped extracted data: {mapped_extracted_data}")
            self.logger.info(f"Similar appliances: {similar_appliances_dicts}")

            # Find matching appliance in database
            matching_appliance = self._find_matching_appliance(mapped_extracted_data, similar_appliances_dicts)

            if matching_appliance is None:
                # No matching appliance found - INSERT
                self.logger.info("No matching appliance found - inserting new record")
                return {"command": "insert"}

            self.logger.info(f"Found matching appliance: {matching_appliance}")

            # Check if extracted data is already fully present in the database
            if self._is_data_already_present(mapped_extracted_data, matching_appliance):
                # All extracted data is already in database - DO NOTHING
                self.logger.info("All extracted data already present in database - doing nothing")
                return {"command": "do_nothing"}

            # Check if there are new fields to update
            fields_to_update = self._get_fields_to_update(mapped_extracted_data, matching_appliance)

            self.logger.info(f"Fields to update: {fields_to_update}")

            if not fields_to_update:
                # No new information to add DO NOTHING
                self.logger.info("No new information to add - doing nothing")
                return {"command": "do_nothing"}

            # There are fields to update: UPDATE
            self.logger.info(f"Updating appliance {matching_appliance['id']} with fields: {fields_to_update}")
            return {
                "command": "update",
                "id": matching_appliance["id"],
                "fields": fields_to_update
            }

        except Exception as e:
            self.logger.exception(f"Error in insert_or_update_appliance: {str(e)}")
            # Default to insert on error to preserve data
            return {"command": "insert"}
