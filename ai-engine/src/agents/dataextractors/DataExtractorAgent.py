import os
import logging
import asyncio
import json
import google.generativeai as genai
from typing import Dict, List, Any, Optional, ClassVar

from src.agents.utils.PromptLoader import PromptLoader


class DataExtractorAgent:
    _prompt_cache: ClassVar[Dict[str, str]] = {}

    _prompt_loader: ClassVar[Optional[PromptLoader]] = None

    _initialized: ClassVar[bool] = False

    _prompt_map: ClassVar[Dict[str, str]] = {
        "Property": "property_extraction_prompt",
        "Insurance": "insurance_extraction_prompt",
        "Bills": "bills_extraction_prompt",
        "Appliances": "appliances_extraction_prompt",
        "Legal": "legal_extraction_prompt",
        "Building": "building_extraction_prompt",
        "Neighbourhood Info": "neighbourhood_extraction_prompt"
    }

    @classmethod
    def initialize(cls):
        if not cls._initialized:
            cls._prompt_loader = PromptLoader()
            cls._initialized = True
            # Preload all prompts at class initialization
            for table, prompt_file in cls._prompt_map.items():
                try:
                    if prompt_file not in cls._prompt_cache:
                        cls._prompt_cache[prompt_file] = cls._prompt_loader.load_system_prompt(prompt_file)
                        logging.getLogger("uvicorn").info(f"Preloaded prompt for table: {table}")
                except Exception as e:
                    logging.getLogger("uvicorn").error(f"Error preloading prompt for {table}: {str(e)}")

    def __init__(self):
        self.logger = logging.getLogger("uvicorn")

        DataExtractorAgent.initialize()

        if "GEMINI_API_KEY" not in os.environ:
            raise KeyError("GEMINI_API_KEY environment variable is required")
        self._gemini_api_key = os.environ["GEMINI_API_KEY"]
        genai.configure(api_key=self._gemini_api_key)
        self.gemini_model = genai.GenerativeModel("gemini-2.5-pro")

    @classmethod
    def _get_extraction_prompt(cls, table: str) -> str:
        prompt_file = cls._prompt_map.get(table)
        if not prompt_file:
            raise ValueError(f"No prompt file defined for table: {table}")

        if prompt_file in cls._prompt_cache:
            return cls._prompt_cache[prompt_file]

        # Load the prompt
        try:
            extraction_prompt = cls._prompt_loader.load_system_prompt(prompt_file)
            cls._prompt_cache[prompt_file] = extraction_prompt
            return extraction_prompt
        except Exception as e:
            logging.getLogger("uvicorn").error(f"Error loading prompt for {table}: {str(e)}")
            raise

    async def extract_data(self, relevant_tables: List[str], content: str) -> Dict[str, Any]:
        if "NOT_RELEVANT" in relevant_tables:
            return {"status": "NO_DATA_EXTRACTED"}

        extraction_tasks = []
        for table in relevant_tables:
            if table in self._prompt_map:
                extraction_tasks.append(self._extract_table_data(table, content))

        # Run all extraction tasks concurrently
        results = await asyncio.gather(*extraction_tasks, return_exceptions=True)

        # Combine the results
        extracted_data = {}
        for i, table in enumerate(relevant_tables):
            if i < len(results):  # Safety check
                if isinstance(results[i], Exception):
                    self.logger.exception(f"Error extracting data for table {table}: {str(results[i])}")
                    extracted_data[table] = {"error": str(results[i])}
                else:
                    extracted_data[table] = results[i]

        return extracted_data

    async def _extract_table_data(self, table: str, content: str) -> Dict[str, Any]:
        try:
            # Get the appropriate extraction prompt from  cache
            extraction_prompt = self._get_extraction_prompt(table)

            # Prepare prompt for Gemini
            full_prompt = f"""
            {extraction_prompt}

            Extract {table} data from the following content:

            {content}

            Respond with a valid JSON object only. No explanations or additional text.
            """

            # Call Gemini Pro for extraction
            response = await self.gemini_model.generate_content_async(
                contents=full_prompt,
                generation_config={
                    "temperature": 0.2,
                    "top_p": 0.8,
                    "max_output_tokens": 200000,
                    "response_mime_type": "application/json"  # Request JSON directly
                }
            )

            self.logger.info(f"Successfully extracted data for table {response}")
            # Extract and parse the JSON from the response
            extracted_data = self._parse_extraction_response(response.text, table)
            self.logger.info(f"Successfully extracted data for table {table}")

            return extracted_data

        except Exception as e:
            self.logger.exception(f"Error extracting data for table {table}: {str(e)}")
            raise

    def _parse_extraction_response(self, response: str, table: str) -> Dict[str, Any]:
        import re

        # Try to extract JSON from the response
        try:
            # Clean up the response remove markdown code blocks if present
            cleaned_response = re.sub(r'```json\s*|\s*```', '', response).strip()

            # Try to parse directly first
            try:
                data = json.loads(cleaned_response)
                return data
            except json.JSONDecodeError:
                # If direct parsing fails, try to find a JSON object
                json_match = re.search(r'({[\s\S]*})', cleaned_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1).strip()
                    data = json.loads(json_str)
                    return data
                else:
                    raise ValueError("No valid JSON found in response")

        except Exception as e:
            self.logger.exception(f"Error parsing extraction response for table {table}: {str(e)}")
            self.logger.debug(f"Raw response: {response}")
            return {"status": "PARSING_ERROR", "error": str(e)}

