import asyncio
import os
import logging

from src.agents.dataextractors.AppliancesManager import AppliancesManager
from src.db_models.appliance import Appliance

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_appliances_manager")


# Mock Appliance class for testing
class MockAppliance(Appliance):
    def __init__(self, id=None, propertyId=None, type=None, brand=None, model=None,
                 warranty=None, serialNumber=None, otherDetails=None):
        self.id = id
        self.propertyId = propertyId
        self.type = type
        self.brand = brand
        self.model = model
        self.warranty = warranty
        self.serialNumber = serialNumber
        self.otherDetails = otherDetails


async def test_appliances_manager():
    try:
        agent = AppliancesManager()
        logger.info("Successfully initialized AppliancesManagerAgent")
    except KeyError as e:
        logger.exception(f"Failed to initialize AppliancesManagerAgent: {e}")
        logger.exception("Make sure the GEMINI_API_KEY environment variable is set")
        return False

    # Define test cases
    test_cases = [
        {
            "name": "Case 1: Insert - New Appliance",
            "extracted_data": {
                "appliance_type": "Refrigerator",
                "brand": "Samsung",
                "model": "RF28R7351",
                "serial_number": None,
                "warranty_details": None,
                "other_details": None
            },
            "similar_appliances": [],
            "expected_command": "insert",
            "expected_fields": None
        },
        {
            "name": "Case 2: Insert - Conflict in Model",
            "extracted_data": {
                "appliance_type": "Boiler",
                "brand": "Maxol",
                "model": "Micro turbo",
                "serial_number": None,
                "warranty_details": None,
                "other_details": None
            },
            "similar_appliances": [
                MockAppliance(id=1, propertyId=101, type="Boiler", brand="Maxol", model="Micro basic")
            ],
            "expected_command": "insert",
            "expected_fields": None
        },
        {
            "name": "Case 3: Update - New Information",
            "extracted_data": {
                "appliance_type": "Boiler",
                "brand": "Maxol",
                "model": None,
                "serial_number": "SN12345",
                "warranty_details": "2 years",
                "other_details": "Installed 2023"
            },
            "similar_appliances": [
                MockAppliance(id=2, propertyId=102, type="Boiler", brand="Maxol")
            ],
            "expected_command": "update",
            "expected_fields": ["serialNumber", "otherDetails"]
        },
        {
            "name": "Case 4: Do Nothing - Data Already Present",
            "extracted_data": {
                "appliance_type": "Dishwasher",
                "brand": "Bosch",
                "model": None,
                "serial_number": None,
                "warranty_details": None,
                "other_details": None
            },
            "similar_appliances": [
                MockAppliance(id=3, propertyId=103, type="Dishwasher", brand="Bosch",
                              model="SMS68MW05E", serialNumber="FD12345")
            ],
            "expected_command": "do_nothing",
            "expected_fields": None
        },
        {
            "name": "Case 5: Update - Additional Fields",
            "extracted_data": {
                "appliance_type": "Oven",
                "brand": "Whirlpool",
                "model": "WOS51EC0HS",
                "serial_number": "SN54321",
                "warranty_details": None,
                "other_details": None
            },
            "similar_appliances": [
                MockAppliance(id=4, propertyId=104, type="Oven", brand="Whirlpool")
            ],
            "expected_command": "update",
            "expected_fields": ["model", "serialNumber"]
        },
        {
            "name": "Case 6: Do Nothing - Less Information",
            "extracted_data": {
                "appliance_type": "Microwave",
                "brand": "LG",
                "model": None,
                "serial_number": None,
                "warranty_details": None,
                "other_details": None
            },
            "similar_appliances": [
                MockAppliance(id=5, propertyId=105, type="Microwave", brand="LG",
                              model="MS2336GIB", warranty="2024-12-31")
            ],
            "expected_command": "do_nothing",
            "expected_fields": None
        }
    ]

    # Run the tests
    test_results = {}
    tests_passed = True

    for i, test_case in enumerate(test_cases):
        logger.info(f"Running test: {test_case['name']}")

        try:
            # Call the agent
            result = await agent.insert_or_update_appliance(
                test_case["extracted_data"],
                test_case["similar_appliances"]
            )

            # Check the command
            command_passed = result["command"] == test_case["expected_command"]

            # Check fields if they should be present
            fields_passed = True
            if test_case["expected_command"] == "update":
                if "fields" not in result:
                    fields_passed = False
                    logger.exception(f"Expected 'fields' in result but not found")
                else:
                    # Check if all expected fields are present
                    for field in test_case["expected_fields"]:
                        if field not in result["fields"]:
                            fields_passed = False
                            logger.exception(f"Expected field '{field}' not found in result fields: {result['fields']}")

            test_passed = command_passed and fields_passed

            if test_passed:
                logger.info(f"PASS Test {i + 1} PASSED: {test_case['name']}")
                logger.info(f"   Got: {result}")
            else:
                logger.exception(f"X Test {i + 1} FAILED: {test_case['name']}")
                logger.exception(f"   Expected command: {test_case['expected_command']}, got: {result['command']}")
                if test_case["expected_command"] == "update":
                    logger.exception(
                        f"   Expected fields: {test_case['expected_fields']}, got: {result.get('fields', 'no fields')}")
                tests_passed = False

            test_results[test_case["name"]] = test_passed

        except Exception as e:
            logger.exception(f"Error in test {i + 1}: {str(e)}")
            test_results[test_case["name"]] = False
            tests_passed = False

    # Test the real example from the requirements
    logger.info("Running example test from requirements")

    try:
        # Example 1 (insert - conflict exists)
        extracted_data = {
            "appliance_type": "Boiler",
            "brand": "Maxol",
            "model": "Micro turbo"
        }
        similar_appliances = [
            MockAppliance(id=6, propertyId=106, type="Boiler", brand="Maxol", model="Micro basic")
        ]

        result = await agent.insert_or_update_appliance(extracted_data, similar_appliances)
        if result["command"] == "insert":
            logger.info("PASS Example 1 PASSED: Insert command returned due to conflicting model")
        else:
            logger.exception(f"X Example 1 FAILED: Expected 'insert', got '{result['command']}'")
            tests_passed = False

        # Example 2 (update)
        extracted_data = {
            "appliance_type": "Boiler",
            "brand": "Maxol"
        }
        similar_appliances = [
            MockAppliance(id=7, propertyId=107, type="Boiler", brand="Maxol",
                          model="Micro basic", serialNumber="SN 923T51")
        ]

        result = await agent.insert_or_update_appliance(extracted_data, similar_appliances)
        expected_fields = []  # Actually expects none since extracted data has less info than DB

        if result["command"] == "do_nothing":
            logger.info("PASS Example 2 PASSED: Do nothing command returned since extracted data has less info")
        else:
            logger.exception(f"X Example 2 FAILED: Expected 'do_nothing', got '{result['command']}'")
            tests_passed = False

        # Example 3 (update with additional fields)
        extracted_data = {
            "appliance_type": "Boiler",
            "brand": "Maxol",
            "model": "Micro basic",
            "serial_number": "SN 923T51",
            "warranty_details": "5 years from installation"
        }
        similar_appliances = [
            MockAppliance(id=8, propertyId=108, type="Boiler", brand="Maxol"),
            MockAppliance(id=8, propertyId=108, type="Boiler", brand="Maxol", model="Micro 78")
        ]

        result = await agent.insert_or_update_appliance(extracted_data, similar_appliances)
        expected_fields = ["model", "serialNumber", "warranty"]

        if result["command"] == "update" and "fields" in result:
            fields_match = all(field in result["fields"] for field in expected_fields)
            if fields_match:
                logger.info("PASS Example 3 PASSED: Update command with correct fields")
            else:
                logger.exception(
                    f"X Example 3 FAILED: Fields mismatch. Expected: {expected_fields}, Got: {result.get('fields', [])}")
                tests_passed = False
        else:
            logger.exception(f"X Example 3 FAILED: Expected 'update', got '{result['command']}'")
            tests_passed = False

    except Exception as e:
        logger.exception(f"Error in examples test: {str(e)}")
        tests_passed = False

    # Print test summary
    logger.info("\n--- Test Summary ---")
    if tests_passed:
        logger.info("PASS: All tests passed!")
    else:
        logger.exception("FAIL: Some tests failed!")

    for name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        logger.info(f"{status}: {name}")

    return tests_passed


async def main():
    logger.info("Starting AppliancesManagerAgent test...")

    if "GEMINI_API_KEY" not in os.environ:
        logger.exception("GEMINI_API_KEY environment variable is not set!")
        logger.exception("Please set this variable and try again:")
        logger.exception("  export GEMINI_API_KEY='your-api-key'")
        return

    success = await test_appliances_manager()

    if success:
        logger.info("AppliancesManagerAgent is functioning correctly.")
        exit(0)
    else:
        logger.exception("AppliancesManagerAgent test failed!")
        exit(1)


if __name__ == "__main__":
    # To run tests:
    # python -m src.agents.dataextractors.test_appliances_manager
    asyncio.run(main())
