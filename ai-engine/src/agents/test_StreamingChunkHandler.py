import asyncio
import json
import re
from unittest.mock import Async<PERSON><PERSON>, MagicMock, call

import pytest

from src.agents.StreamingChunkHandler import StreamingChunkHandler, Command, Tool
from src.schemas import ImageUrl


async def collect_async_gen(agen):
    return [item async for item in agen]

@pytest.mark.parametrize(
    "buffer, expected_index",
    [
        ("hello world", -1),
        ("hello [complete]", -1),
        ("hello [[complete]]", -1),
        ("hello [incomplete", 6),
        ("hello [[incomplete", 6),
        ("hello [[incomplete]", 6),  # Still incomplete, expects "]]"
        ("text [cmd:val] text [incomplete", 20),
        ("text [[cmd:val]] text [[incomplete", 22),
        ("", -1),
        ("[", 0),
        ("[[", 0),
    ],
)
def test_detect_incomplete_token_at_end(buffer, expected_index):
    """Tests the logic for detecting an incomplete command token at the end of the buffer."""
    assert StreamingChunkHandler._detect_incomplete_token_at_end(buffer) == expected_index


@pytest.mark.parametrize(
    "obj, expected_content",
    [
        (MagicMock(content="some string"), "some string"),
        (MagicMock(content=""), ""),
        (MagicMock(content=None), ""),
        (MagicMock(spec=[]), ""),  # No content attribute
        (MagicMock(content=[]), ""),
        (
            MagicMock(
                content=[
                    {"type": "text", "text": "Hello "},
                    {"type": "image", "source": "..."},
                    {"type": "text", "text": "world"},
                ]
            ),
            "Hello world",
        ),
        (MagicMock(content=[{"type": "text", "text": "Just one part"}]), "Just one part"),
    ],
)
def test_get_content(obj, expected_content):
    """Tests the extraction of text content from various LangChain-like chunk objects."""
    assert StreamingChunkHandler.get_content(obj) == expected_content


# --- Tests for core async generator methods ---

@pytest.mark.asyncio
async def test_process_text_and_commands_chunk_no_commands():
    """Tests that text without commands is yielded correctly."""
    handler = StreamingChunkHandler(commands=[])
    chunks = await collect_async_gen(handler._process_text_and_commands_chunk("Hello world"))

    assert len(chunks) == 1
    assert json.loads(chunks[0].split("data: ")[1]) == {"type": "content", "data": "Hello world"}
    assert handler._buffer == ""
    assert handler.accumulated_text_for_processing == "Hello world"


@pytest.mark.asyncio
async def test_process_text_and_commands_chunk_with_incomplete_command():
    """Tests that text before an incomplete command is yielded, and the rest is buffered."""
    mock_cmd_handler = MagicMock(return_value=None)
    command = Command(re.compile(r"\[cmd:(.*?)\]"), mock_cmd_handler)
    handler = StreamingChunkHandler(commands=[command])

    chunks = await collect_async_gen(handler._process_text_and_commands_chunk("Some text [cmd:incomplete"))

    assert len(chunks) == 1
    assert json.loads(chunks[0].split("data: ")[1]) == {"type": "content", "data": "Some text "}
    assert handler._buffer == "[cmd:incomplete"
    assert handler.accumulated_text_for_processing == "Some text "
    mock_cmd_handler.assert_not_called()


@pytest.mark.asyncio
async def test_process_text_and_commands_chunk_command_split_across_chunks():
    """Tests that a command split across two chunks is correctly processed."""
    mock_cmd_handler = MagicMock(return_value=asyncio.create_task(asyncio.sleep(0)))
    command = Command(re.compile(r"\[cmd:(.*?)\]"), mock_cmd_handler)
    handler = StreamingChunkHandler(commands=[command])

    # First chunk, part of the command
    chunks1 = await collect_async_gen(handler._process_text_and_commands_chunk("Text before [cmd:pa"))
    assert len(chunks1) == 1
    assert json.loads(chunks1[0].split("data: ")[1]) == {"type": "content", "data": "Text before "}
    assert handler._buffer == "[cmd:pa"
    assert len(handler._pending_tasks) == 0

    # Second chunk, completing the command
    chunks2 = await collect_async_gen(handler._process_text_and_commands_chunk("rtial] and after."))
    assert len(chunks2) == 1
    assert json.loads(chunks2[0].split("data: ")[1]) == {"type": "content", "data": " and after."}
    assert handler._buffer == ""

    # Verify command was handled
    mock_cmd_handler.assert_called_once_with("partial")
    assert len(handler._pending_tasks) == 1
    # Clean up task
    for task in handler._pending_tasks:
        task.cancel()


@pytest.mark.asyncio
async def test_process_llm_chunk_with_text_and_tool_calls():
    """Tests that process_llm_chunk correctly handles chunks with both text and tool calls."""
    handler = StreamingChunkHandler(commands=[])
    mock_chunk = MagicMock()
    mock_chunk.content = "Some text from LLM. "
    mock_chunk.tool_calls = [{"name": "search", "args": {"query": "test"}}]

    # Problem with real AsyncGen:  mock the method with a real async generator
    # function to satisfy the `async for` loop, and wrap it in a MagicMock
    # to track calls for assertion.
    async def mock_processor_gen(*_):
        yield f"data: {json.dumps({'type': 'content', 'data': 'Some text from LLM. '})}\n\n"

    # Patch the text processor to see if it's called
    handler._process_text_and_commands_chunk = MagicMock(wraps=mock_processor_gen)

    chunks = await collect_async_gen(handler.process_llm_chunk(mock_chunk))

    # Verify text was processed and the mock was called correctly
    handler._process_text_and_commands_chunk.assert_called_once_with("Some text from LLM. ")
    assert len(chunks) == 1
    assert "Some text from LLM." in chunks[0]

    # Verify tool call was accumulated
    assert handler.get_detected_tool_calls() == [{"name": "search", "args": {"query": "test"}}]




@pytest.mark.asyncio
async def test_execute_tool_calls_successful():
    """Tests concurrent and successful execution of multiple tools."""
    tool1_handler = AsyncMock(return_value="Result from tool 1")
    tool2_handler = AsyncMock(return_value="Result from tool 2")
    tool1 = Tool(name="tool_one", handler=tool1_handler)
    tool2 = Tool(name="tool_two", handler=tool2_handler)
    handler = StreamingChunkHandler(commands=[], tools=[tool1, tool2])

    tool_calls = [
        {"name": "tool_one", "args": {"arg1": "a"}},
        {"name": "tool_two", "args": {"arg2": "b"}},
    ]
    context = await handler.execute_tool_calls(tool_calls, extra_kwarg="extra")

    tool1_handler.assert_awaited_once_with(arg1="a", extra_kwarg="extra")
    tool2_handler.assert_awaited_once_with(arg2="b", extra_kwarg="extra")
    assert context == "Result from tool 1\n\nResult from tool 2"


@pytest.mark.asyncio
async def test_execute_tool_calls_with_failure_and_unknown():
    """Tests handling of a failed tool, an unknown tool, and a successful tool."""
    success_handler = AsyncMock(return_value="Success")
    fail_handler = AsyncMock(side_effect=ValueError("Tool failed"))
    tool_success = Tool(name="success_tool", handler=success_handler)
    tool_fail = Tool(name="fail_tool", handler=fail_handler)
    handler = StreamingChunkHandler(commands=[], tools=[tool_success, tool_fail])
    handler.logger = MagicMock()  # Mock logger to check calls

    tool_calls = [
        {"name": "unknown_tool", "args": {}},
        {"name": "fail_tool", "args": {}},
        {"name": "success_tool", "args": {}},
    ]
    context = await handler.execute_tool_calls(tool_calls)

    # Verify logging
    handler.logger.warning.assert_called_with("LLM tried to call unknown tool: unknown_tool")
    handler.logger.exception.assert_called_with("Tool execution failed: Tool failed")

    # Verify handlers were called
    fail_handler.assert_awaited_once()
    success_handler.assert_awaited_once()

    # Verify returned context
    assert "Context retrieval failed for a tool: Tool failed" in context
    assert "Success" in context



@pytest.mark.asyncio
async def test_finalize_with_remaining_buffer():
    """Tests that finalize yields any remaining text in the buffer."""
    handler = StreamingChunkHandler(commands=[])
    handler._buffer = "Final text."
    handler.logger = MagicMock()

    chunks = await collect_async_gen(handler.finalize())

    assert len(chunks) == 1
    assert json.loads(chunks[0].split("data: ")[1]) == {"type": "content", "data": "Final text."}
    handler.logger.warning.assert_called_with("Yielding remaining buffer during finalization: Final text.")
    assert handler.accumulated_text_for_processing == "Final text."


@pytest.mark.asyncio
async def test_finalize_with_pending_tasks():
    """Tests that finalize correctly processes different results from pending tasks."""
    handler = StreamingChunkHandler(commands=[])
    handler.logger = MagicMock()

    # Mock tasks with different return types
    async def task_success_clickable():
        return [ImageUrl(imageUrl="http://clickable.com/1.jpg", description="Clickable")]

    async def task_success_image_url():
        return ("query", [ImageUrl(imageUrl="http://image.com/1.jpg", description="Image")])

    async def task_success_fallback():
        return "This is a fallback string."

    async def task_failure():
        raise ValueError("Task failed")

    handler._pending_tasks = [
        asyncio.create_task(task_success_clickable()),
        asyncio.create_task(task_success_image_url()),
        asyncio.create_task(task_success_fallback()),
        asyncio.create_task(task_failure()),
    ]

    chunks = await collect_async_gen(handler.finalize())

    # Expecting 3 chunks: "waiting" message, fallback string, then done
    assert len(chunks) == 2
    assert "Please wait a few seconds" in chunks[0]
    assert "This is a fallback string." in chunks[1]

    # Verify accumulated data
    assert len(handler.accumulated_clickable_urls) == 1
    assert handler.accumulated_clickable_urls[0].description == "Clickable"
    assert len(handler.accumulated_image_urls) == 1
    assert handler.accumulated_image_urls[0].description == "Image"

    # Verify logging
    handler.logger.exception.assert_called_with("Async task failed: Task failed")


@pytest.mark.asyncio
async def test_finalize_no_buffer_no_tasks():
    """Tests that finalize does nothing if there's no work to do."""
    handler = StreamingChunkHandler(commands=[])
    chunks = await collect_async_gen(handler.finalize())
    assert len(chunks) == 0


@pytest.mark.asyncio
async def test_process_text_and_commands_chunk_with_job_summary():
    """Tests that a multi-chunk jobSummary command is correctly parsed and cut from the stream."""
    mock_job_summary_handler = MagicMock(return_value=None)
    # This regex is designed to capture the entire job summary block for the handler.
    # It's important that it matches the one in the application code.
    job_summary_command = Command(
        re.compile(r"\[\[([\s\S]*?jobSummary\s*=\s*\{[\s\S]*?\})[\s\S]*?\]\]"),
        mock_job_summary_handler
    )
    handler = StreamingChunkHandler(commands=[job_summary_command])

    # Reconstruct the chunk sequence from the log
    chunks = [
        "Perfect", "! I'll update", " the timing to after 7", "pm for you.\n\nHere",
        "'s your final job summary", ":\n\n| **Fiel", "d** | **Details", "** |\n|---|---|",
        "\n| **Job", " Headline** | Annual", " Gas Safety Check", " Required |\n",
        "\n\n[[\njobS", "ummary = {\n job", 'Headline: "Annual Gas Safety', ' Check Required",\n jobSubTitle',
        ': "Overdue Gas Safety Check - Maxol', ' Micro Turbo Boiler",\n job',
        'Details: "Seeking a Gas Safe registered engineer to perform', ' an overdue Annual Gas Safety Check on',
        ' a Maxol Micro turbo', ' boiler located in the kitchen at Flat 60 ',
        'Tudor Court. The boiler is currently working fine with no',
        ' issues reported. Last safety check was completed in January 2', '020 and found the appliance safe',
        ' with no defects. Check is now', ' overdue since January', ' 2021. Standard safety inspection an',
        'd certification required.",\n jobDate:', ' "Urgent (', 'within 1-2 weeks)",',
        '\n jobTimeOfDay: "', 'Weekday even', 'ings after 7pm"\n}', '\n]]\n\nAll'
    ]

    full_expected_text = (
        "Perfect! I'll update the timing to after 7pm for you.\n\n"
        "Here's your final job summary:\n\n"
        "| **Field** | **Details** |\n"
        "|---|---|\n"
        "| **Job Headline** | Annual Gas Safety Check Required |\n"
    )

    expected_job_summary_content = (
        '[[jobSummary = {\n jobHeadline: "Annual Gas Safety Check Required",\n '
        'jobSubTitle: "Overdue Gas Safety Check - Maxol Micro Turbo Boiler",\n '
        'jobDetails: "Seeking a Gas Safe registered engineer to perform an overdue Annual Gas Safety Check on a Maxol Micro turbo boiler located in the kitchen at Flat 60 Tudor Court. The boiler is currently working fine with no issues reported. Last safety check was completed in January 2020 and found the appliance safe with no defects. Check is now overdue since January 2021. Standard safety inspection and certification required.",\n '
        'jobDate: "Urgent (within 1-2 weeks)",\n '
        'jobTimeOfDay: "Weekday evenings after 7pm"\n}]]'
    )


    # Process all chunks
    output_text = ""
    for chunk in chunks:
        async for yielded_chunk in handler._process_text_and_commands_chunk(chunk):
            data = json.loads(yielded_chunk.split("data: ")[1])
            if data['type'] == 'content':
                output_text += data['data']


    # Check that the text before the command was yielded
    assert full_expected_text in output_text
    # Check that the command itself was NOT yielded
    assert "jobSummary" not in output_text
    # Check that the text after the command was yielded
    assert "All" in output_text
    # Check that the command handler was called once with the correct, full content
    mock_job_summary_handler.assert_called_once()
    # Extract the first argument from the call
    actual_call_arg = mock_job_summary_handler.call_args[0][0]
    # Normalize whitespace for a robust comparison
    assert "".join(actual_call_arg.split()) == "".join(expected_job_summary_content.split())
    # Check that the buffer is empty
    assert handler._buffer == ""

