
import logging
import traceback

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os

from starlette.responses import StreamingResponse

from src.agents.DiagnosticAgentStreaming import DiagnosticAgentStreaming
from src.dependencies_agents import get_diagnostic_agent_streaming
from src.schemas import MessageRequest

router = APIRouter(prefix="/message", tags=["message"])

security = HTTPBearer()
logger = logging.getLogger("uvicorn")


async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")

    try:
        # Get API key from AWS environment variable
        valid_api_key = os.environ.get("AI_ENGINE_API_KEY")

        if not valid_api_key:
            raise HTTPException(status_code=500, detail="API key configuration is missing")

        if credentials.credentials != valid_api_key:
            raise HTTPException(status_code=401, detail="Invalid API key")
        return credentials.credentials
    except <PERSON>TT<PERSON><PERSON><PERSON><PERSON> as he:
        raise he
    except Exception:
        raise HTTPException(status_code=401, detail="Could not validate credentials")


@router.post("/stream")
async def process_message_stream(
    request_data: MessageRequest, # Renamed to avoid conflict with FastAPI Request
    diagnostic_agent_streaming: DiagnosticAgentStreaming = Depends(get_diagnostic_agent_streaming),
    api_key: str = Depends(verify_api_key)
):
    logger.info(f"Received streaming request for chat: {request_data.chatId}, user: {request_data.userId}")

    async def stream_generator():
        try:
            stream = diagnostic_agent_streaming.process_next_message_stream(
                request_data.message.content,
                request_data.userId,
                request_data.chatId,
                request_data.attachments
            )
            async for chunk in stream:
                yield chunk

        except Exception as e:
            # Exception during the stream generation setup
            stacktrace = traceback.format_exc()
            logger.exception(f"Error setting up or during stream generation: {str(e)}\nStacktrace: {stacktrace}")
            raise HTTPException(status_code=500, detail="Streaming error")

    return StreamingResponse(stream_generator(), media_type="text/event-stream")

