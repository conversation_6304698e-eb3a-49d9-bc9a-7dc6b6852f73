from fastapi import APIRouter, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from src.agents.todos_agent import TodosAgent
from src.dependencies_agents import get_todos_agent
from src.routers.message import verify_api_key
from src.schemas import FindTodosRequest, FindTodosResponse

router = APIRouter(prefix="/todos", tags=["todos"])

security = HTTPBearer()


@router.post("/find", response_model=FindTodosResponse)
async def find_todos(
    request: FindTodosRequest,
    todos_agent: "TodosAgent" = Depends(get_todos_agent),
    api_key: str = Depends(verify_api_key)
):
    todos = await todos_agent.extract_todos(request.messages)
    return FindTodosResponse(todos=todos)
