import base64
import io
import logging
import os
import time
from pathlib import Path

import anthropic
from PIL import Image
from google import genai
from google.genai import types

from src.aiclients.GeminiClient import GeminiClient


class DocumentSummarizationService:
    def __init__(self):
        if "ANTHROPIC_API_KEY" not in os.environ:
            raise KeyError("ANTHROPIC_API_KEY environment variable is required")
        self.anthropic_api_key = os.environ["ANTHROPIC_API_KEY"]
        self.anthropic_client = anthropic.Anthropic(api_key=self.anthropic_api_key)
        if "GEMINI_API_KEY" not in os.environ:
            raise KeyError("GEMINI_API_KEY environment variable is required")
        self.gemini_api_key = os.environ["GEMINI_API_KEY"]

        self.gemini_client = GeminiClient()

        # Configure Gemini
        self.genai_client = genai.Client()
        # Set up logging
        self.logger = logging.getLogger("uvicorn")
        self.pdf_prompt = (
            "Describe the content of this document in detail, focusing on key information "
            "that would be useful for retrieval. Include specific details about any visible "
            "text, objects, or important information. This is a multi-page document, "
            "so please provide a comprehensive summary of all pages."
        )
        self.image_prompt = (
            "Describe the content of this image in detail, focusing on key information that "
            "would be useful for retrieval. Include specific details about any visible text, "
            "objects, or important information."
        )

    @staticmethod
    def encode_image(image: Image.Image) -> str:
        """Encode PIL Image to base64."""
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode("utf-8")

    @staticmethod
    def get_media_type(file_path):
        extension = file_path.lower().split(".")[-1]
        media_types = {
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "webp": "image/webp",
            "heic": "image/heic",
            "heif": "image/heif",
        }
        return media_types.get(extension)

    async def get_ai_short_description(self, document_description: str = None) -> str:
        self.logger.info(f"get_ai_short_description(): {document_description[:20]}")

        try:
            short_desc_prompt = (
                "Please generate one to eight words short description of the following content from a file:"
            )

            response = await self.genai_client.aio.models.generate_content(
                model="gemini-2.5-flash",
                contents=f"{short_desc_prompt}\n {document_description}",
                config=types.GenerateContentConfig(max_output_tokens=300000, top_p=0.8, temperature=0.4),
            )

            # Extract and return the description
            description = response.text

            self.logger.info(f"get_ai_short_description(): Successfully created short summary: {description}")
            return description

        except Exception as e:
            self.logger.exception(
                f"Error generating short summary with get_ai_short_description(): "
                f"{document_description[:20]}: {str(e)}"
            )
            raise

    async def process_file_gemini(self, file_path: Path, describing_llm_prompt: str = None) -> str:
        """Process a file using Gemini asynchronously based on file type.

        Args:
            file_path (Path): Path to the file
            describing_llm_prompt (str, optional): Custom prompt for file analysis
            number_of_pages_to_include
        Returns:
            str: Description of the file content

        Raises:
            FileNotFoundError: If the file doesn't exist
            Exception: If there's an error processing the file
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Process based on file type
        start_time = time.time()
        if file_path.suffix.lower() == ".pdf":
            prompt = describing_llm_prompt if describing_llm_prompt else self.pdf_prompt
            description = await self.gemini_client.process_pdf_gemini_flash(file_path, prompt)
        else:
            prompt = describing_llm_prompt if describing_llm_prompt else self.image_prompt
            media_type = self.get_media_type(str(file_path))
            description = await self.gemini_client.process_image_gemini_flash(file_path, media_type, prompt)

        processing_time = time.time() - start_time
        self.logger.info(f"Completed processing {file_path} with Gemini in {processing_time:.2f} seconds")

        return description
