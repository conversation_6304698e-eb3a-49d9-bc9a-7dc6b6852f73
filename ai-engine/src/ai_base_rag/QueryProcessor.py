from pathlib import Path
from typing import List, Dict, Optional
import logging
from datetime import datetime
from qdrant_client import QdrantClient
from openai import OpenAI
import pandas as pd
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_community.document_loaders import PyPDFLoader
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain.agents import create_openai_tools_agent
from langchain.agents import AgentExecutor
from langchain.tools import Tool
import json
import os, re
from langchain_core.prompts import MessagesPlaceholder
import configparser


class QueryProcessor:
    def __init__(self,
                 qdrant_path: str = "qdrant_storage",
                 openai_api_key: str = None,
                 serpapi_api_key: str = None):
        """
        Initialize the query processor.

        Args:
            qdrant_path: Path to stored Qdrant index
            openai_api_key: OpenAI API key
            serpapi_api_key: Serp<PERSON>I key for web searches
        """
        self.qdrant_path = Path(qdrant_path)
        self.collection_name = "documents"
        self.openai_client = OpenAI(api_key=openai_api_key)

        # Set up logging
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        log_file = log_dir / f"query_processor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        self.logger.info(f"QueryProcessor construtor...")

        # Initialize Qdrant client
        self.load_index()

        # Initialize LangChain components
        self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        self.search = DuckDuckGoSearchRun()

    def load_index(self):
        """Load the Qdrant index from disk."""
        self.logger.info(f"Loading Qdrant index from: {self.qdrant_path}")

        try:
            if not self.qdrant_path.exists():
                raise FileNotFoundError(f"Qdrant storage not found at {self.qdrant_path}")

            self.qdrant_client = QdrantClient(path=str(self.qdrant_path))

            # Verify collection exists and log stats
            collection_info = self.qdrant_client.get_collection(self.collection_name)
            self.logger.info(f"Successfully loaded index with {collection_info.points_count} points")

        except Exception as e:
            self.logger.exception(f"Error loading index: {str(e)}")
            raise

    def query_index(self, query: str, limit: int = 5):
        """Query the Qdrant index and return top matching files."""
        self.logger.info(f"Querying index with: {query}")

        try:
            # Get query embedding
            query_embedding = self.openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=query
            ).data[0].embedding

            # Search in Qdrant
            results = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit
            )

            # Load file mapping
            mapping_df = pd.read_csv('file_mapping.csv')
            mapping_dict = dict(zip(mapping_df['text_file'], mapping_df['original_file']))

            # Format results
            formatted_results = [
                {
                    'score': result.score,
                    'text_file': result.payload['file_name'],
                    'original_file': mapping_dict[result.payload['file_name']],
                    'content': result.payload['content']
                }
                for result in results
            ]

            self.logger.info(f"Found {len(formatted_results)} results")
            return formatted_results

        except Exception as e:
            self.logger.exception(f"Error querying index: {str(e)}")
            raise

    def select_relevant_documents(self, query: str, results: List[Dict]) -> List[str]:
        """Use GPT-4 to select relevant documents for the query."""
        self.logger.info("Selecting relevant documents with GPT-4")

        documents_info = "\n\n".join([
            f"Document {i + 1}:\nFile: {r['original_file']}\nContent Summary: {r['content']}"
            for i, r in enumerate(results)
        ])

        messages = [
            {
                "role": "system",
                "content": """You are an expert at analyzing document relevance for question answering. 
                Your task is to select the most relevant documents that would help answer a given question.
                Return your response as a JSON object with a single key 'selected_documents' containing an array of document numbers.
                Example response: {"selected_documents": [1, 3]}"""
            },
            {
                "role": "user",
                "content": f"Question: {query}\n\nAvailable Documents:\n{documents_info}\n\nSelect the document numbers that would be most helpful in answering this question. Return only the JSON array of numbers."
            }
        ]

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                response_format={"type": "json_object"}
            )

            # Parse the response
            response_content = response.choices[0].message.content
            self.logger.info(f"GPT-4 response: {response_content}")

            selected_docs = json.loads(response_content).get('selected_documents', [])
            self.logger.info(f"Parsed selected document indices: {selected_docs}")

            # Get the original file paths (adjusting for 0-based indexing)
            selected_files = [results[i - 1]['original_file'] for i in selected_docs]

            self.logger.info(f"Selected {len(selected_files)} relevant documents: {selected_files}")
            return selected_files

        except Exception as e:
            self.logger.exception(f"Error in document selection: {str(e)}")
            # Return empty list in case of error to allow graceful degradation
            return []

    def create_agent(self, selected_files: List[str], query: str) -> str:
        """Create and run a LangChain agent to answer the query using selected documents."""
        self.logger.info("Creating LangChain agent")

        try:
            # Load PDFs
            documents = []
            for file_path in selected_files:
                loader = PyPDFLoader(file_path)
                documents.extend(loader.load())

            # Create tools
            tools = [
                Tool(
                    name="Search",
                    func=self.search.run,
                    description="Useful for finding current prices and service costs online."
                )
            ]

            # Create document context
            context = "\n\n".join([doc.page_content for doc in documents])

            # Create the agent prompt with updated format
            prompt = ChatPromptTemplate.from_messages([
                ("system", """You are a helpful assistant with access to both document content and web search capabilities. 
                Use the provided document content as your primary source of information, and supplement with web searches 
                when needed, especially for current prices and costs. Always explain your reasoning and cite your sources.

                Document Context: {context}"""),
                ("user", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ])

            # Create the agent
            agent = create_openai_tools_agent(
                llm=self.llm,
                tools=tools,
                prompt=prompt
            )

            # Create the executor
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True
            )

            # Run the agent
            self.logger.info("Running agent to answer query")
            response = agent_executor.invoke({
                "context": context,
                "input": query
            })

            return response["output"]

        except Exception as e:
            self.logger.exception(f"Error in agent creation/execution: {str(e)}")
            raise

    def process_query(self, query: str) -> str:
        """Process a query through the entire pipeline."""
        self.logger.info(f"Processing query: {query}")

        try:
            # 1. Query the vector index
            results = self.query_index(query)

            # 2. Select relevant documents
            selected_files = self.select_relevant_documents(query, results)

            if not selected_files:
                return "I couldn't find any relevant documents to answer your question. Could you please rephrase or provide more context?"

            # 3. Create and run agent
            answer = self.create_agent(selected_files, query)

            import textwrap

            final_answer = textwrap.dedent(f"""
                Question: {query}
                Files selected by Supervisor RAG LLM: {str(selected_files)}
                ==================================================
                Answer: {answer}
            """).strip()


            return final_answer

        except Exception as e:
            self.logger.exception(f"Error processing query: {str(e)}")
            raise

    def sanitize_filename(self, query):
        """
        Convert query to a valid filename by:
        1. Removing question marks
        2. Replacing spaces with underscores
        3. Removing any other invalid filename characters
        """
        # Remove question mark
        query = query.replace('?', '')

        # Replace spaces with underscores
        query = query.replace(' ', '_')

        # Remove any other invalid filename characters
        # Keep only alphanumeric characters, underscores, and hyphens
        query = re.sub(r'[^a-zA-Z0-9_-]', '', query)

        return query

    def save_answer(self, query, answer, directory='answers'):
        """
        Save the answer to a file named after the sanitized query
        """
        filename = self.sanitize_filename(query)
        filepath = os.path.join(directory, f"{filename}.txt")

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(answer)

    def process_questions(self, questions):
        """
        Process each question and save its answer
        """
        # Create answers directory if it doesn't exist
        if not os.path.exists('answers'):
            os.makedirs('answers')

        for query in questions:
            try:
                # Process the query
                answer = self.process_query(query)

                # Save the answer
                self.save_answer(query, answer)
                print(f"Processed and saved answer for: {query}")

            except Exception as e:
                print(f"Error processing query '{query}': {str(e)}")


# Example usage
if __name__ == "__main__":
    # Create a ConfigParser object
    # ssh -i .ssh/piotr-poc-vm-keypair.pem ubuntu@35.179.12.136
    config = configparser.ConfigParser()

    # Read the config file
    config.read("./config/ai-base-rag.config")
    api_key = config['OpenAI'].get('API_KEY', None)
    os.environ["OPENAI_API_KEY"] = api_key

    processor = QueryProcessor(
        qdrant_path="qdrant_storage",
        openai_api_key=api_key
    )

    query = "How much would it costs to replace all my windows?"
    answer = processor.process_query(query)
    print(f"\nAnswer: {answer}")

    property_questions = [
        "Can you provide an overview of the purchase process for this property?",
        "Generate a property maintenance checklist for me with frequencies for each task",
        "Please suggest ways to improve the energy efficiency of my home",
        "How would you describe the style of my property?",
        "What are the key issues raised in the property survey that I should be aware of?",
        "Please give me an overview of my energy usage",
        "Help me plan how to wall mount my TV",
        "How much would it costs to replace all my windows?",
        "How much would it costs to add a basement extension to my property?",
        "What washing machine do I have?",
        "What does F13 error code on my washing machine mean?",
        "Who is my water utility provider?",
        "How much am I spending on my water utility?",
        "Who do I have home insurance with?",
        "When does my home insurance policy expire?",
        "Do I have double glazed windows in my property?",
        "What model Thermomix do I have?"
    ]

    processor.process_questions(property_questions)

