from pathlib import Path
from typing import List, Dict
import pandas as pd
from openai import OpenAI
import base64
import mimetypes
import csv
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import numpy as np
import pdf2image
import io
from PIL import Image
import logging
import time, configparser
from datetime import datetime


class DocumentProcessor:
    def __init__(self, input_directory: str, output_directory: str = "text_descriptions",
                 openai_api_key: str = None, qdrant_path: str = "qdrant_storage"):
        """
        Initialize the document processor.

        Args:
            input_directory: Directory to scan for PDFs and images
            output_directory: Directory to store text descriptions
            openai_api_key: OpenAI API key
            qdrant_path: Path to store Qdrant index
        """
        self.input_directory = Path(input_directory)
        self.output_directory = Path(output_directory)
        self.output_directory.mkdir(exist_ok=True)
        self.client = OpenAI(api_key=openai_api_key)
        self.qdrant_client = QdrantClient(path=qdrant_path)
        self.collection_name = "documents"

        # Set up logging
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        log_file = log_dir / f"document_processor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def find_files(self) -> List[Path]:
        """Recursively find all PDFs and images in the input directory."""
        extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp'}
        files = []

        for ext in extensions:
            files.extend(self.input_directory.rglob(f"*{ext}"))

        self.logger.info(f"Found {len(files)} files to process")
        return files

    def encode_image(self, image: Image.Image) -> str:
        """Encode PIL Image to base64."""
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode('utf-8')

    def process_pdf(self, pdf_path: Path) -> str:
        """Convert PDF to images and get description."""
        self.logger.info(f"Converting PDF to images: {pdf_path}")

        try:
            # Convert PDF to images
            images = pdf2image.convert_from_path(pdf_path)
            self.logger.info(f"Converted PDF to {len(images)} pages")

            # Prepare messages for GPT-4
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Describe the content of this document in detail, focusing on key information that would be useful for retrieval. Include specific details about any visible text, objects, or important information. This is a multi-page document, so please provide a comprehensive summary of all pages."
                        }
                    ]
                }
            ]

            # Add each page as an image
            for i, image in enumerate(images):
                encoded_image = self.encode_image(image)
                messages[0]["content"].append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{encoded_image}"
                    }
                })

            self.logger.info(f"Sending PDF pages to GPT-4 for analysis")

            # Get description from GPT-4
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.exception(f"Error processing PDF {pdf_path}: {str(e)}")
            raise

    def process_image(self, image_path: Path) -> str:
        """Process a single image file."""
        self.logger.info(f"Processing image: {image_path}")

        try:
            # Open and encode image
            with Image.open(image_path) as img:
                encoded_image = self.encode_image(img)

            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Describe the content of this image in detail, focusing on key information that would be useful for retrieval. Include specific details about any visible text, objects, or important information."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{encoded_image}"
                            }
                        }
                    ]
                }
            ]

            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.exception(f"Error processing image {image_path}: {str(e)}")
            raise

    def process_files(self) -> Dict[str, str]:
        """Process all files and return mapping of text files to original files."""
        file_mapping = {}
        files = self.find_files()

        for file_path in files:
            try:
                self.logger.info(f"Starting to process: {file_path}")
                start_time = time.time()

                # Process based on file type
                if file_path.suffix.lower() == '.pdf':
                    description = self.process_pdf(file_path)
                else:
                    description = self.process_image(file_path)

                # Save description
                txt_filename = file_path.stem + '.txt'
                txt_path = self.output_directory / txt_filename

                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(description)

                file_mapping[txt_filename] = str(file_path)

                processing_time = time.time() - start_time
                self.logger.info(f"Completed processing {file_path} in {processing_time:.2f} seconds")

            except Exception as e:
                self.logger.exception(f"Failed to process {file_path}: {str(e)}")
                continue

        # Save mapping to CSV
        mapping_df = pd.DataFrame(list(file_mapping.items()),
                                  columns=['text_file', 'original_file'])
        mapping_df.to_csv('file_mapping.csv', index=False)

        self.logger.info(f"Completed processing all files. Mapping saved to file_mapping.csv")
        return file_mapping

    def build_index(self):
        """Build Qdrant index from text descriptions."""
        self.logger.info("Starting to build vector index")

        try:
            # Create or recreate collection
            self.qdrant_client.recreate_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=1536, distance=Distance.COSINE),
                on_disk_payload=True  # Explicitly store payloads on disk
            )

            # Process each text file
            text_files = list(self.output_directory.glob('*.txt'))
            self.logger.info(f"Found {len(text_files)} text files to index")

            for txt_file in text_files:
                try:
                    with open(txt_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Get embedding from OpenAI
                    embedding = self.client.embeddings.create(
                        model="text-embedding-3-small",
                        input=content
                    ).data[0].embedding

                    # Add to Qdrant
                    self.qdrant_client.upsert(
                        collection_name=self.collection_name,
                        points=[
                            PointStruct(
                                id=hash(str(txt_file)),
                                payload={"file_name": txt_file.name, "content": content},
                                vector=embedding
                            )
                        ]
                    )
                    self.logger.info(f"Indexed {txt_file.name}")

                except Exception as e:
                    self.logger.exception(f"Error indexing {txt_file}: {str(e)}")
                    continue

            self.logger.info("Completed building index")

        except Exception as e:
            self.logger.exception(f"Error building index: {str(e)}")
            raise


    def query_index(self, query: str, limit: int = 5):
        """Query the index and return top matching files."""
        self.logger.info(f"Querying index with: {query}")

        try:
            # Get query embedding
            query_embedding = self.client.embeddings.create(
                model="text-embedding-3-small",
                input=query
            ).data[0].embedding

            # Search in Qdrant
            results = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit
            )

            # Load file mapping
            mapping_df = pd.read_csv('file_mapping.csv')
            mapping_dict = dict(zip(mapping_df['text_file'], mapping_df['original_file']))

            # Format results
            formatted_results = [
                {
                    'score': result.score,
                    'text_file': result.payload['file_name'],
                    'original_file': mapping_dict[result.payload['file_name']],
                    'content': result.payload['content']
                }
                for result in results
            ]

            self.logger.info(f"Found {len(formatted_results)} results")
            return formatted_results

        except Exception as e:
            self.logger.exception(f"Error querying index: {str(e)}")
            raise


# Example usage
if __name__ == "__main__":

    config = configparser.ConfigParser()

    # Read the config file
    config.read("./config/ai-base-rag.conf")
    api_key = config['OpenAI'].get('API_KEY', None)
    #os.environ["OPENAI_API_KEY"] = api_key

    processor = DocumentProcessor(
        input_directory="./data",
        openai_api_key=api_key
    )

    # install apt-get install poppler-utils
    processor.process_files()

    # gpt-4-turbo
    # Process files
    #file_mapping = processor.process_files()

    # Build index
    processor.build_index()

    # Query example
    results = processor.query_index("What model of TV do I have?")
    for result in results:
        print(f"Score: {result['score']}")
        print(f"File: {result['original_file']}")
        print(f"Content: {result['content']}\n")