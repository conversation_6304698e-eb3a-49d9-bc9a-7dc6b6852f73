from datetime import datetime
from enum import Enum
from typing import List, Optional

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.ext.associationproxy import AssociationProxy
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class PropertyType(Enum):
    house = "house"
    flat = "flat"
    office = "office"
    retail = "retail"


class PropertySubgroupType(Enum):
    detached = "detached"
    semiDetached = "semiDetached"
    terraced = "terraced"
    NOT_APPLICABLE = "NOT_APPLICABLE"


class PropertyTenureType(Enum):
    freehold = "freehold"
    leasehold = "leasehold"
    shareOfFreehold = "shareOfFreehold"
    NOT_APPLICABLE = "NOT_APPLICABLE"


class PropertyConditionType(Enum):
    excellent = "excellent"
    good = "good"
    fair = "fair"
    poor = "poor"


class PropertyConservationStatusType(Enum):
    gradeI = "gradeI"
    gradeIIstar = "gradeII*"
    gradeII = "gradeII"
    conservationArea = "conservationArea"
    buildingPreservationNotice = "buildingPreservationNotice"
    localHeritageList = "localHeritageList"
    other = "other"


class PropertyEpcRatingType(Enum):
    a = "A"
    b = "B"
    c = "C"
    d = "D"
    e = "E"
    f = "F"
    g = "G"


class Property(BaseModel):
    __tablename__ = "properties"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[Optional[PropertyType]]
    subgroupType: Mapped[Optional[PropertySubgroupType]]
    tenureType: Mapped[Optional[PropertyTenureType]]
    # details:
    sizeInSqft: Mapped[Optional[int]]  # gross internal area
    onFloorLevel: Mapped[Optional[int]]
    hasBalconyTerrace: Mapped[Optional[bool]]
    balconyTerraceDetails: Mapped[Optional[str]]
    hasGarden: Mapped[Optional[bool]]
    gardenDetails: Mapped[Optional[str]]
    hasSwimmingPool: Mapped[Optional[bool]]
    swimmingPoolDetails: Mapped[Optional[str]]
    numberOfBedrooms: Mapped[Optional[int]]
    numberOfBathrooms: Mapped[Optional[int]]
    numberOfFloors: Mapped[Optional[int]]
    lastSoldPriceInGbp: Mapped[Optional[int]]
    condition: Mapped[Optional[PropertyConditionType]]
    yearsOfOwnership: Mapped[Optional[int]]
    architecturalType: Mapped[Optional[str]]
    valuationInGbp: Mapped[Optional[int]]
    conservationStatus: Mapped[Optional[PropertyConservationStatusType]]
    typeOfLock: Mapped[Optional[str]]
    typeOfConstruction: Mapped[Optional[str]]
    proportionOfFlatRoof: Mapped[Optional[int]]  # percentage
    epcRating: Mapped[Optional[PropertyEpcRatingType]]
    # relationships:
    floorPlanDocumentId: Mapped[Optional[int]]
    floorPlanDocument: Mapped["Document"] = relationship(back_populates="property", viewonly=True)
    propertySurveyDocumentId: Mapped[Optional[int]]
    propertySurveyDocument: Mapped["Document"] = relationship(back_populates="property", viewonly=True)
    epcCertificateDocumentId: Mapped[Optional[int]]
    epcCertificateDocument: Mapped["Document"] = relationship(back_populates="property", viewonly=True)
    buildingId: Mapped[Optional[int]] = mapped_column(ForeignKey("buildings.id"))
    building: Mapped["Building"] = relationship(back_populates="properties", lazy="selectin", cascade="all, delete")
    addressId: Mapped[Optional[int]] = mapped_column(ForeignKey("addresses.id"))
    address: Mapped["Address"] = relationship(back_populates="property", lazy="selectin", cascade="all, delete")
    usersProperties: Mapped[List["UsersProperties"]] = relationship(
        back_populates="property", lazy="selectin", cascade="all, delete"
    )
    users: AssociationProxy[List["User"]] = association_proxy("usersProperties", "user")
    documents: Mapped[List["Document"]] = relationship(back_populates="property")
    appliances: Mapped[List["Appliance"]] = relationship(back_populates="property")
    chats: Mapped[List["Chat"]] = relationship(back_populates="property")
    jobs: Mapped[List["Job"]] = relationship(back_populates="property")
    projects: Mapped[List["Project"]] = relationship(back_populates="property")
    bills: Mapped[List["Bill"]] = relationship(back_populates="property")
    insurances: Mapped[List["Insurance"]] = relationship(back_populates="property")
    legals: Mapped[List["Legal"]] = relationship(back_populates="property")
