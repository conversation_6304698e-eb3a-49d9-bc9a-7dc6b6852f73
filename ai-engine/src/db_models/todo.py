from datetime import datetime
from enum import StrEnum
from typing import Optional

from sqlalchemy import Integer, ForeignKey, DateTime, String
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel


class TodoType(StrEnum):
    userCreated = "userCreated"
    systemCreated = "systemCreated"
    systemAccepted = "systemAccepted"
    systemCreatedUserAccepted = "systemCreatedUserAccepted"
    systemCreatedUserRejected = "systemCreatedUserRejected"


class Todo(BaseModel):
    __tablename__ = "todos"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[TodoType]
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    dueDate: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    doneDate: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    deletedDate: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="todos")
