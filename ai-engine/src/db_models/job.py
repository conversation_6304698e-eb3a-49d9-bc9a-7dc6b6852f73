from datetime import datetime
from typing import Optional, List

from sqlalchemy import DateTime, <PERSON><PERSON><PERSON>, Integer, Enum, String, text
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql import func

from .base import BaseModel
from .relationships import jobs_professionals


class Job(BaseModel):
    __tablename__ = "jobs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    reference: Mapped[str] = mapped_column(
        String(10), unique=True, index=True, nullable=False,
        server_default=text("UPPER(LEFT(TRANSLATE(MD5(RANDOM()::TEXT || CLOCK_TIMESTAMP()::TEXT), '0o', ''), 8))")
    )
    headline: Mapped[str]
    subTitle: Mapped[str]
    details: Mapped[str]
    urgency: Mapped[str]
    availability: Mapped[Optional[str]]  # Later on based on specific calendar dates/ranges
    status: Mapped[str] = mapped_column(
        Enum(
            "incomplete",  # for the future
            "created",
            "user_accepted",
            "quoting_started",
            "quoting_completed",
            "more_quotes_requested",
            "booked",
            "completed",
            "disputed",
            "paid",
            "cancelled",
            name="job_status",
        ),
        default="created",
    )
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)
    projectId: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    project: Mapped["Project"] = relationship(back_populates="jobs")
    chatId: Mapped[int] = mapped_column(ForeignKey("chats.id"))
    chat: Mapped["Chat"] = relationship(back_populates="jobs")
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="jobs", lazy="selectin")
    propertyId: Mapped[Optional[int]] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="jobs")
    quotes: Mapped[List["Quote"]] = relationship(back_populates="job", lazy="selectin")
    serviceProviderId: Mapped[Optional[int]] = mapped_column(ForeignKey("service_providers.id"))
    serviceProvider: Mapped[Optional["ServiceProvider"]] = relationship(back_populates="jobs")
    professionals: Mapped[List["Professional"]] = relationship(
        "Professional", secondary=jobs_professionals, back_populates="jobs", lazy="selectin"
    )
    hubspotId: Mapped[Optional[str]] = mapped_column(String, unique=True, index=True, nullable=True)
    hubspotSyncAt: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
