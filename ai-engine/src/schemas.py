from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Optional, Literal, Union

# Define Literal types
MessageType = Literal["text", "diagnostic_report"]
ActionType = Literal["button", "image_url"]
StatusType = Literal["success", "error"]
DocumentType = Literal["pdf", "jpg", "png", "gif", "webp", "jpeg", "heic", "heif"]


class Message(BaseModel):
    content: str
    type: MessageType


class ChatMessage(BaseModel):
    content: str
    senderType: str


class SuggestedAction(BaseModel):
    type: ActionType
    label: str
    action: Literal["click"] = "click"


class ImageUrl(BaseModel):
    imageUrl: str
    description: Optional[str] = None
    source: Optional[str] = None


class JobSummary(BaseModel):
    jobId: Optional[int] = None
    jobHeadline: str = ""
    jobSubTitle: str = ""
    jobDetails: str = ""
    jobDate: str = ""
    jobTimeOfDay: str = ""
    messageContainingJobSummary: Optional[str] = None
    errorDuringParsing: Optional[str] = None


class ResponseAdditionalData(BaseModel):
    category: Optional[str] = None
    confidence: Optional[float] = None
    suggestedActions: Optional[List[SuggestedAction]] = None
    imageUrls: Optional[List[ImageUrl]] = None
    imageClickableUrls: Optional[List[ImageUrl]] = None
    jobSummary: Optional[JobSummary] = None


class ErrorDetail(BaseModel):
    code: str
    message: str
    details: Optional[dict] = None


class ParseFileRequest(BaseModel):
    documentId: int
    documentS3Url: str
    userId: int
    documentS3Key: str
    documentS3Bucket: str
    type: DocumentType
    base64: Optional[str] = None  # for debugging purposes
    context: Optional[str] = None  # e.g. "appliances" etc.


class MessageRequest(BaseModel):
    message: Message
    messageId: int
    chatId: int  # Required
    userId: int
    attachments: Optional[List[ParseFileRequest]] = None


class ParseFileResponse(BaseModel):
    status: Literal["success", "error"]
    errorMessage: Optional[str]


class ResponseData(BaseModel):
    chatId: int
    response: Message
    additionalData: ResponseAdditionalData


class MessageResponse(BaseModel):
    status: Literal["success", "error"]
    data: Optional[ResponseData] = None
    error: Optional[ErrorDetail] = None


class RelevantDocumentClassification(BaseModel):
    category: Optional[str] = Field(..., description="The category of the document")
    label: Optional[str] = Field(..., description="The specific label for the document")
    isNewLabel: Optional[bool] = Field(..., description="Whether this is a new label not in the predefined list")


class NotRelevantDocumentClassification(BaseModel):
    category: Optional[str] = Field(
        "NOT_RELEVANT",
        description="Indicates the document is not relevant to property management",
    )


class FindTodo(BaseModel):
    title: str
    description: str | None
    dueDate: datetime | None


class FindTodosRequest(BaseModel):
    userId: int
    chatId: int
    messages: list[ChatMessage]


class FindTodosResponse(BaseModel):
    todos: list[FindTodo]


class DocumentClassification(BaseModel):
    """Union type for document classification results"""

    @classmethod
    def from_dict(cls, data: dict) -> Union[RelevantDocumentClassification, NotRelevantDocumentClassification]:
        if data.get("category") == "NOT_RELEVANT":
            return NotRelevantDocumentClassification(**data)
        else:
            return RelevantDocumentClassification(**data)
