[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = {extras = ["standard"] }
fastapi-pagination = "*"
uvicorn = "*"
asyncpg = "*"
greenlet = "*"
alembic = "*"
sqlalchemy = {extras = ["asyncio"] }
sentry-sdk = {extras = ["fastapi"], version = "*"}
aiohttp = "*"
pyproj = "*"
langchain-qdrant = "*"
langchain-community = "*"
langchain-openai = "*"
tiktoken = "*"
langchain = "*"
langchain-core = "*"
qdrant-client = "*"
pandas = "*"
pdf2image = "*"
python-dotenv = "*"
duckduckgo-search = "*"
pydantic = "*"
typing = "*"
anthropic = "*"
openai="*"
boto3 = "*"
together = "*"
requests = "*"
langchain_together ="*"
langsmith="*"
beautifulsoup4="*"
python-magic="*"
aiofiles = "*"
google-genai="*"
langchain-anthropic="*"

[dev-packages]
pytest = "*"
faker = "*"
black = "*"
flake8 = "*"
pytest-cov = "*"
tox = "*"
psycopg = {extras = ["binary"], version = "*"}
aioresponses = "*"
pytest-asyncio = "*"
pytest-mock = "*"

[requires]
python_version = "3.12"
#python_full_version = "3.12.6"
