# Data Cleanup Debug

## Problem

After sign out, `guestAuth` was not being cleared properly, causing old guest chats to remain visible.

## Debug Steps

### 1. Check if AuthStateWatcher is working

Open browser console and look for these logs when you sign out:

```
🔍 AuthStateWatcher: isSignedIn changed to: false was: true
🚪 User signed out detected! Clearing data...
```

### 2. Check if clearAllUserData is called

Look for these logs:

```
🧹 Starting clearAllUserData...
📊 Before clearing - guestAuth: {userId: 11279, ...}
🔄 Calling auth store logout...
🔄 Calling clearGuestAuth...
📊 After clearing - guestAuth: null
✅ User data cleared successfully
```

### 3. Manual testing

```javascript
// Check current state
window.testCleanup.checkState();

// Manual clear (for testing)
await window.testCleanup.manualClear();

// Check state after manual clear
window.testCleanup.checkState();
```

### 4. Expected Results

After sign out, you should see:

- `guestAuth is NULL ✅`
- `chats count: 0`
- `localStorage auth-storage: {"guestAuth":null}` or `null`

## If Still Not Working

1. Check browser console for error messages
2. Verify AuthStateWatcher is mounted in layout.tsx
3. Check if sign out actually triggers isSignedIn change
4. Try manual clear to isolate the issue
