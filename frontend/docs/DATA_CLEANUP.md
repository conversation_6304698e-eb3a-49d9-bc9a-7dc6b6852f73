# Data Cleanup on Sign Out

## Overview
Automatic data cleanup when user signs out, using SDK methods only (no direct localStorage manipulation).

## Implementation

### 1. Clear User Data Utility (`src/utils/clearUserData.ts`)
- Uses `mutate()` from SWR to clear cache
- Uses Zustand store methods (`logout()`, `clearRedirectUrl()`, `setState()`)
- Uses store `reset()` methods where available
- Creates new guest user after cleanup

### 2. Sign Out Handler (`src/components/ClerkSignOutHandler/ClerkSignOutHandler.tsx`)
- Detects when `isSignedIn` changes from `true` to `false`
- Automatically calls `clearAllUserData()` after sign out
- Added to app layout for global coverage

## What Gets Cleared

### SWR Cache
- All cached API responses via `mutate(() => true, undefined, { revalidate: false })`

### Zustand Stores
- **Auth Store**: `useAuthStore.getState().logout()`
- **Redirect Store**: `useRedirectQueryUrl.getState().clearRedirectUrl()`
- **Other Stores**: Reset to initial state via `setState()`
  - Chats, Widgets, Messages, Appliances, Onboarding, Streaming, Conversion, Sidebar, Modal

### New Guest User
- Automatically creates new guest user via `createGuestUser()` API

## Testing

### Browser Console Commands
```javascript
// Add test data
window.testCleanup.addTestData();

// Clear all data
await window.testCleanup.clearData();

// Check if data was cleared
window.testCleanup.checkDataCleared();

// Run full test
await window.testCleanup.runTest();
```

### Manual Test Flow
1. Sign in as user
2. Add chats, properties, addresses
3. Sign out via UserButton
4. Check console logs for cleanup confirmation
5. Verify new guest user is created
6. Start new chat - should be clean

## Key Benefits
- ✅ Uses SDK methods only (no direct localStorage access)
- ✅ Automatic trigger on Clerk sign out
- ✅ Clean separation of concerns
- ✅ Reliable data isolation between sessions
