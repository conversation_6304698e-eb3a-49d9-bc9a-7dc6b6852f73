# Data Cleanup Fix

## Problem
After sign out, `guestAuth` was not being cleared properly, causing old guest chats to remain visible.

## Solution
- Clear all data including `guestAuth` on sign out
- Don't create new guest user immediately after sign out
- Let guest user be created when actually needed

## Testing
```javascript
// Check current state
window.testCleanup.checkState();
```

## Expected Flow
1. Guest creates chat → has guestAuth with userId
2. User signs up/in → becomes authenticated user
3. User signs out → guestAuth should be null, chats should be empty
4. New guest session → clean start, no old data
