/**
 * Test utility to check data cleanup after sign out
 */

import { useAuthStore } from '@/stores/auth.store';
import { useChats } from '@/hooks/useChats';
import { clearAllUserData } from './clearUserData';

/**
 * Check current state
 */
export const checkCurrentState = () => {
  const authState = useAuthStore.getState();
  const chatsState = useChats.getState();

  console.log('📊 Current State:');
  console.log('- isAuthenticated:', authState.isAuthenticated);
  console.log('- user:', authState.user);
  console.log('- guestAuth:', authState.guestAuth);
  console.log('- chats count:', chatsState.chats.length);

  if (authState.guestAuth) {
    console.log('- guestAuth userId:', authState.guestAuth.userId);
    console.log(
      '- guestAuth token (first 10 chars):',
      authState.guestAuth.token.substring(0, 10) + '...'
    );
    console.log('- guestAuth expiresAt:', authState.guestAuth.expiresAt);
  } else {
    console.log('- guestAuth is NULL ✅');
  }

  // Check localStorage
  const authStorage = localStorage.getItem('auth-storage');
  console.log('- localStorage auth-storage:', authStorage);

  return {
    isAuthenticated: authState.isAuthenticated,
    hasUser: !!authState.user,
    hasGuestAuth: !!authState.guestAuth,
    guestUserId: authState.guestAuth?.userId,
    chatsCount: chatsState.chats.length,
    localStorageAuth: authStorage,
  };
};

/**
 * Manual clear for testing
 */
export const manualClear = async () => {
  console.log('🧪 Manual clear triggered...');
  await clearAllUserData();
  console.log('🧪 Manual clear completed');
};

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).testCleanup = {
    checkState: checkCurrentState,
    manualClear: manualClear,
  };
}
