/**
 * Simple test utility to verify data cleanup works correctly
 */

import { useAuthStore } from '@/stores/auth.store';
import { useChats } from '@/hooks/useChats';
import { useWidgets } from '@/hooks/useWidgets';
import { clearAllUserData } from './clearUserData';

/**
 * Add some test data to stores
 */
export const addTestData = () => {
  console.log('📝 Adding test data...');

  // Add test data to auth store
  useAuthStore.getState().setGuestAuth({
    token: 'test-token-123',
    userId: 999,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  });

  // Add test data to chats
  useChats.setState({
    chats: [
      {
        id: 1,
        title: 'Test Chat',
        createdAt: new Date().toISOString(),
        messages: [],
        status: 'active' as any,
      },
    ],
  });

  // Add test data to widgets
  useWidgets.setState({
    properties: [
      {
        id: 1,
        address: {
          id: 1,
          streetLine1: 'Test Street 123',
          streetLine2: '',
          townOrCity: 'Test City',
          postcode: 'TE5T 1NG',
          houseAccess: null,
          parkingInstructions: null,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as any,
    ],
  });

  console.log('✅ Test data added');
};

/**
 * Check if data was cleared
 */
export const checkDataCleared = () => {
  console.log('🔍 Checking if data was cleared...');

  const authState = useAuthStore.getState();
  const chatsState = useChats.getState();
  const widgetsState = useWidgets.getState();

  const results = {
    authCleared: !authState.guestAuth || authState.guestAuth.token !== 'test-token-123',
    chatsCleared: chatsState.chats.length === 0,
    widgetsCleared: widgetsState.properties.length === 0,
  };

  console.log('Results:', results);

  const allCleared = Object.values(results).every(Boolean);
  console.log(allCleared ? '✅ All data cleared successfully!' : '❌ Some data was not cleared');

  return results;
};

/**
 * Run full test
 */
export const runTest = async () => {
  console.log('🧪 Running data cleanup test...');

  // Step 1: Add test data
  addTestData();

  // Step 2: Clear all data
  await clearAllUserData();

  // Step 3: Check results
  const results = checkDataCleared();

  return results;
};

// Make functions available in browser console for manual testing
if (typeof window !== 'undefined') {
  (window as any).testCleanup = {
    addTestData,
    clearData: clearAllUserData,
    checkDataCleared,
    runTest,
  };
}
