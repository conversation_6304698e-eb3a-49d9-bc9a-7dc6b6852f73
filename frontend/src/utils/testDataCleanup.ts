/**
 * Test utility to check data cleanup after sign out
 */

import { useAuthStore } from '@/stores/auth.store';
import { useChats } from '@/hooks/useChats';

/**
 * Check current state
 */
export const checkCurrentState = () => {
  const authState = useAuthStore.getState();
  const chatsState = useChats.getState();

  console.log('📊 Current State:');
  console.log('- isAuthenticated:', authState.isAuthenticated);
  console.log('- user:', authState.user);
  console.log('- guestAuth:', authState.guestAuth);
  console.log('- chats count:', chatsState.chats.length);
  
  if (authState.guestAuth) {
    console.log('- guestAuth userId:', authState.guestAuth.userId);
    console.log('- guestAuth token (first 10 chars):', authState.guestAuth.token.substring(0, 10) + '...');
  }

  return {
    isAuthenticated: authState.isAuthenticated,
    hasUser: !!authState.user,
    hasGuestAuth: !!authState.guestAuth,
    guestUserId: authState.guestAuth?.userId,
    chatsCount: chatsState.chats.length,
  };
};

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).testCleanup = {
    checkState: checkCurrentState,
  };
}
