import { mutate } from 'swr';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { useAuthStore } from '@/stores/auth.store';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useChats } from '@/hooks/useChats';
import { useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { useAppliances } from '@/hooks/useAppliances';
import { useStreamingState } from '@/hooks/useStreamingState';
import { useSidebar } from '@/hooks/useSidebar';
import { useModalForGuest } from '@/hooks/useModalGuest';
import { createGuestUser } from '@/api/user';

const deleteCookie = (name: string): void => {
  if (typeof document === 'undefined') return;
  const expiredDate = new Date(0);
  const hostname = window.location.hostname;
  const domains = [hostname, `.${hostname}`, '.heyalfie.com', '.clerk.com'];
  const paths = ['/', ''];
  paths.forEach((path) => {
    domains.forEach((domain) => {
      try {
        document.cookie = `${name}=; expires=${expiredDate.toUTCString()}; path=${path}; domain=${domain}`;
        document.cookie = `${name}=; expires=${expiredDate.toUTCString()}; path=${path}; domain=${domain}; secure`;
      } catch (error) {}
    });
  });
};

const getAllCookies = (): Record<string, string> => {
  if (typeof document === 'undefined') return {};
  const cookies: Record<string, string> = {};
  document.cookie.split(';').forEach((cookie) => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });
  return cookies;
};

const clearAuthCookies = async (): Promise<void> => {
  const allCookies = getAllCookies();
  const cookieNames = Object.keys(allCookies);
  const authCookiePatterns = [
    /^__clerk/,
    /^__session/,
    /^__client_uat/,
    /^__refresh/,
    /^auth_token$/,
    /^session_token$/,
    /^user_session$/,
    /^clerk_active_context$/,
  ];

  const cookiesToDelete = cookieNames.filter((name) =>
    authCookiePatterns.some((pattern) => pattern.test(name))
  );

  cookiesToDelete.forEach((cookieName) => {
    deleteCookie(cookieName);
  });

  const knownAuthCookies = [
    '__clerk_db_jwt',
    '__session',
    '__clerk_client_jwt',
    '__clerk_session',
    '__clerk_refresh_token',
    '__clerk_handshake',
    '__clerk_ssr_jwt',
    '__client_uat',
    'auth_token',
    'session_token',
    'user_session',
    'clerk_active_context',
  ];

  knownAuthCookies.forEach((cookieName) => {
    deleteCookie(cookieName);
  });

  const clerkSuffixes = ['_FLMvHKIX', '_DvIHvy3E', '_-Ti8aGhv'];
  const clerkPrefixes = ['__clerk_db_jwt', '__session', '__client_uat', '__refresh'];

  clerkPrefixes.forEach((prefix) => {
    clerkSuffixes.forEach((suffix) => {
      deleteCookie(`${prefix}${suffix}`);
    });
  });

  try {
    fetch('/api/auth/clear-cookies', {
      method: 'POST',
      credentials: 'include',
    }).catch(() => {});
  } catch (error) {}
};

export const clearAllUserData = async (): Promise<void> => {
  try {
    mutate(() => true, undefined, { revalidate: false });
    mutate((key) => typeof key === 'string' && key.includes('/chats'), undefined, {
      revalidate: false,
    });
    mutate((key) => typeof key === 'string' && key.includes('/messages'), undefined, {
      revalidate: false,
    });
    mutate((key) => typeof key === 'string' && key.includes('/todos'), undefined, {
      revalidate: false,
    });

    useAuthStore.getState().logout();
    useRedirectQueryUrl.getState().clearRedirectUrl();
    localStorage.removeItem('auth-storage');
    localStorage.removeItem('redirect-query-url-storage');

    useChats.setState({
      chats: [],
      isLoading: false,
      isSubmitting: false,
      hasMore: true,
      currentPage: 1,
      optimisticMessage: null,
      jobSummaryConfirmed: false,
      isRetryButtonShown: false,
      isMessagesSpacingActive: false,
      isStreamingMessage: false,
    });

    useWidgets.setState({
      addresses: [],
      properties: [],
      hasAddress: true,
      addressData: null,
      accessInstruction: '',
      propertiesResponse: null,
      addressValue: null,
      isLoadingProperties: false,
      isLoadingAddresses: false,
      isSavingAddress: false,
    });

    useMessages.setState({
      isLoading: false,
      hasMore: true,
      currentPage: 1,
      jobSummaryConfirmed: false,
      messageHeight: { optimistic: 0, lastSystem: 0 },
    });

    const appliancesState = useAppliances.getState();
    if ('reset' in appliancesState && typeof appliancesState.reset === 'function') {
      appliancesState.reset();
    } else {
      useAppliances.setState({
        appliances: [],
        editingAppliances: [],
        currentPage: 1,
        totalPages: null,
        hasMoreItems: true,
        isLoadingMore: false,
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isFetching: false,
      });
    }

    useOnboarding().reset();
    useStreamingState.setState({ isStreamingMessage: false });
    useSidebar.setState({ isSidebarOpen: false });
    useModalForGuest.setState({ isModalOpen: false });

    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.startsWith('user-') ||
          key.startsWith('guest-') ||
          key.startsWith('chat-') ||
          key.startsWith('property-') ||
          key.startsWith('todo-') ||
          key.startsWith('alfie-') ||
          key.startsWith('swr-') ||
          key.includes('chats') ||
          key.includes('messages') ||
          key.includes('recent'))
      ) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach((key) => localStorage.removeItem(key));

    sessionStorage.clear();
    await clearAuthCookies();

    try {
      const response = await createGuestUser();
      useAuthStore.getState().setGuestAuth({
        token: response.token,
        userId: response.userId,
        expiresAt: response.expiresAt,
      });
    } catch (error) {}

    useAuthStore.persist.rehydrate();
    useRedirectQueryUrl.persist.rehydrate();
    await new Promise((resolve) => setTimeout(resolve, 100));
  } catch (error) {
    throw error;
  }
};

export const completeLogout = async (clerkSignOut?: () => Promise<void>): Promise<void> => {
  try {
    if (clerkSignOut) {
      await clerkSignOut();
      await new Promise((resolve) => setTimeout(resolve, 500));
    }
    await clearAllUserData();
    await clearAuthCookies();
    await new Promise((resolve) => setTimeout(resolve, 300));
  } catch (error) {
    throw error;
  }
};

export const useCompleteLogout = () => {
  const { signOut } = useAuth();
  const router = useRouter();

  const logout = useCallback(
    async (options?: { redirectTo?: string; forceReload?: boolean }) => {
      try {
        await completeLogout(async () => {
          await signOut();
        });

        const redirectTo = options?.redirectTo || '/';

        if (options?.forceReload) {
          window.location.href = window.location.origin + redirectTo;
        } else {
          router.push(redirectTo);
        }
      } catch (error) {
        window.location.href = window.location.origin + (options?.redirectTo || '/');
      }
    },
    [signOut, router]
  );

  return { logout };
};
