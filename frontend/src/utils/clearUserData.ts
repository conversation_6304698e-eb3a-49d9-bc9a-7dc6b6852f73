/**
 * Clear user data using SDK methods when signing out
 */

import { mutate } from 'swr';
import { useAuthStore } from '@/stores/auth.store';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';
import { useChats } from '@/hooks/useChats';
import { useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { useAppliances } from '@/hooks/useAppliances';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useStreamingState } from '@/hooks/useStreamingState';
import { useConversionState } from '@/hooks/useGuestConversion';
import { useSidebar } from '@/hooks/useSidebar';
import { useModalForGuest } from '@/hooks/useModalGuest';

/**
 * Clear all user data using SDK methods
 */
export const clearAllUserData = async (): Promise<void> => {
  console.log('🧹 Starting clearAllUserData...');

  // Log current state before clearing
  const authState = useAuthStore.getState();
  console.log('📊 Before clearing - guestAuth:', authState.guestAuth);
  console.log('📊 Before clearing - isAuthenticated:', authState.isAuthenticated);

  try {
    // 1. Clear SWR cache using mutate - clear all cached data
    mutate(() => true, undefined, { revalidate: false });

    // Clear specific chat-related SWR cache keys
    mutate((key) => typeof key === 'string' && key.includes('/chats'), undefined, {
      revalidate: false,
    });
    mutate((key) => typeof key === 'string' && key.includes('/messages'), undefined, {
      revalidate: false,
    });

    // 2. Reset Zustand stores using their methods
    console.log('🔄 Calling auth store logout...');
    useAuthStore.getState().logout();

    console.log('🔄 Calling clearGuestAuth...');
    useAuthStore.getState().clearGuestAuth();

    // Redirect store - use clearRedirectUrl method
    console.log('🔄 Clearing redirect URL...');
    useRedirectQueryUrl.getState().clearRedirectUrl();

    // Reset other Zustand stores to initial state
    useChats.setState({
      chats: [],
      isLoading: false,
      isSubmitting: false,
      hasMore: true,
      currentPage: 1,
      optimisticMessage: null,
      jobSummaryConfirmed: false,
      isRetryButtonShown: false,
      isMessagesSpacingActive: false,
      isStreamingMessage: false,
    });

    useWidgets.setState({
      addresses: [],
      properties: [],
      hasAddress: true,
      addressData: null,
      accessInstruction: '',
      propertiesResponse: null,
      addressValue: null,
      isLoadingProperties: false,
      isLoadingAddresses: false,
      isSavingAddress: false,
    });

    useMessages.setState({
      isLoading: false,
      hasMore: true,
      currentPage: 1,
      jobSummaryConfirmed: false,
      messageHeight: { optimistic: 0, lastSystem: 0 },
    });

    // Reset appliances store using reset method if available
    const appliancesState = useAppliances.getState();
    if ('reset' in appliancesState && typeof appliancesState.reset === 'function') {
      appliancesState.reset();
    } else {
      useAppliances.setState({
        appliances: [],
        editingAppliances: [],
        currentPage: 1,
        totalPages: null,
        hasMoreItems: true,
        isLoadingMore: false,
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isFetching: false,
      });
    }

    // Reset onboarding store using reset method
    const onboardingHook = useOnboarding();
    if ('reset' in onboardingHook && typeof onboardingHook.reset === 'function') {
      onboardingHook.reset();
    }

    useStreamingState.setState({
      isStreamingMessage: false,
    });

    useConversionState.setState({
      isConverting: false,
    });

    useSidebar.setState({
      isSidebarOpen: false,
    });

    useModalForGuest.setState({
      isModalOpen: false,
    });

    // 3. Wait a bit to ensure all stores are cleared
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 4. Verify guestAuth is cleared
    const finalAuthState = useAuthStore.getState();
    console.log('📊 After clearing - guestAuth:', finalAuthState.guestAuth);
    console.log('📊 After clearing - isAuthenticated:', finalAuthState.isAuthenticated);

    // Note: We don't create a new guest user immediately after sign out.
    // The guest user will be created automatically when needed (e.g., when sending first message)
    // This ensures the user is completely logged out and sees no old data.

    console.log('✅ User data cleared successfully');
  } catch (error) {
    console.error('❌ Error clearing user data:', error);
    throw error;
  }
};
