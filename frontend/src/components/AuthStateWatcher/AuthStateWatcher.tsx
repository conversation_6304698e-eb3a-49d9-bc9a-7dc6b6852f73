'use client';

import { useAuth } from '@clerk/nextjs';
import { useEffect, useRef } from 'react';
import { clearAllUserData } from '@/utils/clearUserData';

export const AuthStateWatcher = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const wasSignedInRef = useRef<boolean | null>(null);

  useEffect(() => {
    if (!isLoaded) return;

    if (wasSignedInRef.current === null) {
      wasSignedInRef.current = isSignedIn;
      return;
    }

    if (wasSignedInRef.current && !isSignedIn) {
      clearAllUserData().catch(() => {});
    }

    wasSignedInRef.current = isSignedIn;
  }, [isSignedIn, isLoaded]);

  return null;
};
