'use client';

import { useAuth } from '@clerk/nextjs';
import { useEffect, useRef } from 'react';
import { clearAllUserData } from '@/utils/clearUserData';

export const AuthStateWatcher = () => {
  const { isSignedIn } = useAuth();
  const wasSignedInRef = useRef(isSignedIn);

  useEffect(() => {
    console.log(
      '🔍 AuthStateWatcher: isSignedIn changed to:',
      isSignedIn,
      'was:',
      wasSignedInRef.current
    );

    // Detect when user signs out (isSignedIn changes from true to false)
    if (wasSignedInRef.current && !isSignedIn) {
      console.log('🚪 User signed out detected! Clearing data...');

      // Clear user data after sign out
      clearAllUserData().catch((error) => {
        console.error('❌ Error clearing user data:', error);
      });
    }

    wasSignedInRef.current = isSignedIn;
  }, [isSignedIn]);

  return null;
};
