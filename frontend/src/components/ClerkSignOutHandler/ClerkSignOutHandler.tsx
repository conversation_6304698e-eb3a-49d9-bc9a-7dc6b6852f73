'use client';

import { useAuth } from '@clerk/nextjs';
import { useEffect, useRef } from 'react';
import { clearAllUserData } from '@/utils/clearUserData';

/**
 * Component that intercepts sign out to clear user data
 */
export const ClerkSignOutHandler = () => {
  const { isSignedIn } = useAuth();
  const wasSignedInRef = useRef(isSignedIn);

  useEffect(() => {
    // Detect when user signs out (isSignedIn changes from true to false)
    if (wasSignedInRef.current && !isSignedIn) {
      console.log('🚪 User signed out, clearing data...');

      // Clear user data after sign out
      clearAllUserData().catch((error) => {
        console.error('❌ Error clearing user data:', error);
      });
    }

    wasSignedInRef.current = isSignedIn;
  }, [isSignedIn]);

  return null;
};
