'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { useAuth } from '@clerk/nextjs';

import { PropertyHeader } from '@/components/PropertyHeader';
import { PropertyHeaderState } from '@/components/PropertyHeader/PropertyHeader.types';
import { useWidgets } from '@/hooks/useWidgets';
import { usePropertyDetailsActions } from '@/hooks/usePropertyDetails';
import { mapUserPropertyRelationTypeToDisplayText } from '@/api/properties';
import { AddressType } from '@/components/AddressRoleForm/AddressRoleForm.types';

interface IntegratedPropertyHeaderProps {
  onPropertyDataChange?: () => void;
}

export const IntegratedPropertyHeader: React.FC<IntegratedPropertyHeaderProps> = ({
  onPropertyDataChange,
}) => {
  const { getToken, isSignedIn } = useAuth();
  const { properties, fetchProperties } = useWidgets();
  const { createProperty, updateProperty } = usePropertyDetailsActions();

  const [propertyHeaderState, setPropertyHeaderState] = useState<PropertyHeaderState>(
    PropertyHeaderState.EMPTY
  );
  const [address, setAddress] = useState<string>('');
  const [ownerStatus, setOwnerStatus] = useState<string | undefined>(undefined);
  const [profileImageUrl, setProfileImageUrl] = useState<string>('');

  const refreshPropertyData = useCallback(async () => {
    if (!isSignedIn) {
      return;
    }

    const token = await getToken();
    if (token) {
      await fetchProperties(token);
      onPropertyDataChange?.();
    }
  }, [getToken, fetchProperties, onPropertyDataChange, isSignedIn]);

  useEffect(() => {
    void refreshPropertyData();
  }, [refreshPropertyData]);

  useEffect(() => {
    if (properties.length > 0) {
      const currentProperty = properties[0];
      if (!currentProperty.address) return;
      const fullAddress = `${currentProperty.address.streetLine1}, ${currentProperty.address.townOrCity}, ${currentProperty.address.postcode}`;

      setAddress(fullAddress);
      setPropertyHeaderState(PropertyHeaderState.FILLED);

      if (currentProperty.userPropertyRelationshipType) {
        const ownerStatusText = mapUserPropertyRelationTypeToDisplayText(
          currentProperty.userPropertyRelationshipType
        );
        setOwnerStatus(ownerStatusText);
      }
    }
  }, [properties]);

  const handleProfileImageChange = (file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setProfileImageUrl(imageUrl);
  };

  const handleAddressComplete = async (
    data:
      | { address: string; displayAddress: string; role: string } // Autocomplete
      | { address: AddressType; role: string } // Manual entry
  ) => {
    try {
      if (properties.length === 0) {
        await createProperty({
          address: data.address,
          role: data.role,
        });
      } else {
        await updateProperty(properties[0].id, {
          address: data.address,
          role: data.role,
        });
      }
      await refreshPropertyData();

      // Set display address for UI
      let displayAddress: string;
      if ('displayAddress' in data) {
        // Autocomplete case - use the suggestion text from the API
        displayAddress = data.displayAddress;
      } else if (typeof data.address === 'string') {
        // Fallback for string address without display text
        displayAddress = data.address;
      } else {
        // Manual entry case - format the address object
        displayAddress =
          `${data.address.line1}, ${data.address.city}, ${data.address.postcode}`.trim();
      }

      setAddress(displayAddress);

      let ownerStatusText = '';
      switch (data.role) {
        case 'owner':
          ownerStatusText = 'Owner and occupier';
          break;
        case 'landlord':
          ownerStatusText = 'Landlord';
          break;
        case 'tenant':
          ownerStatusText = 'Tenant';
          break;
        default:
          ownerStatusText = data.role;
      }

      setOwnerStatus(ownerStatusText);
      setPropertyHeaderState(PropertyHeaderState.FILLED);
    } catch (error) {
      console.error('Failed to save address:', error);
    }
  };

  return (
    <PropertyHeader
      address={address}
      ownerStatus={ownerStatus}
      profileImageUrl={profileImageUrl}
      state={propertyHeaderState}
      onProfileImageChange={handleProfileImageChange}
      onAddressComplete={handleAddressComplete}
    />
  );
};
