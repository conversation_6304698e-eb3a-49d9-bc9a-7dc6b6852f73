import { DocumentDto, fetchDocuments, uploadDocument } from '@/api/documents';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getFileType } from '@/utils/fileUtils';
import { useAuth } from '@clerk/nextjs';
import { UniversalFile, UploadedFile } from '@/types/file';
import { useNotifications } from '@/hooks/useNotifications';
import { Context } from '@/api/notifications';

interface UseAppliancesDocumentUploadOptions {
  setUploadContext: Context;
  getUploadContext: Context | null;
  pollingInterval?: number;
  onLoaded?: () => void;
  onChanged?: () => void;
}

interface ErrorWithResponse {
  response?: {
    data?: {
      detail?: string;
    };
  };
}

const SUPPORTED_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/heic',
  'image/heif',
];

const SUPPORTED_EXTENSIONS = ['.pdf', '.png', '.jpg', '.jpeg', '.webp', '.gif', '.heic', '.heif'];

function getUniversalFileId(document: DocumentDto) {
  return `document-${document.id}`;
}

export const useFiles = ({
  pollingInterval = 5000,
  onLoaded,
  getUploadContext,
  setUploadContext,
  onChanged,
}: UseAppliancesDocumentUploadOptions) => {
  const { getToken, isSignedIn } = useAuth();
  const [files, setFiles] = useState<UniversalFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const recentlyUploadedFilesRef = useRef<Set<string>>(new Set());

  const {
    notifications,
    deleteNotifications,
    deleteNotification,
    fetchNotifications,
    pushNotification,
  } = useNotifications({
    context: getUploadContext,
  });

  const filesHash = useMemo(() => {
    const sorted = files.sort((a, b) => a.id.localeCompare(b.id));
    return JSON.stringify(sorted);
  }, [files]);

  useEffect(() => {
    onChanged?.();
  }, [filesHash, onChanged]);

  const validateFiles = useCallback((files: File[]) => {
    const supported: File[] = [];
    const unsupported: string[] = [];

    files.forEach((file) => {
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      const isSupported =
        SUPPORTED_TYPES.includes(file.type) || SUPPORTED_EXTENSIONS.includes(fileExtension);

      if (!isSupported) {
        unsupported.push(file.name);
      } else {
        supported.push(file);
      }
    });

    return { supported, unsupported };
  }, []);

  const pollDocuments = useCallback(async () => {
    if (!isSignedIn) {
      return;
    }

    const token = await getToken();
    if (!token) return;

    const documents = await fetchDocuments(token, getUploadContext);

    const toFileStatus = (status: DocumentDto['status']): UploadedFile['status'] => {
      switch (status) {
        case 'processing':
        case 'saved':
          return 'uploading';
        case 'processingCompleted':
        case 'info':
          return 'success';
        case 'irrelevant':
        case 'error':
        case 'warning':
          return 'error';
      }
    };

    const fetchedFiles = documents.map(
      (document): UniversalFile => ({
        id: getUniversalFileId(document),
        documentId: document.id,
        name: document.fileName,
        type:
          document.browserMimeType.includes('pdf') ||
          document.fileName.toLowerCase().endsWith('.pdf')
            ? 'pdf'
            : 'image',
        size: document.sizeInKiloBytes,
        status: toFileStatus(document.status),
        createdAt: document.createdAt,
        category: document.category ?? undefined,
        label: document.label ?? undefined,
      })
    );

    setFiles((prevFiles) => {
      const uniqueFilesMap = new Map<string, UniversalFile>();
      prevFiles.forEach((file) => {
        uniqueFilesMap.set(file.id, file);
      });
      fetchedFiles.forEach((file) => {
        uniqueFilesMap.set(file.id, file);
      });
      return Array.from(uniqueFilesMap.values());
    });

    onLoaded?.();
    await fetchNotifications();
  }, [fetchNotifications, getToken, getUploadContext, onLoaded, isSignedIn]);

  useEffect(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    pollingIntervalRef.current = setInterval(pollDocuments, pollingInterval);
    void pollDocuments();

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [pollDocuments, pollingInterval]);

  const uploadFiles = useCallback(
    async (files: File[]) => {
      const { supported, unsupported } = validateFiles(files);

      recentlyUploadedFilesRef.current.clear();

      if (unsupported.length > 0) {
        pushNotification({
          id: `unsupported-files-${new Date()}`,
          systemId: null,
          type: 'document_error',
          fileName: '',
          severity: 'error',
          title: `Unsupported file format`,
          message: `${unsupported.map((name) => `'${name}'`).join(', ')} ${unsupported.length === 1 ? 'has' : 'have'} not been processed. Alfie is able to read documents and photos including PDF, PNG, JPG, JPEG, WEBP, GIF, HEIC, and HEIF file formats.`,
        });
      }

      if (supported.length === 0) return;

      setIsUploading(true);

      const token = await getToken();
      if (!token) {
        setIsUploading(false);
        return;
      }

      supported.forEach((file) => {
        recentlyUploadedFilesRef.current.add(file.name);
      });

      const uploadPromises = supported.map(async (file) => {
        const tempId = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const uploadingFile: UniversalFile = {
          id: tempId,
          name: file.name,
          type: getFileType(file),
          size: file.size,
          file,
          createdAt: new Date().toISOString(),
          status: 'uploading',
        };

        setFiles((prev) => [uploadingFile, ...prev]);

        try {
          await deleteNotifications();
          const uploadedFile = await uploadDocument(file, token, setUploadContext);

          setFiles((prev) =>
            prev.map((f) =>
              f.id === tempId
                ? {
                    ...f,
                    id: getUniversalFileId(uploadedFile),
                    documentId: uploadedFile.id,
                  }
                : f
            )
          );
        } catch (error) {
          console.error('Upload failed for file:', file.name, error);
          const detail = (error as ErrorWithResponse)?.response?.data?.detail;
          pushNotification({
            id: `notification-for-file-${file.name}`,
            systemId: null,
            type: 'document_error',
            severity: 'error',
            fileName: file.name,
            title: `Failed to upload '${file.name}'`,
            message: detail ? detail : `An unexpected issue occurred. Please try again.`,
          });
          setFiles((prev) => prev.filter((f) => f.id !== tempId));
        }
      });

      await Promise.all(uploadPromises);
      setIsUploading(false);
    },
    [deleteNotifications, getToken, pushNotification, setUploadContext, validateFiles]
  );

  const uploadingFiles = useMemo(
    () => files.filter((file) => file.status === 'uploading'),
    [files]
  );

  return {
    uploadFiles,
    uploadingFiles,
    isUploading,
    files,
    deleteNotification,
    notifications,
  };
};
