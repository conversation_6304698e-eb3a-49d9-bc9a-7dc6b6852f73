FROM python:3.12.11-slim AS builder

RUN apt-get update && apt-get -y upgrade
RUN pip install pipenv
RUN apt-get install libmagic1 -y
RUN apt-get install poppler-utils  -y
ENV PIPENV_VENV_IN_PROJECT=1
ADD Pipfile.lock Pipfile /usr/src/

WORKDIR /usr/src
RUN pipenv sync

FROM python:3.12.11-slim AS runtime

RUN apt-get update && apt-get -y upgrade
RUN apt-get install -y curl # for healthcheck
RUN apt-get install libmagic1 -y
RUN apt-get install poppler-utils  -y
RUN mkdir -v /usr/src/.venv
COPY --from=builder /usr/src/.venv/ /usr/src/.venv/
RUN /usr/src/.venv/bin/pip install awslambdaric

WORKDIR /app
COPY src /app/src
COPY prompts /app/prompts
COPY alembic.ini /app

COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]

FROM builder AS test

WORKDIR /usr/src
RUN pipenv install --dev --system
RUN apt-get install libmagic1 -y

WORKDIR /app
COPY src /app/src
COPY prompts /app/prompts
COPY alembic.ini /app

COPY ./migrations-check.py  /app
COPY .coveragerc /app
COPY ./tox.ini /app

CMD ["tox"]