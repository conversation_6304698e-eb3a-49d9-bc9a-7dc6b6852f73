from os import environ

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.agents.DiagnosticAgentStreaming import DiagnosticAgentStreaming
from src.agents.ContextualPromptsAgent import ContextualPromptAgent
from src.agents.dataextractors.DataExtractorService import DataExtractorService
from src.agents.todos_agent import TodosAgent
from src.agents.utils.PromptLoader import PromptLoader
from src.ai_clients.GeminiClient import GeminiClient
from src.ai_dao.QdrantDAO import <PERSON>drantDAO
from src.database import sessionmanager
from src.singleton import singleton
from src.integrations.backend import BackendAPI
from src.services.ai_appliances import ApplianceService
from src.services.ai_jobs import JobService
from src.services.ai_property import PropertyService
from src.services.contextual_prompts import ContextualPromptService
from src.services.entity_links import EntityLinksService

prompt_loader = PromptLoader(default_prompt_version="DiagnosticAgent_v6.txt")


async def get_db_session():
    async with sessionmanager.session() as session:
        yield session


def get_job_service(db: AsyncSession = Depends(get_db_session)) -> JobService:
    return JobService(db=db)


def get_appliance_service(db: AsyncSession = Depends(get_db_session)) -> ApplianceService:
    return ApplianceService(db=db)


def get_property_service(db: AsyncSession = Depends(get_db_session)) -> PropertyService:
    return PropertyService(db=db)


# the same symmetric api key for ai engine and backend for authentication
backend_api = BackendAPI(environ["AI_ENGINE_API_KEY"], environ["BACKEND_API_BASE_URL"])


def get_backend_api():
    return backend_api


@singleton()
def get_qdrant_dao():
    return QdrantDAO()


@singleton()
def get_data_extractor_service(
    backend_api: BackendAPI = Depends(get_backend_api),
    appliance_service: ApplianceService = Depends(get_appliance_service),
    property_service: PropertyService = Depends(get_property_service),
    qdrant_dao: QdrantDAO = Depends(get_qdrant_dao),
) -> DataExtractorService:
    return DataExtractorService(
        backend_api=backend_api,
        appliance_service=appliance_service,
        property_service=property_service,
        qdrant_dao=qdrant_dao,
    )


_SYSTEM_PROMPT = prompt_loader.load_system_prompt()
_TODOS_AGENT_PROMPT = prompt_loader.load_system_prompt(prompt_version="todos_extraction_prompt.txt")


@singleton()
def get_diagnostic_agent_streaming(
    job_service: JobService = Depends(get_job_service),
    appliance_service=Depends(get_appliance_service),
    property_service=Depends(get_property_service),
    qdrant_dao: QdrantDAO = Depends(get_qdrant_dao),
):
    # Create a new instance with the injected job_service
    return DiagnosticAgentStreaming(
        job_service=job_service,
        appliance_service=appliance_service,
        property_service=property_service,
        agent_prompt=_SYSTEM_PROMPT,
        qdrant_dao=qdrant_dao,
    )


def get_todos_agent():
    return TodosAgent(agent_prompt=_TODOS_AGENT_PROMPT)


@singleton()
def get_gemini_client() -> GeminiClient:
    return GeminiClient()


def get_entity_link_service(db: AsyncSession = Depends(get_db_session)) -> EntityLinksService:
    return EntityLinksService(db=db)


def get_contextual_prompt_service(
    db: AsyncSession = Depends(get_db_session),
    entity_link_service: EntityLinksService = Depends(get_entity_link_service),
) -> ContextualPromptService:
    return ContextualPromptService(
        db=db,
        entity_link_service=entity_link_service,
    )


@singleton()
def get_contextual_prompt_agent(
    job_service: JobService = Depends(get_job_service),
    appliance_service: ApplianceService = Depends(get_appliance_service),
    property_service: PropertyService = Depends(get_property_service),
    qdrant_dao: QdrantDAO = Depends(get_qdrant_dao),
    gemini_client: GeminiClient = Depends(get_gemini_client),
    contextual_prompts_service: ContextualPromptService = Depends(get_contextual_prompt_service),
) -> ContextualPromptAgent:
    return ContextualPromptAgent(
        job_service=job_service,
        appliance_service=appliance_service,
        property_service=property_service,
        qdrant_dao=qdrant_dao,
        gemini_client=gemini_client,
        contextual_prompts_service=contextual_prompts_service,
    )
