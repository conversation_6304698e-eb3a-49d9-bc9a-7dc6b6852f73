import asyncio
import functools

_cache = {}
_lock = asyncio.Lock()


def singleton():
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            async with _lock:
                key = func.__name__
                if key in _cache:
                    return _cache[key]
                result = func(*args, **kwargs)
                _cache[key] = result
                return result

        return wrapper

    return decorator
