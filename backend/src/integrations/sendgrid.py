import asyncio
import logging
from enum import Enum

from pydantic import BaseModel, ConfigDict
from sendgrid import SendGridAPIClient, From
from sendgrid.helpers.mail import Mail, Attachment, FileContent, FileName, FileType, Disposition

from src.schemas import B2BDemoRequest

logger = logging.getLogger("uvicorn")


class UserView(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    firstName: str
    lastName: str | None = None
    email: str
    phoneNumber: str | None = None


class PropertyView(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    address: str
    parkingInstructions: str | None = ""
    houseAccess: str | None = ""


class JobView(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    reference: str
    headline: str
    subtitle: str
    details: str
    date: str
    timeOfDay: str


class AttachmentView(BaseModel):
    fileContent: str
    fileName: str
    fileType: str


class SendgridService:
    class MessageTemplateIds(Enum):
        WELCOME = "d-9516c8c0cf654083ad64878b4bf5c20c"
        USER_JOB_REQUEST_ACKNOWLEDGEMENT = "d-a05155661648465a8c51dc87911045b7"
        OPS_JOB_REQUEST_ACKNOWLEDGEMENT = "d-0d2ce44976b74b6c88510dfa35d1f2c6"
        QUOTE_AND_PROVIDER_UPDATES = "d-3f40c7ac01324c97a719db84313b0ae5"
        ADDITIONAL_INFORMATION_REQUEST = "d-a0cfcbd0f994450daa687991635a2ffc"
        REQUEST_WITH_PROVIDER_CONFIRMATION = "d-6ed37c3ca4a0477cb21870b31aba620f"
        APPOINTMENT_CONFIRMATION = "d-3ec26f9c7b7640198f7ad917738c4556"
        REMINDERS_AND_FOLLOW_UPS = "d-4ee90df1d7464319ba9d8506a174a6a6"
        POST_SERVICE_FOLLOW_UP = "d-ccd2a96ea5c04338be17a6df25ecb303"
        TODO_REMINDER = "d-71ea6f8732a241679421576a6bcf642d"
        USER_B2B_DEMO_REQUEST_ACK = "d-18941493be914e41b169a1ad0c123d19"
        OPS_B2B_DEMO_REQUEST = "d-addea68653de465b99224590d328530c"

    def __init__(self, api_key: str, default_from_email: str, stage: str):
        self._api_key = api_key
        self._default_from_email = default_from_email
        self._sendgrid_client = SendGridAPIClient(api_key)
        self._stage = stage

    async def send_welcome_email(self, to_email: str, user_first_name: str) -> None:
        logger.info(f"Sending welcome email to {to_email}")
        await self._send_email_with_dynamic_template(
            to_email=to_email,
            from_email=self._default_from_email,
            template_id=self.MessageTemplateIds.WELCOME.value,
            dynamic_template_data={"userFirstName": user_first_name},
        )

    async def send_user_job_acknowledgement_email(
        self,
        to_email: str,
        user: UserView,
        job: JobView,
        attachments: list[AttachmentView] | None = None,
        from_email: str = "<EMAIL>",
    ) -> None:
        logger.info(f"Sending user job acknowledgement email to {to_email}")
        await self._send_email_with_dynamic_template(
            to_email=to_email,
            from_email=from_email,
            template_id=self.MessageTemplateIds.USER_JOB_REQUEST_ACKNOWLEDGEMENT.value,
            dynamic_template_data={
                "userFirstName": user.firstName,
                "jobHeadline": job.headline,
                "jobSubtitle": job.subtitle,
                "jobDetails": job.details,
                "jobUrgency": job.date,
                "jobAvailability": job.timeOfDay,
            },
            attachments=attachments,
        )

    async def send_ops_job_acknowledgement_email(
        self,
        user: UserView,
        job: JobView,
        property_view: PropertyView,
        chat_id: int,
        attachments: list[AttachmentView] | None = None,
        from_email: str = "<EMAIL>",
        to_email: str = "<EMAIL>",
    ) -> None:
        logger.info(f"Sending ops job acknowledgement email to {to_email}")
        await self._send_email_with_dynamic_template(
            to_email=to_email,
            from_email=from_email,
            template_id=self.MessageTemplateIds.OPS_JOB_REQUEST_ACKNOWLEDGEMENT.value,
            dynamic_template_data={
                "userId": user.id,
                "userFirstName": user.firstName,
                "userLastName": user.lastName,
                "userPhoneNumber": user.phoneNumber,
                "jobId": job.id,
                "jobReference": job.reference,
                "jobHeadline": job.headline,
                "jobSubtitle": job.subtitle,
                "jobDetails": job.details,
                "jobUrgency": job.date,
                "jobAvailability": job.timeOfDay,
                "propertyAddress": property_view.address,
                "parkingInstructions": property_view.parkingInstructions,
                "houseAccess": property_view.houseAccess,
                "langchainChatUrl": f"https://eu.smith.langchain.com/o/d5072b92-a784-4856-bb13-5ee8774c862c/projects/p/fdbc435d-6904-4366-b3b1-360333eaeae5/t/?conversationId=chat_id%3A%20{chat_id}",  # noqa: E501 # TODO env var
            },
            attachments=attachments,
        )

    async def send_b2b_demo_request_emails(
        self, user: UserView, b2b_demo_request: B2BDemoRequest, from_email: str = "<EMAIL>"
    ) -> None:
        logger.info(f"Sending B2B demo request emails for {user.email}")
        await asyncio.gather(
            self._send_user_b2b_demo_request_ack_email(
                to_email=str(b2b_demo_request.businessEmail),
                user_first_name=user.firstName,
                from_email=from_email,
            ),
            self._send_ops_b2b_demo_request_email(
                user=user,
                b2b_demo_request=b2b_demo_request,
                from_email=from_email,
            ),
        )

    async def _send_user_b2b_demo_request_ack_email(
        self, to_email: str, user_first_name: str, from_email: str = "<EMAIL>"
    ) -> None:
        logger.info(f"Sending user B2B demo request ack email to {to_email}")
        await self._send_email_with_dynamic_template(
            to_email=to_email,
            from_email=from_email,
            template_id=self.MessageTemplateIds.USER_B2B_DEMO_REQUEST_ACK.value,
            dynamic_template_data={"userFirstName": user_first_name},
        )

    async def _send_ops_b2b_demo_request_email(
        self,
        user: UserView,
        b2b_demo_request: B2BDemoRequest,
        from_email: str = "<EMAIL>",
        to_email: str = "<EMAIL>",
    ) -> None:
        logger.info(f"Sending ops B2B demo request email to {to_email}")
        await self._send_email_with_dynamic_template(
            to_email=to_email,
            from_email=from_email,
            template_id=self.MessageTemplateIds.OPS_B2B_DEMO_REQUEST.value,
            dynamic_template_data={
                "userId": user.id,
                "userFirstName": user.firstName,
                "userLastName": user.lastName,
                "userPhoneNumber": user.phoneNumber,
                "userEmail": user.email,
                "companyName": b2b_demo_request.companyName,
                "businessEmail": b2b_demo_request.businessEmail,
                "numberOfPropertiesManaged": b2b_demo_request.numberOfPropertiesManaged,
            },
        )

    async def _send_email_with_dynamic_template(
        self,
        to_email: str,
        from_email: str,
        template_id: str,
        dynamic_template_data: dict,
        attachments: list[AttachmentView] | None = None,
    ) -> None:
        logger.info(f"Sending email to {to_email} with template {template_id}")

        match self._stage:
            case "production":
                pass
            case "staging":
                to_email = to_email.replace("@", "+staging@")
            case "local":
                to_email = to_email.replace("@", "+local@")
            case _:
                to_email = to_email.replace("@", "+local@")

        if from_email == "<EMAIL>":
            from_email = From(from_email, name="Alfie")

        message = Mail(from_email=from_email, to_emails=to_email)
        message.template_id = template_id
        message.dynamic_template_data = dynamic_template_data
        if attachments:
            for attachment in attachments:
                message.add_attachment(
                    Attachment(
                        FileContent(attachment.fileContent),
                        FileName(attachment.fileName),
                        FileType(attachment.fileType),
                        Disposition("attachment"),
                    )
                )

        try:
            # Send email in a separate thread to avoid blocking the event loop
            response = await asyncio.to_thread(self._sendgrid_client.send, message)
            logger.info(f"Email sent to {to_email}. Status code: {response.status_code}")
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            raise
