import logging
from typing import Tuple, Any

from pydantic import BaseModel, field_validator

from src.integrations.base import BaseAPI


class FindAddressesHit(BaseModel):
    id: str
    suggestion: str
    udprn: int


class ResolvedAddress(BaseModel):
    id: str
    line_1: str
    line_2: str
    line_3: str
    post_town: str
    postcode: str
    county: str
    country: str
    uprn: str
    udprn: int
    umprn: int | None
    thoroughfare: str
    building_number: str
    building_name: str
    sub_building_name: str
    latitude: float | str
    longitude: float | str

    @field_validator("umprn", mode="before")
    @classmethod
    def check_umprn(cls, value: Any) -> int | None:
        if not value:
            return None
        try:
            v = int(value)
            return v
        except ValueError:
            return None


class IdealPostcodesAPI(BaseAPI):
    _base_url = "https://api.ideal-postcodes.co.uk"

    def __init__(self, api_key: str):
        self._api_key = api_key

    async def _fetch(self, url: str, params: dict = None):
        await self._ensure_session()
        logging.debug(f"Fetching {url}")
        query_params = {
            "api_key": self._api_key,
        }
        if params:
            query_params.update(params)
        async with self._session.get(url, params=query_params) as response:
            response.raise_for_status()
            return await response.json()

    async def find_addresses(self, query: str, limit: int = 10) -> list[FindAddressesHit]:
        url = f"{self._base_url}/v1/autocomplete/addresses"
        response = await self._fetch(url, {"query": query, "limit": limit, "context": "GBR"})
        hits = [FindAddressesHit.model_validate(hit) for hit in response["result"]["hits"]]
        return hits

    async def resolve_address(self, address_id: str) -> Tuple[ResolvedAddress, dict]:
        url = f"{self._base_url}/v1/autocomplete/addresses/{address_id}/gbr"
        response = await self._fetch(url)
        if response["code"] != 2000:
            error_message = f"Failed to resolve address {address_id}"
            logging.error(error_message)
            logging.error(response)
            raise ValueError(error_message)
        return ResolvedAddress.model_validate(response["result"]), response["result"]
