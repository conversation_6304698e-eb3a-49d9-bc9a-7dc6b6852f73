import asyncio
import json
import logging
from abc import ABC, abstractmethod
from typing import Literal, Any, AsyncGenerator, Optional
from urllib.parse import urljoin

import aiohttp
from fastapi import HTTPException, BackgroundTasks
from pydantic import BaseModel
from sentry_sdk import capture_exception as sentry_capture_exception
from tenacity import retry, stop_after_attempt, wait_fixed

from src.agents import DiagnosticAgentStreaming
from src.agents.dataextractors.DataExtractorService import DataExtractorService
from src.agents.todos_agent import TodosAgent
from src.ai_dao import QdrantDAO
from src.ai_schemas import Message, ParseFileRequest
from src.db_models.chat import Chat
from src.db_models.document import Document
from src.integrations.base import BaseAPI
from src.schemas import SendMessageRequest, FindTodo, FindTodoChatMessage
from src.services.FileParser import FileParser

logger = logging.getLogger("uvicorn")


class AttachmentDetails(BaseModel):
    documentId: int
    documentS3Url: str
    userId: int
    documentS3Key: str
    documentS3Bucket: str
    # also check _ALLOWED_DOCUMENT_EXTENSIONS in DocumentService
    type: Literal["png", "jpg", "jpeg", "webp", "gif", "pdf", "heic", "heif"]
    base64: Optional[str] = None


class UserMessage(SendMessageRequest):
    userId: int
    chatId: int
    messageId: int
    attachments: list[AttachmentDetails] | None = None


class ResponseData(BaseModel):
    chatId: int
    response: Message
    additionalData: dict[str, Any] | None


class AIMessageResponse(BaseModel):
    status: Literal["success", "error"]
    data: ResponseData


class FindTodosRequest(BaseModel):
    userId: int
    chatId: int
    messages: list[FindTodoChatMessage]


class FindTodosResponse(BaseModel):
    todos: list[FindTodo]


class AbstractAIEngineAPI(ABC):
    @abstractmethod
    async def generate_response_stream(self, to_message: UserMessage) -> AsyncGenerator[str, None]:
        """Generate a response stream from the AI engine for a given user message."""
        raise NotImplementedError("Subclasses must implement this method.")

    @abstractmethod
    async def parse_file(self, document: Document, body: bytes = None) -> None:
        """Parse a file using the AI engine."""
        raise NotImplementedError("Subclasses must implement this method.")

    @abstractmethod
    async def find_todos(self, chat: Chat) -> list[FindTodo]:
        """Find todos in a chat using the AI engine."""
        raise NotImplementedError("Subclasses must implement this method.")


class AIEngineAPI(BaseAPI, AbstractAIEngineAPI):
    def __init__(self, api_key: str, base_url: str):
        self._api_key = api_key
        self._base_url = base_url

    async def generate_response(self, to_message: UserMessage) -> AIMessageResponse:
        ai_response_json = await self._post_message(to_message)
        ai_response = AIMessageResponse.model_validate(ai_response_json)
        return ai_response

    async def generate_response_stream(self, to_message: UserMessage) -> AsyncGenerator[str, None]:
        await self._ensure_session()
        logger.info(f"Posting message to AI engine stream endpoint for chat {to_message.chatId}.")
        url = urljoin(self._base_url, "ai/message/stream")
        logger.info(f"AI engine stream url: {url}")
        headers = {"Authorization": f"Bearer {self._api_key}", "Accept": "text/event-stream"}
        payload = to_message.model_dump(exclude_none=True)

        try:
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes  timeout
            async with self._session.post(url, headers=headers, json=payload, timeout=timeout) as response:
                if response.status != 200:
                    error_detail = f"AI Engine stream returned status {response.status}"
                    try:
                        error_body = await response.text()
                        error_detail += f": {error_body}"
                    except Exception as e:
                        logger.error(f"Unexpected error during reading body: {str(e)}", exc_info=True)
                        sentry_capture_exception(e)

                    if response.status == 529:
                        logger.error(f"AI Engine overloaded (529): {error_detail}")
                        raise HTTPException(status_code=529, detail="AI Engine unavailable")
                    elif response.status >= 500:
                        logger.error(f"AI Engine server error ({response.status}): {error_detail}")
                        raise HTTPException(status_code=500, detail="AI Internal server error occurred")
                    else:
                        logger.error(f"AI Engine request error ({response.status}): {error_detail}")
                        raise HTTPException(status_code=500, detail="Failed to communicate with AI")

                async for line_bytes in response.content:
                    yield line_bytes.decode("utf-8")

        except asyncio.TimeoutError:
            logger.error(f"Timeout connecting to or streaming from AI Engine: {url}")
            sentry_capture_exception()
            error_payload = {"code": "AI_ENGINE_TIMEOUT", "message": "Request to AI service timed out."}
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with timeout error\n\n"

        except aiohttp.ClientError as e:
            logger.error(f"AIOHTTP client error during AI engine stream: {e}", exc_info=True)
            sentry_capture_exception(e)
            error_payload = {"code": "AI_ENGINE_CONNECTION_ERROR", "message": "Could not connect to AI service."}
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with connection error\n\n"

        except HTTPException as http_exc:
            # HTTP exceptions (like 529)
            logger.error(
                f"HTTP Exception caught during AI engine stream setup: {http_exc.status_code} - {http_exc.detail}"
            )
            error_payload = {"code": f"AI_ENGINE_HTTP_{http_exc.status_code}", "message": http_exc.detail}
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with HTTP error\n\n"

        except Exception as e:
            # Unexpected errors
            logger.error(f"Unexpected error during AI engine stream: {str(e)}", exc_info=True)
            sentry_capture_exception(e)
            error_payload = {
                "code": "AI_ENGINE_STREAM_ERROR",
                "message": "An unexpected error occurred while processing the AI response.",
            }
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with unexpected error\n\n"

        finally:
            logger.info(f"Finished streaming from AI engine for chat {to_message.chatId}.")

    async def parse_file(self, document: Document, body: bytes = None) -> None:
        await self._ensure_session()
        logger.debug("Sending parse_file request to AI engine.")
        request = ParseFileRequest(
            documentId=document.id,
            documentS3Url="",
            userId=document.userId,
            documentS3Key=document.s3Key,
            documentS3Bucket=document.s3Bucket,
            type=document.fileExtension,
        )
        try:
            async with self._session.post(
                urljoin(self._base_url, "ai/parse_file/"),
                headers={"Authorization": f"Bearer {self._api_key}"},
                json=request.model_dump(),
            ) as response:
                response.raise_for_status()
        except Exception as e:
            logger.error(f"Error in AI engine communication: {str(e)}", exc_info=True)
            sentry_capture_exception(e)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    async def find_todos(self, chat: Chat) -> list[FindTodo]:
        await self._ensure_session()
        request = FindTodosRequest(
            chatId=chat.id,
            userId=chat.userId,
            messages=[FindTodoChatMessage.model_validate(message) for message in chat.messages],
        )
        try:
            response = await self._session.post(
                urljoin(self._base_url, "ai/todos/find"),
                headers={"Authorization": f"Bearer {self._api_key}"},
                json=request.model_dump(),
            )
            response.raise_for_status()
            return FindTodosResponse.model_validate(await response.json()).todos
        except Exception as e:
            logger.error(f"Error in AI engine communication: {str(e)}", exc_info=True)
            raise e

    async def _post_message(self, message: UserMessage) -> dict:
        await self._ensure_session()
        logger.debug("Posting message to AI engine.")
        try:
            async with self._session.post(
                urljoin(self._base_url, "ai/message/"),
                headers={"Authorization": f"Bearer {self._api_key}"},
                json=message.model_dump(),
            ) as response:
                # Check specifically for 529 error code
                if response.status == 529:
                    error_data = await response.json()
                    error_message = error_data.get("detail", "AI Engine unavailable")
                    # Propagate the 529 error with its original message
                    raise HTTPException(status_code=529, detail=error_message)

                # For 500 errors, raise with default  message
                if response.status == 500:
                    raise HTTPException(status_code=500, detail="Internal server error occurred")

                # For other errors, raise generic exception
                response.raise_for_status()
                return await response.json()
        except HTTPException as http_exc:
            # Log the HTTP exception with traceback before re-raising
            logger.error(
                f"HTTP Exception in AI engine communication: {http_exc.status_code} - {http_exc.detail}", exc_info=True
            )
            raise
        except Exception as e:
            # Log the general exception with full stacktrace
            logger.error(f"Error in AI engine communication: {str(e)}", exc_info=True)
            # All other exceptions get converted to 500 with default message
            raise HTTPException(status_code=500, detail="Internal server error occurred")


class AIEngineDirect(AbstractAIEngineAPI):
    def __init__(
        self,
        diagnostic_agent_streaming: DiagnosticAgentStreaming,
        background_tasks: BackgroundTasks,
        data_extractor_service: DataExtractorService,
        todos_agent: TodosAgent,
        qdrant_dao: QdrantDAO,
    ):
        self._diagnostic_agent_streaming = diagnostic_agent_streaming
        self._background_tasks = background_tasks
        self._data_extractor_service = data_extractor_service
        self._todos_agent = todos_agent
        self._qdrant_dao = qdrant_dao

    async def generate_response_stream(self, to_message: UserMessage) -> AsyncGenerator[str, None]:
        logger.info(f"Generating response stream for chat {to_message.chatId} using direct AI Engine integration.")
        try:
            async for chunk in self._diagnostic_agent_streaming.process_next_message_stream(
                to_message.message.content, to_message.userId, to_message.chatId, to_message.attachments
            ):
                yield chunk
        except GeneratorExit as e:
            logger.info("Generator was closed prematurely.")
            logger.error(str(e), exc_info=True)

    async def parse_file(self, document: Document, body: bytes = None) -> None:
        self._background_tasks.add_task(
            FileParser(self._qdrant_dao).process_file,
            user_id=document.userId,
            document_id=document.id,
            file_key=document.s3Key,
            bucket_name=document.s3Bucket,
            doc_type=document.fileExtension,
            data_extractor_service=self._data_extractor_service,
            body=body,
        )

    async def find_todos(self, chat: Chat) -> list[FindTodo]:
        return await self._todos_agent.extract_todos(
            [FindTodoChatMessage.model_validate(message) for message in chat.messages]
        )
