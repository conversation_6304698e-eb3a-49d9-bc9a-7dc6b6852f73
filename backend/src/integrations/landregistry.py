import json
import logging
from typing import Tuple
from sentry_sdk import capture_exception as sentry_capture_exception

import aiohttp
from aiohttp import ClientError

from src.integrations.base import BaseAPI

logger = logging.getLogger("uvicorn")


class LandRegistryAPI(BaseAPI):
    _base_url = "https://landregistry.data.gov.uk/qonsole/query"
    _query_prefixes = """prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
                prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>
                prefix owl: <http://www.w3.org/2002/07/owl#>
                prefix xsd: <http://www.w3.org/2001/XMLSchema#>
                prefix sr: <http://data.ordnancesurvey.co.uk/ontology/spatialrelations/>
                prefix ukhpi: <http://landregistry.data.gov.uk/def/ukhpi/>
                prefix lrppi: <http://landregistry.data.gov.uk/def/ppi/>
                prefix skos: <http://www.w3.org/2004/02/skos/core#>
                prefix lrcommon: <http://landregistry.data.gov.uk/def/common/>
    """

    async def _post(self, url, form_data):
        await self._ensure_session()
        logger.debug(f"Fetching {url}")
        async with self._session.post(url, data=form_data) as response:
            response.raise_for_status()
            return await response.json()

    async def _send_query(self, query, output="json", url="/landregistry/query"):
        form_data = aiohttp.FormData()
        form_data.add_field("output", output)
        form_data.add_field("url", url)
        form_data.add_field("q", query)
        response = await self._post(self._base_url, form_data)
        if response["status"] != 200:
            raise RuntimeError(f"Query failed with status {response["status"]} {json.dumps(response)}")
        result = json.loads(response["result"])
        return result

    async def get_average_price_paid_for_postcode(self, postcode: str) -> int | None:
        postcode = postcode.upper()
        query = f"""{self._query_prefixes}
                SELECT ?postcode (xsd:integer(AVG(?amount)) AS ?avg)
                WHERE
                {{
                    VALUES ?postcode {{\"{postcode}\"^^xsd:string}}
                    ?addr lrcommon:postcode ?postcode.
                    ?transx lrppi:propertyAddress ?addr ;
                            lrppi:pricePaid ?amount ;
                            lrppi:transactionDate ?date ;
                            lrppi:transactionCategory/skos:prefLabel ?category.
                }}
                GROUP BY ?postcode"""
        result = await self._send_query(query)

        if not result["results"]["bindings"]:
            return None
        return int(result["results"]["bindings"][0]["avg"]["value"])

    async def get_average_price_paid_for_street(self, postcode: str, town: str, street: str) -> int | None:
        postcode = postcode.upper()
        town = town.upper()
        street = street.upper()
        query = f"""{self._query_prefixes}
                SELECT (xsd:integer(AVG(?amount)) AS ?avg)
                WHERE
                {{
                VALUES ?street {{\"{street}\"^^xsd:string}}
                VALUES ?town {{\"{town}\"^^xsd:string}}
                VALUES ?postcode {{\"{postcode}\"^^xsd:string}}
                  ?addr lrcommon:postcode ?postcode ;
                        lrcommon:street ?street ;
                        lrcommon:town ?town.

                  ?transx lrppi:propertyAddress ?addr ;
                          lrppi:pricePaid ?amount ;
                          lrppi:transactionDate ?date ;
                          lrppi:transactionCategory/skos:prefLabel ?category.
                }}"""
        result = await self._send_query(query)

        if not result["results"]["bindings"]:
            return None
        return int(result["results"]["bindings"][0]["avg"]["value"])

    async def get_average_price_paid_for_flat_for_town(self, town: str) -> int | None:
        town = town.upper()
        query = f"""{self._query_prefixes}
                SELECT ?town (xsd:integer(AVG(?amount)) as ?avg)
                WHERE
                {{
                    VALUES ?town {{\"{town}\"^^xsd:string}}
                    VALUES ?propertytype {{lrcommon:flat-maisonette}}


                  ?addr lrcommon:town ?town.

                  ?transx lrppi:propertyAddress ?addr ;
                          lrppi:pricePaid ?amount ;
                          lrppi:transactionDate ?date ;
                          lrppi:propertyType ?propertytype ;
                          lrppi:transactionCategory/skos:prefLabel ?category.
                }}
                GROUP BY ?town #?type"""
        result = await self._send_query(query)

        if not result["results"]["bindings"]:
            return None
        return int(result["results"]["bindings"][0]["avg"]["value"])

    async def get_average_price_paid_change_over_years_for_town(
        self, town: str, number_of_years: int = 5
    ) -> int | None:
        town = town.upper()
        query = f"""{self._query_prefixes}
                SELECT
                    (xsd:integer(AVG(?amount)) as ?avgPrice)
                    ?year
                WHERE {{
                    ?addr lrcommon:town "{town}" .

                    ?transx lrppi:propertyAddress ?addr ;
                            lrppi:pricePaid ?amount ;
                            lrppi:transactionDate ?date .

                    BIND(YEAR(?date) as ?year)

                    FILTER(?year >= YEAR(NOW()) - {number_of_years - 1})
                }}
                GROUP BY ?year
                ORDER BY ?year"""
        result = await self._send_query(query)

        if not result["results"]["bindings"]:
            return None
        avg_price_at_start = int(result["results"]["bindings"][0]["avgPrice"]["value"])
        avg_price_at_end = int(result["results"]["bindings"][-1]["avgPrice"]["value"])
        change_in_percentage = int((avg_price_at_end - avg_price_at_start) * 100 / avg_price_at_start)
        return change_in_percentage

    async def get_most_expensive_postcodes_for_town(
        self, town: str, number_of_postcodes: int = 10
    ) -> list[Tuple[str, int, int]]:
        town = town.upper()
        query = f"""{self._query_prefixes}
                SELECT
                    ?postcode
                    (xsd:integer(AVG(?amount)) as ?avgPrice)
                    (COUNT(?transx) as ?transactions)
                WHERE {{
                    ?addr lrcommon:postcode ?postcode ;
                          lrcommon:town "{town}"^^xsd:string .

                    ?transx lrppi:propertyAddress ?addr ;
                            lrppi:pricePaid ?amount ;
                            lrppi:transactionDate ?date .

                    # Filter for recent transactions (last 5 years)
                    FILTER(YEAR(?date) >= YEAR(NOW()) - 4)
                }}
                GROUP BY ?postcode
                HAVING (COUNT(?transx) >= 3)
                ORDER BY DESC(?avgPrice)
                LIMIT {number_of_postcodes}"""
        result = await self._send_query(query)
        return [
            (row["postcode"]["value"], int(row["avgPrice"]["value"]), int(row["transactions"]["value"]))
            for row in result["results"]["bindings"]
        ]

    async def get_property_type(
        self,
        building_name: str,
        building_number: str,
        sub_building_name: str,
        street: str,
        postcode: str,
    ) -> str | None:
        building_name = building_name.upper()
        building_number = building_number.upper()
        sub_building_name = sub_building_name.upper()
        street = street.replace("'", "").upper()
        postcode = postcode.upper()

        if building_name:
            paon = f"{building_name}, {building_number}"
        else:
            paon = building_number

        saon_line = ""
        if sub_building_name:
            saon_line = f'lrcommon:saon "{sub_building_name}";'

        query = f"""{self._query_prefixes}
                SELECT ?propertytype
                WHERE
                {{
                  ?addr lrcommon:postcode "{postcode}";
                        lrcommon:street "{street}";
                        {saon_line}
                        lrcommon:paon "{paon}".
                  ?transx lrppi:propertyAddress ?addr ;
                          lrppi:propertyType/skos:prefLabel ?propertytype.
                }}"""
        try:
            result = await self._send_query(query)
        except ClientError as e:
            logger.error(e)
            sentry_capture_exception(e)
            return None
        property_type = None
        for row in result["results"]["bindings"]:
            value = row["propertytype"]["value"]
            if value != "other":
                return value
            else:
                property_type = value
        return property_type
