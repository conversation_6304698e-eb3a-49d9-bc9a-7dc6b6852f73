import logging
from datetime import datetime
from typing import Any
from dataclasses import dataclass

from src.db_models.job import Job
from src.db_models.user import User
from src.integrations.base import BaseAPI
from src.schemas import B2BDemoRequest

logger = logging.getLogger("uvicorn")


@dataclass
class JobWithDocumentsUrls:
    job: Job
    document_urls: list[str]


class HubSpotAPI(BaseAPI):
    def __init__(self, api_key: str):
        self._api_key = api_key

    @staticmethod
    def get_batch_size_limit():
        return 100

    async def _request(self, url, method, json):
        await self._ensure_session()
        headers = {"Content-Type": "application/json", "authorization": f"Bearer {self._api_key}"}
        match method:
            case "post":
                return self._session.post(url, json=json, headers=headers)
            case "patch":
                return self._session.patch(url, json=json, headers=headers)
        raise Exception(f"Unsupported method: {method}")

    @staticmethod
    async def _to_json(response) -> dict:
        if 200 <= response.status < 300:
            response_json = await response.json()
            status = response_json.get("status")
            if status and status != "COMPLETE":
                raise Exception(f"Non-complete status in response: {status}")
            return response_json
        else:
            error_detail = await response.text()
            raise Exception(f"Failed to extract JSON from response: {response.status}, {error_detail}")

    async def _post(self, url, data):
        response = await self._request(url=url, method="post", json=data)
        async with response as resp:
            return await self._to_json(resp)

    async def _patch(self, url, data):
        response = await self._request(url=url, method="patch", json=data)
        async with response as resp:
            return await self._to_json(resp)

    def __user_to_details(self, user: User):
        property_obj = user.properties[0] if user.properties else None
        address = property_obj.address if property_obj else None

        content_dict = {
            "ID": user.id,
            "Created at": user.created_at,
            "Updated at": user.updated_at,
            "Is email verified?": user.isEmailVerified,
            "Is phone number verified?": user.isPhoneNumberVerified,
            "Main usage": user.mainUsage,
            "DIY proficiency": user.diyProficiency,
            "Property ID": property_obj.id if property_obj else None,
            "Property Type": property_obj.type if property_obj else None,
            "Property Address ID": property_obj.addressId if property_obj else None,
            "Property Address Street Line 1": address.streetLine1 if address else None,
            "Property Address Street Line 2": address.streetLine2 if address else None,
            "Property Address Street Line 3": address.streetLine3 if address else None,
            "Property Address Town or City": address.townOrCity if address else None,
            "Property Address Postcode": address.postcode if address else None,
            "Property Address Country": address.country if address else None,
            "Property Address House Access": address.houseAccess if address else None,
            "Property Address Parking Instructions": address.parkingInstructions if address else None,
        }
        return self.__to_rich_text(content_dict)

    async def upsert_contacts(self, users: list[User], sync_time=datetime.now()) -> list[User]:
        if len(users) > self.get_batch_size_limit():
            raise HubspotBatchLimitExceededException(self.get_batch_size_limit())

        if not users:
            return []

        for user in users:
            user.hubspotSyncAt = sync_time

        user_ids = [str(user.id) for user in users]
        logger.info(f"Synchronizing users (ids: {', '.join(user_ids)})")
        response_json = await self._post(
            url="https://api.hubapi.com/crm/v3/objects/contacts/batch/upsert",
            data={
                "inputs": [
                    {
                        "idProperty": "email",
                        "id": user.email,
                        "properties": {
                            "email": user.email,
                            "firstname": user.firstName,
                            "lastname": user.lastName or "",
                            "phone": user.phoneNumber or "",
                            "hey_alfie_details": self.__user_to_details(user),
                        },
                    }
                    for user in users
                ]
            },
        )
        logger.info("Users synced successfully")
        email_to_user = {user.email: user for user in users}
        for result in response_json.get("results", []):
            email = result.get("properties", {}).get("email")
            user = email_to_user.get(email)
            if user:
                user.hubspotId = result.get("id")
        return users

    @staticmethod
    def __to_rich_text(content_dict: dict[str, Any]) -> str:
        def to_li(value):
            return f"<li>{value}</li>"

        def to_lis(value):
            return "\n".join([to_li(elem) for elem in value])

        def convert(value):
            if isinstance(value, list):
                return f"<ul>{to_lis(value)}</ul>"
            else:
                return value

        return "<br><br>\n\n".join(
            f"<b>{key}</b><br>\n{convert(value)}" for key, value in content_dict.items() if value is not None
        )

    async def upsert_job_ticket(self, job_with_docs: JobWithDocumentsUrls, sync_time=datetime.now()):
        job = job_with_docs.job
        document_urls = job_with_docs.document_urls

        logger.info(f"Upserting a ticket for job id {job.id}")
        job.hubspotSyncAt = sync_time

        properties = {
            "hey_alfie_details": self.__job_to_details(job, document_urls),
            "hey_alfie_job_reference": job.reference,
            "subject": job.headline,
            "hs_pipeline": "0",
            "hs_pipeline_stage": "1",
        }
        if job.hubspotId:
            logger.info(f"Updating a ticket for job id {job.id} (hubspot id: {job.hubspotId})")
            response = await self._patch(
                url=f"https://api.hubapi.com/crm/v3/objects/tickets/{job.hubspotId}", data={"properties": properties}
            )
            job.hubspotId = response.get("id")
        else:
            logger.info(f"Creating a ticket for job id {job.id}")
            if job.user.hubspotId is None:
                raise Exception(f"User with id {job.user.id} has no hubspot id")
            response = await self._post(
                url="https://api.hubapi.com/crm/v3/objects/tickets",
                data={
                    "associations": [
                        {
                            "types": [{"associationCategory": "HUBSPOT_DEFINED", "associationTypeId": 16}],
                            "to": {"id": job.user.hubspotId},
                        }
                    ],
                    "properties": properties,
                },
            )
            job.hubspotId = response.get("id")

    async def create_b2b_demo_request_ticket(self, b2b_demo_request: B2BDemoRequest):
        logger.info(f"Creating a ticket for a B2B demo request for company {b2b_demo_request.companyName}")
        details = self.__to_rich_text(
            {
                "Company Name": b2b_demo_request.companyName,
                "Business Email": b2b_demo_request.businessEmail,
                "Number of Properties Managed": b2b_demo_request.numberOfPropertiesManaged,
            }
        )
        await self._post(
            url="https://api.hubapi.com/crm/v3/objects/tickets",
            data={
                "properties": {
                    "hey_alfie_details": details,
                    "subject": f"B2B request for company {b2b_demo_request.companyName}",
                    "hs_pipeline": "0",
                    "hs_pipeline_stage": "1",
                }
            },
        )

    def __job_to_details(self, job: Job, document_urls: list[str] = None):
        if document_urls is None:
            document_urls = []

        content_dict = {
            "Headline": job.headline,
            "Subtitle": job.subTitle,
            "Details": job.details,
            "Urgency": job.urgency,
            "Availability": job.availability,
            "Timestamp": job.timestamp,
            "Job ID": job.id,
            "Project ID": job.projectId,
            "Chat ID": job.chatId,
            "User ID": job.userId,
            "Property ID": job.propertyId,
            "Created At": job.created_at,
            "Updated At": job.updated_at,
            "Hubspot ID": job.hubspotId,
            "Hubspot Sync At": job.hubspotSyncAt,
            "Documents": [f'<a href="{url}">Document {i + 1}</a>' for i, url in enumerate(document_urls)],
        }
        return self.__to_rich_text(content_dict)

    async def upsert_job_tickets(
        self, jobs_with_docs: list[JobWithDocumentsUrls], sync_time=datetime.now()
    ) -> list[Job]:
        if len(jobs_with_docs) > self.get_batch_size_limit():
            raise HubspotBatchLimitExceededException(self.get_batch_size_limit())

        if not jobs_with_docs:
            return []

        # Separate jobs based on whether they have hubspotId
        jobs_to_update = [jwd for jwd in jobs_with_docs if jwd.job.hubspotId is not None]
        jobs_to_upsert = [jwd for jwd in jobs_with_docs if jwd.job.hubspotId is None]

        all_jobs = []

        # Handle jobs that need to be updated (have hubspotId)
        if jobs_to_update:
            jobs = [job_with_docs.job for job_with_docs in jobs_to_update]

            for job in jobs:
                job.hubspotSyncAt = sync_time

            job_ids = [str(job.id) for job in jobs]
            logger.info(f"Updating job tickets for jobs ids: {', '.join(job_ids)}")

            response_json = await self._post(
                url="https://api.hubapi.com/crm/v3/objects/tickets/batch/update",
                data={
                    "inputs": [
                        {
                            "id": job_with_docs.job.hubspotId,
                            "properties": {
                                "hey_alfie_details": self.__job_to_details(
                                    job_with_docs.job, job_with_docs.document_urls
                                ),
                                "hey_alfie_job_reference": job_with_docs.job.reference,
                                "subject": job_with_docs.job.headline,
                            },
                        }
                        for job_with_docs in jobs_to_update
                    ]
                },
            )

            status = response_json.get("status")
            if status != "COMPLETE":
                raise Exception(f"Non-complete status in response: {status}")
            logger.info("Jobs updated successfully")
            all_jobs.extend(jobs)

        # Handle jobs that need to be upserted (no hubspotId)
        for job_with_docs in jobs_to_upsert:
            await self.upsert_job_ticket(job_with_docs, sync_time)
            all_jobs.append(job_with_docs.job)

        return all_jobs


class HubspotBatchLimitExceededException(Exception):
    def __init__(self, batch_limit: int):
        self.batch_limit = batch_limit
        super().__init__(f"Hubspot batch limit of {batch_limit} exceeded.")
