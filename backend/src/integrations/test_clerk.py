import json
from unittest.mock import AsyncMock, patch

import pytest
from clerk_backend_api import models as clerk_models, EmailAddressObject, PhoneNumberObject

from src.integrations.clerk import <PERSON><PERSON><PERSON>, ClerkUser
from src.integrations.clerk import ClerkWebhookUserCreateEvent


@pytest.fixture
def webhook_event():
    return ClerkWebhookUserCreateEvent(
        id="user_123",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        primary_email_address_id="email_123",
        email_addresses=[
            {"id": "email_123", "email_address": "<EMAIL>", "verification": {"status": "verified"}}
        ],
        primary_phone_number_id="phone_123",
        phone_numbers=[{"id": "phone_123", "phone_number": "+**********", "verification": {"status": "verified"}}],
        private_metadata={"user_id": "1"},
    )


@pytest.fixture
def clerk_sdk_user():
    return clerk_models.User(
        id="user_123",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        primary_email_address_id="email_123",
        email_addresses=[
            clerk_models.EmailAddress(
                id="email_123",
                email_address="<EMAIL>",
                created_at=1733394862537,
                updated_at=1733394862537,
                linked_to=[],
                object=EmailAddressObject.EMAIL_ADDRESS,
                reserved=False,
                verification=clerk_models.Otp(
                    status=clerk_models.VerificationStatus.VERIFIED,
                    expire_at=1743394862537,
                    attempts=1,
                    strategy=clerk_models.Strategy.EMAIL_CODE,
                ),
            )
        ],
        primary_phone_number_id="phone_123",
        phone_numbers=[
            clerk_models.PhoneNumber(
                id="phone_123",
                phone_number="+**********",
                created_at=1733394862537,
                updated_at=1733394862537,
                linked_to=[],
                object=PhoneNumberObject.PHONE_NUMBER,
                reserved=False,
                verification=clerk_models.VerificationOTP(
                    status=clerk_models.OTPVerificationStatus.VERIFIED,
                    expire_at=1743394862537,
                    attempts=1,
                    strategy=clerk_models.OTPVerificationStrategy.PHONE_CODE,
                ),
            )
        ],
        private_metadata={"user_id": "1"},
    )


def test_from_webhook_create_event(webhook_event):
    clerk_user = ClerkUser.from_webhook(webhook_event)
    assert clerk_user.clerk_id == "user_123"
    assert clerk_user.primary_email == "<EMAIL>"
    assert clerk_user.first_name == "John"
    assert clerk_user.last_name == "Doe"
    assert clerk_user.primary_phone_number == "+**********"


def test_from_clerk_sdk_user(clerk_sdk_user):
    clerk_user = ClerkUser.from_webhook(clerk_sdk_user)
    assert clerk_user.clerk_id == "user_123"
    assert clerk_user.primary_email == "<EMAIL>"
    assert clerk_user.first_name == "John"
    assert clerk_user.last_name == "Doe"
    assert clerk_user.primary_phone_number == "+**********"


def test_get_primary_email_from_list_of_emails():
    contact = ClerkUser.get_optional_contact(
        "email_123",
        "email_address",
        [{"id": "email_123", "email_address": "<EMAIL>", "verification": {"status": "verified"}}],
    )
    assert contact.value == "<EMAIL>"
    assert contact.is_primary
    assert contact.is_verified


def test_get_primary_phone_from_list_of_phones():
    contact = ClerkUser.get_optional_contact(
        "phone_123",
        "phone_number",
        [{"id": "phone_123", "phone_number": "+48123123123", "verification": {"status": "verified"}}],
    )
    assert contact.value == "+48123123123"
    assert contact.is_primary
    assert contact.is_verified


def test_no_optional_contact():
    contact = ClerkUser.get_optional_contact(
        "phone_123",
        "phone_number",
        [],
    )
    assert contact is None


def test_no_mandatory_contact():
    with pytest.raises(Exception):
        ClerkUser.get_mandatory_contact(
            "phone_123",
            "phone_number",
            [],
        )


def test_get_primary_phone_number_from_list_of_phone_numbers():
    contact = ClerkUser.get_mandatory_contact(
        "phone_123",
        "phone_number",
        [{"id": "phone_123", "phone_number": "+**********", "verification": {"status": "verified"}}],
    )
    assert contact.value == "+**********"
    assert contact.is_verified


@pytest.fixture
def clerk_api():
    return ClerkAPI(api_key="test_api_key", public_key="test_public_key", permitted_origins=["http://localhost:8000"])


@pytest.mark.asyncio
async def test_get_user_by_clerk_id(clerk_api, clerk_sdk_user):
    with patch("src.integrations.clerk.Clerk") as MockClerk:
        mock_clerk_instance = MockClerk.return_value.__aenter__.return_value
        mock_clerk_instance.users.get_async = AsyncMock(return_value=clerk_sdk_user)

        result = await clerk_api.get_user_by_clerk_id("user_123")

        assert result.clerk_id == "user_123"
        assert result.primary_email == "<EMAIL>"
        assert result.first_name == "John"
        assert result.last_name == "Doe"
        assert result.primary_phone_number == "+**********"


@pytest.mark.asyncio
async def test_get_user_by_clerk_id_not_found(clerk_api):
    with patch("src.integrations.clerk.Clerk") as MockClerk:
        mock_clerk_instance = MockClerk.return_value.__aenter__.return_value
        mock_clerk_instance.users.get_async = AsyncMock(return_value=None)

        result = await clerk_api.get_user_by_clerk_id("non_existent_user")

        assert result is None


async def test_create_user(clerk_api, clerk_sdk_user):
    with patch("src.integrations.clerk.Clerk") as MockClerk:
        mock_clerk_instance = MockClerk.return_value.__aenter__.return_value
        mock_clerk_instance.users.create_async = AsyncMock(return_value=clerk_sdk_user)
        mock_clerk_instance.email_addresses.update_async = AsyncMock()

        result = await clerk_api.create_user(email="<EMAIL>", first_name="John", last_name="Doe")
        assert result.clerk_id == "user_123"
        assert result.primary_email == "<EMAIL>"
        assert result.first_name == "John"
        assert result.last_name == "Doe"

        assert mock_clerk_instance.users.create_async.called
        assert mock_clerk_instance.email_addresses.update_async.called


async def test_create_user_with_verified_email(clerk_api, clerk_sdk_user):
    with patch("src.integrations.clerk.Clerk") as MockClerk:
        mock_clerk_instance = MockClerk.return_value.__aenter__.return_value
        mock_clerk_instance.users.create_async = AsyncMock(return_value=clerk_sdk_user)
        mock_clerk_instance.email_addresses.update_async = AsyncMock()

        result = await clerk_api.create_user(
            email="<EMAIL>", first_name="John", last_name="Doe", is_email_verified=True
        )
        assert result.clerk_id == "user_123"
        assert result.primary_email == "<EMAIL>"
        assert result.first_name == "John"
        assert result.last_name == "Doe"

        assert mock_clerk_instance.users.create_async.called
        assert not mock_clerk_instance.email_addresses.update_async.called


def test_from_update_event_only_unverified_email():
    json_payload = """
        {
          "data": {
            "backup_code_enabled": false,
            "banned": false,
            "create_organization_enabled": true,
            "created_at": 1750144570975,
            "delete_self_enabled": false,
            "email_addresses": [
              {
                "created_at": *************,
                "email_address": "<EMAIL>",
                "id": "idn_934857893459",
                "linked_to": [],
                "matches_sso_connection": false,
                "object": "email_address",
                "reserved": true,
                "updated_at": *************,
                "verification": null
              }
            ],
            "enterprise_accounts": [],
            "external_accounts": [],
            "external_id": null,
            "first_name": "John",
            "has_image": false,
            "id": "user_123123123",
            "image_url": "https://img.clerk.com/dummy",
            "last_active_at": *************,
            "last_name": "Doe",
            "last_sign_in_at": *************,
            "legal_accepted_at": null,
            "locked": false,
            "lockout_expires_in_seconds": null,
            "mfa_disabled_at": null,
            "mfa_enabled_at": null,
            "object": "user",
            "passkeys": [],
            "password_enabled": true,
            "phone_numbers": [
              {
                "backup_codes": null,
                "created_at": *************,
                "default_second_factor": false,
                "id": "user_123123123",
                "linked_to": [],
                "object": "phone_number",
                "phone_number": "+**********",
                "reserved": false,
                "reserved_for_second_factor": false,
                "updated_at": 1750145374107,
                "verification": {
                  "attempts": 1,
                  "channel": "sms",
                  "expire_at": *************,
                  "status": "verified",
                  "strategy": "phone_code"
                }
              }
            ],
            "primary_email_address_id": null,
            "primary_phone_number_id": "user_123123123",
            "primary_web3_wallet_id": null,
            "private_metadata": {},
            "profile_image_url": "https://www.gravatar.com/avatar?d=mp",
            "public_metadata": {},
            "saml_accounts": [],
            "totp_enabled": false,
            "two_factor_enabled": false,
            "unsafe_metadata": {
              "visitedOnboarding": true
            },
            "updated_at": *************,
            "username": null,
            "verification_attempts_remaining": 100,
            "web3_wallets": []
          },
          "event_attributes": {
            "http_request": {
              "client_ip": "*************",
              "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"
            }
          },
          "instance_id": "ins_2pTWgqXt6NCxGuuZaIAVR8iYnqH",
          "object": "event",
          "timestamp": *************,
          "type": "user.updated"
        }
        """
    json_event = json.loads(json_payload)
    event = ClerkWebhookUserCreateEvent.model_validate(json_event["data"])
    clerk_user = ClerkUser.from_webhook(event)

    assert clerk_user.clerk_id == "user_123123123"
    assert clerk_user.primary_email == "<EMAIL>"
    assert clerk_user.first_name == "John"
    assert clerk_user.last_name == "Doe"
    assert clerk_user.primary_phone_number == "+**********"


def test_from_update_event_verified_and_unverified_emails():
    json_payload = """
        {
          "data": {
            "backup_code_enabled": false,
            "banned": false,
            "create_organization_enabled": true,
            "created_at": 1750144570975,
            "delete_self_enabled": false,
            "email_addresses": [
              {
                "created_at": *************,
                "email_address": "<EMAIL>",
                "id": "idn_934857893459",
                "linked_to": [],
                "matches_sso_connection": false,
                "object": "email_address",
                "reserved": true,
                "updated_at": *************,
                "verification": null
              },
              {
                "created_at": *************,
                "email_address": "<EMAIL>",
                "id": "idn_92349328948324",
                "linked_to": [],
                "matches_sso_connection": false,
                "object": "email_address",
                "reserved": true,
                "updated_at": *************,
                "verification": {"status": "verified"}
              }
            ],
            "enterprise_accounts": [],
            "external_accounts": [],
            "external_id": null,
            "first_name": "John",
            "has_image": false,
            "id": "user_123123123",
            "image_url": "https://img.clerk.com/dummy",
            "last_active_at": *************,
            "last_name": "Doe",
            "last_sign_in_at": *************,
            "legal_accepted_at": null,
            "locked": false,
            "lockout_expires_in_seconds": null,
            "mfa_disabled_at": null,
            "mfa_enabled_at": null,
            "object": "user",
            "passkeys": [],
            "password_enabled": true,
            "phone_numbers": [
              {
                "backup_codes": null,
                "created_at": *************,
                "default_second_factor": false,
                "id": "user_123123123",
                "linked_to": [],
                "object": "phone_number",
                "phone_number": "+**********",
                "reserved": false,
                "reserved_for_second_factor": false,
                "updated_at": 1750145374107,
                "verification": {
                  "attempts": 1,
                  "channel": "sms",
                  "expire_at": *************,
                  "status": "verified",
                  "strategy": "phone_code"
                }
              }
            ],
            "primary_email_address_id": null,
            "primary_phone_number_id": "user_123123123",
            "primary_web3_wallet_id": null,
            "private_metadata": {},
            "profile_image_url": "https://www.gravatar.com/avatar?d=mp",
            "public_metadata": {},
            "saml_accounts": [],
            "totp_enabled": false,
            "two_factor_enabled": false,
            "unsafe_metadata": {
              "visitedOnboarding": true
            },
            "updated_at": *************,
            "username": null,
            "verification_attempts_remaining": 100,
            "web3_wallets": []
          },
          "event_attributes": {
            "http_request": {
              "client_ip": "*************",
              "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"
            }
          },
          "instance_id": "ins_2pTWgqXt6NCxGuuZaIAVR8iYnqH",
          "object": "event",
          "timestamp": *************,
          "type": "user.updated"
        }
        """
    json_event = json.loads(json_payload)
    event = ClerkWebhookUserCreateEvent.model_validate(json_event["data"])
    clerk_user = ClerkUser.from_webhook(event)

    assert clerk_user.primary_email == "<EMAIL>"


def test_from_update_event_multiple_emails():
    json_payload = """
        {
          "data": {
            "backup_code_enabled": false,
            "banned": false,
            "create_organization_enabled": true,
            "created_at": 1750144570975,
            "delete_self_enabled": false,
            "email_addresses": [
              {
                "created_at": *************,
                "email_address": "<EMAIL>",
                "id": "idn_934857893459",
                "linked_to": [],
                "matches_sso_connection": false,
                "object": "email_address",
                "reserved": true,
                "updated_at": *************,
                "verification": {"status": "verified"}
              },
              {
                "created_at": *************,
                "email_address": "<EMAIL>",
                "id": "idn_92349328948324",
                "linked_to": [],
                "matches_sso_connection": false,
                "object": "email_address",
                "reserved": true,
                "updated_at": *************,
                "verification": {"status": "verified"}
              }
            ],
            "enterprise_accounts": [],
            "external_accounts": [],
            "external_id": null,
            "first_name": "John",
            "has_image": false,
            "id": "user_123123123",
            "image_url": "https://img.clerk.com/dummy",
            "last_active_at": *************,
            "last_name": "Doe",
            "last_sign_in_at": *************,
            "legal_accepted_at": null,
            "locked": false,
            "lockout_expires_in_seconds": null,
            "mfa_disabled_at": null,
            "mfa_enabled_at": null,
            "object": "user",
            "passkeys": [],
            "password_enabled": true,
            "phone_numbers": [
              {
                "backup_codes": null,
                "created_at": *************,
                "default_second_factor": false,
                "id": "user_123123123",
                "linked_to": [],
                "object": "phone_number",
                "phone_number": "+**********",
                "reserved": false,
                "reserved_for_second_factor": false,
                "updated_at": 1750145374107,
                "verification": {
                  "attempts": 1,
                  "channel": "sms",
                  "expire_at": *************,
                  "status": "verified",
                  "strategy": "phone_code"
                }
              }
            ],
            "primary_email_address_id": "idn_92349328948324",
            "primary_phone_number_id": "user_123123123",
            "primary_web3_wallet_id": null,
            "private_metadata": {},
            "profile_image_url": "https://www.gravatar.com/avatar?d=mp",
            "public_metadata": {},
            "saml_accounts": [],
            "totp_enabled": false,
            "two_factor_enabled": false,
            "unsafe_metadata": {
              "visitedOnboarding": true
            },
            "updated_at": *************,
            "username": null,
            "verification_attempts_remaining": 100,
            "web3_wallets": []
          },
          "event_attributes": {
            "http_request": {
              "client_ip": "*************",
              "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"
            }
          },
          "instance_id": "ins_2pTWgqXt6NCxGuuZaIAVR8iYnqH",
          "object": "event",
          "timestamp": *************,
          "type": "user.updated"
        }
        """
    json_event = json.loads(json_payload)
    event = ClerkWebhookUserCreateEvent.model_validate(json_event["data"])
    clerk_user = ClerkUser.from_webhook(event)

    assert clerk_user.primary_email == "<EMAIL>"
