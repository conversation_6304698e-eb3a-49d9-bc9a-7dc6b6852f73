import datetime
from unittest.mock import AsyncMock

import pytest
from aioresponses import aioresponses, CallbackResult
from yarl import URL

from .hubspot import HubSpotAPI, HubspotBatchLimitExceededException, JobWithDocumentsUrls
from ..db_models.job import Job
from ..db_models.relationships import UserPropertyRelationType
from ..db_models.user import User
from ..schemas import B2BDemoRequest

NOW = datetime.datetime(2025, 6, 4, 12, 20, 31, 502937, tzinfo=datetime.timezone.utc)

dummy_details = (
    "Plumber needed to repair a slowly dripping pipe located under the kitchen sink. "
    "The leak started today and requires urgent attention."
)


@pytest.fixture
def user_1():
    return User(
        id=1,
        clerkId="user_1",
        email="<EMAIL>",
        firstName="Test",
        lastName="User 1",
        phoneNumber=None,
        isEmailVerified=True,
        isPhoneNumberVerified=None,
        isWelcomeEmailSent=False,
        mainUsage=None,
        created_at=datetime.datetime.fromisoformat("2025-05-19 12:07:19.000452"),
        updated_at=datetime.datetime.fromisoformat("2025-05-19 12:07:19.000452"),
    )


@pytest.fixture
def user_2():
    return User(
        id=2,
        clerkId="user_2",
        email="<EMAIL>",
        firstName="Test",
        lastName="User 2",
        phoneNumber="1234567890",
        isEmailVerified=False,
        isPhoneNumberVerified=True,
        isWelcomeEmailSent=True,
        mainUsage=UserPropertyRelationType.landlord,
        created_at=datetime.datetime.fromisoformat("2025-05-20 10:00:00"),
        updated_at=datetime.datetime.fromisoformat("2025-05-20 10:00:00"),
    )


@pytest.fixture
def user_1_with_hubspot():
    return User(
        id=1,
        clerkId="user_1",
        email="<EMAIL>",
        firstName="Test",
        lastName="User 1",
        phoneNumber=None,
        isEmailVerified=True,
        isPhoneNumberVerified=None,
        isWelcomeEmailSent=False,
        mainUsage=None,
        created_at=datetime.datetime.fromisoformat("2025-05-19 12:07:19.000452"),
        updated_at=datetime.datetime.fromisoformat("2025-05-19 12:07:19.000452"),
        hubspotId="************",
        hubspotSyncAt=NOW,
    )


@pytest.fixture
def user_2_with_hubspot():
    return User(
        id=2,
        clerkId="user_2",
        email="<EMAIL>",
        firstName="Test",
        lastName="User 2",
        phoneNumber="1234567890",
        isEmailVerified=False,
        isPhoneNumberVerified=True,
        isWelcomeEmailSent=True,
        mainUsage=UserPropertyRelationType.landlord,
        created_at=datetime.datetime.fromisoformat("2025-05-20 10:00:00"),
        updated_at=datetime.datetime.fromisoformat("2025-05-20 10:00:00"),
        hubspotId="************",
        hubspotSyncAt=NOW,
    )


@pytest.fixture
def job_basic():
    return Job(
        id=12,
        reference="DUMMY_REFERENCE",
        headline="Leaking Pipe",
        subTitle="Kitchen Sink Pipe Leak Repair",
        details=dummy_details,
        urgency="Urgent (within 24 hours)",
        availability="Any time",
        status="created",
        timestamp=datetime.datetime.fromisoformat("2025-05-30 16:33:55.831894"),
        projectId=12,
        chatId=109,
        userId=1,
        propertyId=1,
        serviceProviderId=None,
        created_at=datetime.datetime.fromisoformat("2025-05-30 16:33:55.831894"),
        updated_at=datetime.datetime.fromisoformat("2025-05-30 16:33:55.831894"),
    )


@pytest.fixture
def job_with_user(user_1_with_hubspot):
    job = Job(
        id=255,
        reference="DUMMY_REFERENCE_1",
        headline="Some Pipe Problem",
        subTitle="Nasty Pipe",
        details=dummy_details,
        urgency="Urgent (within 24 hours)",
        availability="Any time",
        status="created",
        timestamp=datetime.datetime.fromisoformat("2026-05-30 16:33:55.831894"),
        projectId=12,
        chatId=109,
        userId=1,
        propertyId=1,
        serviceProviderId=None,
        created_at=datetime.datetime.fromisoformat("2026-05-30 16:33:55.831894"),
        updated_at=datetime.datetime.fromisoformat("2026-05-30 16:33:55.831894"),
    )
    job.user = user_1_with_hubspot
    return job


@pytest.fixture
def job_with_hubspot_id():
    job = Job(
        id=256,
        reference="DUMMY_REFERENCE_2",
        headline="Some Pipe Problem 2",
        subTitle="Nasty Pipe 2",
        details=dummy_details,
        urgency="Urgent (within 24 hours)",
        availability="Any time",
        status="created",
        timestamp=datetime.datetime.fromisoformat("2026-05-30 16:33:55.831894"),
        projectId=12,
        chatId=109,
        userId=1,
        propertyId=1,
        serviceProviderId=None,
        created_at=datetime.datetime.fromisoformat("2026-05-30 16:33:55.831894"),
        updated_at=datetime.datetime.fromisoformat("2026-05-30 16:33:55.831894"),
    )
    job.user = User(hubspotId="userHubspotId")
    job.hubspotId = "156793524428"
    return job


@pytest.fixture
def job_new_sync(job_basic):
    job_basic.user = User(hubspotId="userHubspotId")
    return job_basic


@pytest.mark.asyncio(loop_scope="session")
class TestHubspotAPI:
    api_key = "DEMO"

    @pytest.mark.asyncio
    async def test_close_session(self):
        api = HubSpotAPI(api_key=self.api_key)
        await api.close()
        assert api._session is None

    @pytest.mark.asyncio
    async def test_sync_users_error(self, user_1):
        with aioresponses() as m:
            m.post(
                "https://api.hubapi.com/crm/v3/objects/contacts/batch/upsert",
                status=400,
            )
            api = HubSpotAPI(api_key=self.api_key)
            with pytest.raises(Exception):
                await api.upsert_contacts([user_1])

    @pytest.mark.asyncio
    async def test_sync_users_empty(self):
        with aioresponses() as m:
            m.post(
                "https://api.hubapi.com/crm/v3/objects/contacts/batch/upsert",
                status=400,
            )
            api = HubSpotAPI(api_key=self.api_key)
            await api.upsert_contacts([])

    @pytest.mark.asyncio
    async def test_sync_users_success(self, user_1, user_2, user_1_with_hubspot, user_2_with_hubspot):
        with aioresponses() as m:
            mock_data = {
                "completedAt": "2025-06-03T21:03:20.176Z",
                "results": [
                    {
                        "archived": False,
                        "createdAt": "2025-06-03T21:03:19.926Z",
                        "id": "************",
                        "new": True,
                        "properties": {
                            "createdate": "2025-06-03T21:03:19.926Z",
                            "email": "<EMAIL>",
                            "firstname": "Test",
                            "hs_all_contact_vids": "************",
                            "hs_associated_target_accounts": "0",
                            "hs_currently_enrolled_in_prospecting_agent": "false",
                            "hs_email_domain": "heyalfie.com",
                            "hs_full_name_or_email": "Test User 1",
                            "hs_is_contact": "true",
                            "hs_is_unworked": "true",
                            "hs_lifecyclestage_lead_date": "2025-06-03T21:03:19.926Z",
                            "hs_marketable_status": "false",
                            "hs_marketable_until_renewal": "false",
                            "hs_membership_has_accessed_private_content": "0",
                            "hs_object_id": "************",
                            "hs_object_source": "INTEGRATION",
                            "hs_object_source_id": "********",
                            "hs_object_source_label": "INTEGRATION",
                            "hs_pipeline": "contacts-lifecycle-pipeline",
                            "hs_prospecting_agent_actively_enrolled_count": "0",
                            "hs_registered_member": "0",
                            "hs_sequences_actively_enrolled_count": "0",
                            "lastmodifieddate": "2025-06-03T21:03:19.926Z",
                            "lastname": "User 1",
                            "lifecyclestage": "lead",
                            "num_notes": "0",
                        },
                        "updatedAt": "2025-06-03T21:03:19.926Z",
                    },
                    {
                        "archived": False,
                        "createdAt": "2025-06-03T21:03:19.926Z",
                        "id": "************",
                        "new": True,
                        "properties": {
                            "createdate": "2025-06-03T21:03:19.926Z",
                            "email": "<EMAIL>",
                            "firstname": "Test",
                            "hs_all_contact_vids": "************",
                            "hs_associated_target_accounts": "0",
                            "hs_currently_enrolled_in_prospecting_agent": "false",
                            "hs_email_domain": "heyalfie.com",
                            "hs_full_name_or_email": "Test User 2",
                            "hs_is_contact": "true",
                            "hs_is_unworked": "true",
                            "hs_lifecyclestage_lead_date": "2025-06-03T21:03:19.926Z",
                            "hs_marketable_status": "false",
                            "hs_marketable_until_renewal": "false",
                            "hs_membership_has_accessed_private_content": "0",
                            "hs_object_id": "************",
                            "hs_object_source": "INTEGRATION",
                            "hs_object_source_id": "********",
                            "hs_object_source_label": "INTEGRATION",
                            "hs_pipeline": "contacts-lifecycle-pipeline",
                            "hs_prospecting_agent_actively_enrolled_count": "0",
                            "hs_registered_member": "0",
                            "hs_searchable_calculated_phone_number": "1234567890",
                            "hs_sequences_actively_enrolled_count": "0",
                            "lastmodifieddate": "2025-06-03T21:03:19.926Z",
                            "lastname": "User 2",
                            "lifecyclestage": "lead",
                            "num_notes": "0",
                            "phone": "1234567890",
                        },
                        "updatedAt": "2025-06-03T21:03:19.926Z",
                    },
                ],
                "startedAt": "2025-06-03T21:03:19.852Z",
                "status": "COMPLETE",
            }

            def callback(url, **kwargs):
                expected_headers = {"Content-Type": "application/json", "authorization": "Bearer DEMO"}
                expected_json = {
                    "inputs": [
                        {
                            "id": "<EMAIL>",
                            "idProperty": "email",
                            "properties": {
                                "email": "<EMAIL>",
                                "firstname": "Test",
                                "lastname": "User 1",
                                "phone": "",
                                "hey_alfie_details": "<b>ID</b><br>\n"
                                "1<br><br>\n"
                                "\n"
                                "<b>Created "
                                "at</b><br>\n"
                                "2025-05-19 "
                                "12:07:19.000452<br><br>\n"
                                "\n"
                                "<b>Updated "
                                "at</b><br>\n"
                                "2025-05-19 "
                                "12:07:19.000452<br><br>\n"
                                "\n"
                                "<b>Is email "
                                "verified?</b><br>\n"
                                "True",
                            },
                        },
                        {
                            "id": "<EMAIL>",
                            "idProperty": "email",
                            "properties": {
                                "email": "<EMAIL>",
                                "firstname": "Test",
                                "lastname": "User 2",
                                "phone": "1234567890",
                                "hey_alfie_details": "<b>ID</b><br>\n"
                                "2<br><br>\n"
                                "\n"
                                "<b>Created "
                                "at</b><br>\n"
                                "2025-05-20 "
                                "10:00:00<br><br>\n"
                                "\n"
                                "<b>Updated "
                                "at</b><br>\n"
                                "2025-05-20 "
                                "10:00:00<br><br>\n"
                                "\n"
                                "<b>Is email "
                                "verified?</b><br>\n"
                                "False<br><br>\n"
                                "\n"
                                "<b>Is phone number "
                                "verified?</b><br>\n"
                                "True<br><br>\n"
                                "\n"
                                "<b>Main "
                                "usage</b><br>\n"
                                "UserPropertyRelationType.landlord",
                            },
                        },
                    ]
                }
                expected_kwargs = {
                    "allow_redirects": True,
                    "data": None,
                    "headers": expected_headers,
                    "json": expected_json,
                }
                assert kwargs == expected_kwargs
                return CallbackResult(payload=mock_data, status=200)

            m.post(
                "https://api.hubapi.com/crm/v3/objects/contacts/batch/upsert",
                callback=callback,
            )

            api = HubSpotAPI(api_key=self.api_key)
            result = await api.upsert_contacts(
                sync_time=NOW,
                users=[user_1, user_2],
            )
            assert len(result) == 2
            assert result[0].to_dict() == user_1_with_hubspot.to_dict()
            assert result[1].to_dict() == user_2_with_hubspot.to_dict()

    @pytest.mark.asyncio
    async def test_sync_job_ticket_error(self, job_with_user):
        with aioresponses() as m:
            m.post("https://api.hubapi.com/crm/v3/objects/tickets/batch/upsert", status=400)
            api = HubSpotAPI(api_key=self.api_key)
            with pytest.raises(Exception):
                await api.upsert_job_ticket(sync_time=NOW, job_with_docs=JobWithDocumentsUrls(job_with_user, []))

    async def test_sync_job_tickets_error(self, job_with_hubspot_id):
        with aioresponses() as m:
            m.post(
                "https://api.hubapi.com/crm/v3/objects/tickets/batch/update",
                status=500,
                payload={"error": "Internal server error"},
            )
            api = HubSpotAPI(self.api_key)
            job_with_docs = JobWithDocumentsUrls(job=job_with_hubspot_id, document_urls=["http://example.com/doc1.pdf"])

            with pytest.raises(Exception):
                await api.upsert_job_tickets([job_with_docs])

    @pytest.mark.asyncio
    async def test_sync_already_synced_job_success(self, job_with_hubspot_id):
        with aioresponses() as m:
            mock_data = {
                "archived": False,
                "createdAt": "2025-06-04T10:18:59.529Z",
                "id": "156793524428",
                "properties": {
                    "createdate": "2025-06-04T10:18:59.529Z",
                    "hs_helpdesk_sort_timestamp": "2025-06-04T10:18:59.529Z",
                    "hs_lastmodifieddate": "2025-06-04T10:19:26.966Z",
                    "hs_object_id": "156793524428",
                    "hs_object_source": "INTEGRATION",
                    "hs_object_source_id": "13750093",
                    "hs_object_source_label": "INTEGRATION",
                    "hs_pipeline": "0",
                    "hs_pipeline_stage": "1",
                    "hs_ticket_id": "156793524428",
                    "subject": "Leaking Pipe",
                },
                "updatedAt": "2025-06-04T10:19:26.966Z",
            }

            def callback(url, **kwargs):
                assert kwargs == {
                    "allow_redirects": True,
                    "data": None,
                    "headers": {"Content-Type": "application/json", "authorization": "Bearer DEMO"},
                    "json": {
                        "properties": {
                            "hey_alfie_details": "<b>Headline</b><br>\n"
                            "Some Pipe Problem 2<br><br>\n"
                            "\n"
                            "<b>Subtitle</b><br>\n"
                            "Nasty Pipe 2<br><br>\n"
                            "\n"
                            "<b>Details</b><br>\n"
                            "Plumber needed to repair a "
                            "slowly dripping pipe located "
                            "under the kitchen sink. The "
                            "leak started today and requires "
                            "urgent attention.<br><br>\n"
                            "\n"
                            "<b>Urgency</b><br>\n"
                            "Urgent (within 24 "
                            "hours)<br><br>\n"
                            "\n"
                            "<b>Availability</b><br>\n"
                            "Any time<br><br>\n"
                            "\n"
                            "<b>Timestamp</b><br>\n"
                            "2026-05-30 "
                            "16:33:55.831894<br><br>\n"
                            "\n"
                            "<b>Job ID</b><br>\n"
                            "256<br><br>\n"
                            "\n"
                            "<b>Project ID</b><br>\n"
                            "12<br><br>\n"
                            "\n"
                            "<b>Chat ID</b><br>\n"
                            "109<br><br>\n"
                            "\n"
                            "<b>User ID</b><br>\n"
                            "1<br><br>\n"
                            "\n"
                            "<b>Property ID</b><br>\n"
                            "1<br><br>\n"
                            "\n"
                            "<b>Created At</b><br>\n"
                            "2026-05-30 "
                            "16:33:55.831894<br><br>\n"
                            "\n"
                            "<b>Updated At</b><br>\n"
                            "2026-05-30 "
                            "16:33:55.831894<br><br>\n"
                            "\n"
                            "<b>Hubspot ID</b><br>\n"
                            "156793524428<br><br>\n"
                            "\n"
                            "<b>Hubspot Sync At</b><br>\n"
                            "2025-06-04 "
                            "12:20:31.502937+00:00<br><br>\n"
                            "\n"
                            "<b>Documents</b><br>\n"
                            "<ul><li><a "
                            'href="https://test-url-1.com">Document '
                            "1</a></li>\n"
                            "<li><a "
                            'href="https://test-url-2.com">Document '
                            "2</a></li></ul>",
                            "hey_alfie_job_reference": "DUMMY_REFERENCE_2",
                            "hs_pipeline": "0",
                            "hs_pipeline_stage": "1",
                            "subject": "Some Pipe Problem 2",
                        }
                    },
                }
                return CallbackResult(payload=mock_data, status=200)

            m.patch(
                "https://api.hubapi.com/crm/v3/objects/tickets/156793524428",
                callback=callback,
            )

            api = HubSpotAPI(api_key=self.api_key)
            await api.upsert_job_ticket(
                sync_time=NOW,
                job_with_docs=JobWithDocumentsUrls(
                    job_with_hubspot_id, ["https://test-url-1.com", "https://test-url-2.com"]
                ),
            )

            assert job_with_hubspot_id.hubspotId == "156793524428"
            assert job_with_hubspot_id.hubspotSyncAt == NOW

    @pytest.mark.asyncio
    async def test_sync_new_job_success(self, job_new_sync):
        with aioresponses() as m:
            mock_data = {
                "archived": False,
                "createdAt": "2025-06-04T11:27:25.054Z",
                "id": "156800762083",
                "properties": {
                    "createdate": "2025-06-04T11:27:25.054Z",
                    "hs_helpdesk_sort_timestamp": "2025-06-04T11:27:25.054Z",
                    "hs_is_visible_in_help_desk": "true",
                    "hs_last_message_from_visitor": "false",
                    "hs_lastmodifieddate": "2025-06-04T11:27:25.054Z",
                    "hs_num_associated_companies": "0",
                    "hs_num_associated_conversations": "0",
                    "hs_num_times_contacted": "0",
                    "hs_object_id": "156800762083",
                    "hs_object_source": "INTEGRATION",
                    "hs_object_source_id": "13750093",
                    "hs_object_source_label": "INTEGRATION",
                    "hs_pipeline": "0",
                    "hs_pipeline_stage": "1",
                    "hs_ticket_id": "156800762083",
                    "num_notes": "0",
                    "subject": "Leaking Pipe",
                },
                "updatedAt": "2025-06-04T11:27:25.054Z",
            }

            def callback(url, **kwargs):
                assert kwargs == {
                    "allow_redirects": True,
                    "data": None,
                    "headers": {
                        "Content-Type": "application/json",
                        "authorization": "Bearer DEMO",
                    },
                    "json": {
                        "associations": [
                            {
                                "types": [
                                    {
                                        "associationCategory": "HUBSPOT_DEFINED",
                                        "associationTypeId": 16,
                                    }
                                ],
                                "to": {"id": "userHubspotId"},
                            }
                        ],
                        "properties": {
                            "hs_pipeline": "0",
                            "hs_pipeline_stage": "1",
                            "subject": "Leaking Pipe",
                            "hey_alfie_job_reference": "DUMMY_REFERENCE",
                            "hey_alfie_details": "<b>Headline</b><br>\n"
                            "Leaking Pipe<br><br>\n"
                            "\n"
                            "<b>Subtitle</b><br>\n"
                            "Kitchen Sink Pipe Leak "
                            "Repair<br><br>\n"
                            "\n"
                            "<b>Details</b><br>\n"
                            "Plumber needed to repair a "
                            "slowly dripping pipe located "
                            "under the kitchen sink. The "
                            "leak started today and requires "
                            "urgent attention.<br><br>\n"
                            "\n"
                            "<b>Urgency</b><br>\n"
                            "Urgent (within 24 "
                            "hours)<br><br>\n"
                            "\n"
                            "<b>Availability</b><br>\n"
                            "Any time<br><br>\n"
                            "\n"
                            "<b>Timestamp</b><br>\n"
                            "2025-05-30 "
                            "16:33:55.831894<br><br>\n"
                            "\n"
                            "<b>Job ID</b><br>\n"
                            "12<br><br>\n"
                            "\n"
                            "<b>Project ID</b><br>\n"
                            "12<br><br>\n"
                            "\n"
                            "<b>Chat ID</b><br>\n"
                            "109<br><br>\n"
                            "\n"
                            "<b>User ID</b><br>\n"
                            "1<br><br>\n"
                            "\n"
                            "<b>Property ID</b><br>\n"
                            "1<br><br>\n"
                            "\n"
                            "<b>Created At</b><br>\n"
                            "2025-05-30 "
                            "16:33:55.831894<br><br>\n"
                            "\n"
                            "<b>Updated At</b><br>\n"
                            "2025-05-30 "
                            "16:33:55.831894<br><br>\n"
                            "\n"
                            "<b>Hubspot Sync At</b><br>\n"
                            "2025-06-04 "
                            "12:20:31.502937+00:00<br><br>\n"
                            "\n"
                            "<b>Documents</b><br>\n"
                            "<ul><li><a "
                            'href="https://test-url-1.com">Document '
                            "1</a></li>\n"
                            "<li><a "
                            'href="https://test-url-2.com">Document '
                            "2</a></li></ul>",
                        },
                    },
                }
                return CallbackResult(payload=mock_data, status=200)

            m.post("https://api.hubapi.com/crm/v3/objects/tickets", callback=callback)

            api = HubSpotAPI(api_key=self.api_key)
            await api.upsert_job_ticket(
                sync_time=NOW,
                job_with_docs=JobWithDocumentsUrls(job_new_sync, ["https://test-url-1.com", "https://test-url-2.com"]),
            )

            assert job_new_sync.hubspotId == "156800762083"
            assert job_new_sync.hubspotSyncAt == NOW

    @pytest.mark.asyncio
    async def test_sync_b2b_demo_request(self):
        content = (
            "<b>Company Name</b><br>\n"
            "B2B Demo<br><br>\n"
            "\n"
            "<b>Business Email</b><br>\n"
            "<EMAIL><br><br>\n"
            "\n"
            "<b>Number of Properties "
            "Managed</b><br>\n"
            "5"
        )

        with aioresponses() as m:
            mock_data = {
                "archived": False,
                "createdAt": "2025-06-04T19:59:35.815Z",
                "id": "157003674858",
                "properties": {
                    "hey_alfie_details": content,
                    "createdate": "2025-06-04T19:59:35.815Z",
                    "hs_helpdesk_sort_timestamp": "2025-06-04T19:59:35.815Z",
                    "hs_is_visible_in_help_desk": "true",
                    "hs_last_message_from_visitor": "false",
                    "hs_lastmodifieddate": "2025-06-04T19:59:35.815Z",
                    "hs_num_associated_companies": "0",
                    "hs_num_associated_conversations": "0",
                    "hs_num_times_contacted": "0",
                    "hs_object_id": "157003674858",
                    "hs_object_source": "INTEGRATION",
                    "hs_object_source_id": "13750093",
                    "hs_object_source_label": "INTEGRATION",
                    "hs_pipeline": "0",
                    "hs_pipeline_stage": "1",
                    "hs_ticket_id": "157003674858",
                    "num_notes": "0",
                    "subject": "B2B request for company B2B Demo",
                },
                "updatedAt": "2025-06-04T19:59:35.815Z",
            }

            def callback(url, **kwargs):
                assert kwargs == {
                    "allow_redirects": True,
                    "data": None,
                    "headers": {
                        "Content-Type": "application/json",
                        "authorization": "Bearer DEMO",
                    },
                    "json": {
                        "properties": {
                            "hey_alfie_details": content,
                            "hs_pipeline": "0",
                            "hs_pipeline_stage": "1",
                            "subject": "B2B request for company B2B Demo",
                        }
                    },
                }
                return CallbackResult(payload=mock_data, status=200)

            m.post("https://api.hubapi.com/crm/v3/objects/tickets", callback=callback)

            api = HubSpotAPI(api_key=self.api_key)
            await api.create_b2b_demo_request_ticket(
                B2BDemoRequest(
                    companyName="B2B Demo",
                    businessEmail="<EMAIL>",
                    numberOfPropertiesManaged="5",
                )
            )
            m.assert_called()

    @pytest.mark.asyncio
    async def test_sync_jobs_update_error(self, job_with_hubspot_id):
        with aioresponses() as m:
            # Mock the batch upsert request to return an error
            m.post(
                "https://api.hubapi.com/crm/v3/objects/tickets/batch/update",
                status=400,
                payload={"error": "Bad Request"},
            )

            hubspot_api = HubSpotAPI(api_key=self.api_key)
            jobs = [JobWithDocumentsUrls(job_with_hubspot_id, [])]

            with pytest.raises(Exception) as exc_info:
                await hubspot_api.upsert_job_tickets(jobs, sync_time=NOW)

            assert "Failed to extract JSON from response: 400" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_sync_jobs_empty(self):
        hubspot_api = HubSpotAPI(api_key=self.api_key)
        result = await hubspot_api.upsert_job_tickets([], sync_time=NOW)
        assert result == []

    @pytest.mark.asyncio
    async def test_sync_jobs_batch_limit_exceeded(self, job_basic):
        hubspot_api = HubSpotAPI(api_key=self.api_key)
        # Create a list with more jobs than the batch limit
        jobs = [job_basic] * (hubspot_api.get_batch_size_limit() + 1)

        with pytest.raises(HubspotBatchLimitExceededException) as exc_info:
            await hubspot_api.upsert_job_tickets(jobs, sync_time=NOW)

        assert exc_info.value.batch_limit == hubspot_api.get_batch_size_limit()

    @pytest.mark.asyncio
    async def test_sync_jobs_success_new_jobs(self, job_with_hubspot_id, job_with_user):
        with aioresponses() as m:
            m.post(
                "https://api.hubapi.com/crm/v3/objects/tickets/batch/update",
                payload={
                    "status": "COMPLETE",
                    "results": [
                        {
                            "id": "156793524428",
                            "properties": {
                                "hey_alfie_job_reference": job_with_hubspot_id.reference,
                                "subject": job_with_hubspot_id.headline,
                                "hs_pipeline": "0",
                                "hs_pipeline_stage": "1",
                            },
                        },
                    ],
                },
            )
            m.post(
                "https://api.hubapi.com/crm/v3/objects/tickets/batch/update",
                payload={
                    "status": "COMPLETE",
                    "results": [
                        {
                            "id": "156793524428",
                            "properties": {
                                "hey_alfie_job_reference": job_with_hubspot_id.reference,
                                "subject": job_with_hubspot_id.headline,
                                "hs_pipeline": "0",
                                "hs_pipeline_stage": "1",
                            },
                        },
                    ],
                },
            )

            hubspot_api = HubSpotAPI(api_key=self.api_key)

            def fill_dummy_hubspot_id(job_with_docs: JobWithDocumentsUrls, sync_time) -> Job:
                job_with_docs.job.hubspotId = "dummy_new_hubspot_id"
                job_with_docs.job.hubspotSyncAt = NOW
                return job_with_docs.job

            hubspot_api.upsert_job_ticket = AsyncMock()
            hubspot_api.upsert_job_ticket.side_effect = fill_dummy_hubspot_id

            jobs = [JobWithDocumentsUrls(job_with_hubspot_id, []), JobWithDocumentsUrls(job_with_user, [])]

            # Ensure the job doesn't have hubspot IDs initially
            assert job_with_user.hubspotId is None
            assert job_with_user.hubspotSyncAt is None

            result = await hubspot_api.upsert_job_tickets(jobs, sync_time=NOW)

            # Verify the request was made correctly
            assert len(m.requests) == 1
            request = m.requests[("POST", URL("https://api.hubapi.com/crm/v3/objects/tickets/batch/update"))][0][1]

            # Verify request headers
            assert request["headers"]["authorization"] == f"Bearer {self.api_key}"
            assert request["headers"]["Content-Type"] == "application/json"
            assert request["json"] == {
                "inputs": [
                    {
                        "id": "156793524428",
                        "properties": {
                            "hey_alfie_details": "<b>Headline</b><br>\nSome Pipe Problem 2<br><br>\n\n<b>Subtitle</b>"
                            "<br>\nNasty Pipe 2<br><br>\n\n<b>Details</b><br>\nPlumber needed to repair "
                            "a slowly dripping pipe located under the kitchen sink. The leak started "
                            "today and requires urgent attention.<br><br>\n\n<b>Urgency</b><br>\n"
                            "Urgent (within 24 hours)<br><br>\n\n<b>Availability</b><br>\nAny time"
                            "<br><br>\n\n<b>Timestamp</b><br>\n2026-05-30 16:33:55.831894<br><br>\n\n"
                            "<b>Job ID</b><br>\n256<br><br>\n\n<b>Project ID</b><br>\n12<br><br>\n\n"
                            "<b>Chat ID</b><br>\n109<br><br>\n\n<b>User ID</b><br>\n1<br><br>\n\n"
                            "<b>Property ID</b><br>\n1<br><br>\n\n<b>Created At</b><br>\n"
                            "2026-05-30 16:33:55.831894<br><br>\n\n<b>Updated At</b><br>\n"
                            "2026-05-30 16:33:55.831894<br><br>\n\n<b>Hubspot ID</b><br>\n"
                            "156793524428<br><br>\n\n<b>Hubspot Sync At</b><br>\n"
                            "2025-06-04 12:20:31.502937+00:00<br><br>\n\n<b>Documents</b>"
                            "<br>\n<ul></ul>",
                            "hey_alfie_job_reference": "DUMMY_REFERENCE_2",
                            "subject": "Some Pipe Problem 2",
                        },
                    }
                ]
            }

            assert len(result) == 2
            assert job_with_hubspot_id.hubspotId == "156793524428"
            assert job_with_user.hubspotId == "dummy_new_hubspot_id"
            assert job_with_hubspot_id.hubspotSyncAt == NOW
            assert job_with_user.hubspotSyncAt == NOW
            hubspot_api.upsert_job_ticket.assert_called()
