import logging
import re
from typing import Any

from clerk_backend_api import Clerk
from clerk_backend_api import models as clerk_models
from clerk_backend_api.jwks_helpers import AuthenticateRequestOptions, RequestState, AuthStatus
from fastapi import Request
from pydantic import BaseModel

logger = logging.getLogger("uvicorn")


class ClerkWebhookUserCreateEvent(BaseModel):
    id: str
    first_name: str
    last_name: str | None
    primary_email_address_id: str | None
    email_addresses: list[dict]
    primary_phone_number_id: str | None
    phone_numbers: list[dict]
    private_metadata: dict


class ClerkWebhookUserUpdateEvent(ClerkWebhookUserCreateEvent):
    pass


class ClerkWebhookUserDeleteEvent(BaseModel):
    deleted: bool
    id: str


class ClerkContact(BaseModel):
    value: str
    is_primary: bool
    is_verified: bool


class ClerkUser(BaseModel):
    user_id: int | None
    clerk_id: str
    primary_email: str
    is_primary_email_verified: bool
    first_name: str
    last_name: str | None
    primary_phone_number: str | None
    is_primary_phone_number_verified: bool | None

    @classmethod
    def from_webhook(
        cls, user: ClerkWebhookUserCreateEvent | ClerkWebhookUserUpdateEvent | clerk_models.User
    ) -> "ClerkUser":
        email_contact = cls.get_mandatory_contact(
            primary_id=user.primary_email_address_id, key="email_address", raw_contacts=user.email_addresses
        )
        phone_contact = cls.get_optional_contact(
            primary_id=user.primary_phone_number_id, key="phone_number", raw_contacts=user.phone_numbers
        )
        user_id = user.private_metadata.get("user_id")
        return ClerkUser(
            user_id=user_id,
            clerk_id=user.id,
            primary_email=email_contact.value,
            is_primary_email_verified=email_contact.is_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            primary_phone_number=phone_contact.value if phone_contact else None,
            is_primary_phone_number_verified=phone_contact.is_verified if phone_contact else None,
        )

    @classmethod
    def _get_from_model(cls, model: dict | BaseModel, key: str) -> Any:
        return model.model_dump()[key] if isinstance(model, BaseModel) else model[key]

    @classmethod
    def _is_verified(cls, model: dict | BaseModel):
        verification = cls._get_from_model(model, "verification")
        return verification is not None and verification["status"] == "verified"

    @classmethod
    def get_optional_contact(
        cls, primary_id: str | None, key: str, raw_contacts: list[dict | BaseModel]
    ) -> ClerkContact | None:
        candidates: list[ClerkContact] = []
        for raw_contact in raw_contacts:
            contact = ClerkContact(
                value=cls._get_from_model(raw_contact, key),
                is_primary=primary_id is not None and cls._get_from_model(raw_contact, "id") == primary_id,
                is_verified=cls._is_verified(raw_contact),
            )
            if contact.is_primary:
                return contact
            candidates.append(contact)
        for contact in candidates:
            if contact.is_verified:
                return contact
        return candidates[0] if len(candidates) > 0 else None

    @classmethod
    def get_mandatory_contact(
        cls, primary_id: str | None, key: str, raw_contacts: list[dict | BaseModel]
    ) -> ClerkContact:
        contact = cls.get_optional_contact(primary_id, key, raw_contacts)
        if contact is None:
            raise Exception(f"No matching contact for key '{key}' and primary id '{primary_id}' found")
        return contact


class InvalidTokenError(Exception):
    pass


class ClerkAPI:
    def __init__(
        self,
        api_key: str,
        public_key: str,
        permitted_origins: list[str] | None,
        permitted_origin_regex: str | None = None,
    ):
        self._api_key = api_key
        self._public_key = public_key
        self._permitted_origins = permitted_origins
        self._permitted_origin_regex = permitted_origin_regex
        self._sync_sdk = Clerk(bearer_auth=self._api_key)

    async def get_user_by_clerk_id(self, clerk_id: str) -> ClerkUser | None:
        async with Clerk(bearer_auth=self._api_key) as clerk:
            clerk_sdk_user = await clerk.users.get_async(user_id=clerk_id)
            if clerk_sdk_user is None:
                return None
            return ClerkUser.from_webhook(clerk_sdk_user)

    async def create_user(
        self, email: str, first_name: str, last_name: str, is_email_verified: bool = False, user_id: str | None = None
    ) -> ClerkUser:
        async with Clerk(bearer_auth=self._api_key) as clerk:
            request = {
                "email_address": [email],
                "first_name": first_name,
                "last_name": last_name,
                "skip_password_requirement": True,
            }
            if user_id is not None:
                request["private_metadata"] = {"user_id": user_id}
            user = await clerk.users.create_async(request=request)
            if not is_email_verified:
                await clerk.email_addresses.update_async(email_address_id=user.primary_email_address_id, verified=False)
            return ClerkUser.from_webhook(user)

    async def get_sign_in_token(self, clerk_id: str) -> str:
        async with Clerk(bearer_auth=self._api_key) as clerk:
            token = await clerk.sign_in_tokens.create_async(request={"user_id": clerk_id})
            return token.token

    def validate_request(self, request: Request, silent: bool = False) -> dict[str, Any]:
        request_state: RequestState = self._sync_sdk.authenticate_request(
            request,
            AuthenticateRequestOptions(
                jwt_key=self._public_key, secret_key=self._api_key, authorized_parties=self._permitted_origins
            ),
        )
        if request_state.status == AuthStatus.SIGNED_OUT:
            if not silent:
                logger.warning("Invalid token - user is not signed in.")
                logger.warning(f"Reason: {request_state.reason}")
            raise InvalidTokenError(f"Invalid token - user is not signed in. {request_state.reason}")

        if request_state.payload is None:
            if not silent:
                logger.warning("Invalid token - payload is None.")
                logger.warning(f"Reason: {request_state.reason}")
            raise InvalidTokenError("Invalid token - payload is None.")

        if self._permitted_origin_regex is not None:
            azp = request_state.payload.get("azp")
            if azp is None or not re.match(self._permitted_origin_regex, azp):
                if not silent:
                    logger.warning("Invalid origin - doesn't match regex on jwt's azp.")
                raise InvalidTokenError("Invalid origin")

        if request_state.status == AuthStatus.SIGNED_IN:
            return request_state.payload
        if not silent:
            logger.warning(f"Invalid token: {request_state.reason}")
        raise InvalidTokenError("Invalid token")
