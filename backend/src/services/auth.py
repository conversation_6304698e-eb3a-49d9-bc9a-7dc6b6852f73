import datetime
import logging
from os import environ
from typing import Annotated

import jwt
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sentry_sdk import capture_exception as sentry_capture_exception

from src.db_models.user import User
from src.dependencies import get_user_service, get_clerk_api
from src.integrations.clerk import Clerk<PERSON><PERSON>, InvalidTokenError
from src.services.users import UserService

security = HTTPBearer()

logger = logging.getLogger("uvicorn")

AI_ENGINE_API_KEY = environ["AI_ENGINE_API_KEY"]
ADMIN_API_KEY = environ["ADMIN_API_KEY"]
GUEST_USER_TOKEN_SIGNING_KEY = environ["GUEST_USER_TOKEN_SIGNING_KEY"]
GUEST_USER_TOKEN_EXP_DAYS = int(environ.get("GUEST_USER_TOKEN_EXP_DAYS", 14))
BASE_URL = environ["BASE_URL"]


def validate_clerk_jwt_token_and_get_users_clerk_id(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    request: Request,
    clerk_api: ClerkAPI = Depends(get_clerk_api),
) -> str:
    try:
        token_payload = clerk_api.validate_request(request)
        return token_payload["sub"]
    except InvalidTokenError as e:
        logger.warning(f"Error decoding JWT token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def validate_clerk_jwt_token(token: str = Depends(validate_clerk_jwt_token_and_get_users_clerk_id)) -> None:
    return


async def get_current_user(
    user_clerk_id: str = Depends(validate_clerk_jwt_token_and_get_users_clerk_id),
    user_service: UserService = Depends(get_user_service),
) -> User:
    return await user_service.get_or_create_user_by_clerk_id(user_clerk_id)


async def get_current_user_or_guest(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    request: Request,
    clerk_api: ClerkAPI = Depends(get_clerk_api),
    user_service: UserService = Depends(get_user_service),
) -> User:
    if credentials.scheme != "Bearer":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication scheme",
            headers={"WWW-Authenticate": "Bearer"},
        )
    # Attempt to validate Clerk JWT token first
    try:
        token_payload = clerk_api.validate_request(request, silent=True)
        return await user_service.get_or_create_user_by_clerk_id(token_payload["sub"])
    except InvalidTokenError as e:
        logger.info(f"Couldn't decode Clerk JWT token: {e}. Attempting to authenticate as a guest user.")

    # If Clerk JWT validation fails, try to authenticate as a guest user
    return await get_guest_user(credentials, user_service)


async def get_guest_user(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    user_service: UserService = Depends(get_user_service),
) -> User:
    try:
        token_payload = jwt.decode(
            credentials.credentials,
            GUEST_USER_TOKEN_SIGNING_KEY,
            algorithms=["HS256"],
            verify=True,
            require=["sub", "exp", "iat", "nbf", "iss", "azp"],
            issuer=BASE_URL,
        )
        if token_payload["azp"] != BASE_URL:
            raise jwt.InvalidTokenError("Invalid azp in guest user token")
        user_id = int(token_payload["sub"])
        logger.info(f"Successfully verified guest user JWT token. User ID: {user_id}")
        return await user_service.get_user_by_id(user_id)
    except jwt.InvalidTokenError as e:
        sentry_capture_exception(e)
        logger.error(f"Error decoding as guest user JWT token: {e}")
    except ValueError as e:
        sentry_capture_exception(e)
        logger.error(f"Invalid guest user ID in token: {e}")

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )


def validate_ai_engine_token(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
) -> None:
    if credentials.credentials != AI_ENGINE_API_KEY:
        logger.warning("Invalid AI Engine API key provided.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return


def validate_admin_token(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
) -> None:
    if credentials.credentials != ADMIN_API_KEY:
        logger.warning("Invalid Admin API key provided.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return


def issue_guest_user_token(user: User) -> str:
    if not user.isGuest:
        raise ValueError("User must be a guest user to issue a guest user token.")
    now = datetime.datetime.now(tz=datetime.timezone.utc)
    payload = {
        "sub": str(user.id),
        "exp": now + datetime.timedelta(days=GUEST_USER_TOKEN_EXP_DAYS),
        "iat": now,
        "nbf": now,
        "iss": BASE_URL,
        "azp": BASE_URL,
    }
    token = jwt.encode(payload, GUEST_USER_TOKEN_SIGNING_KEY, algorithm="HS256")
    return token
