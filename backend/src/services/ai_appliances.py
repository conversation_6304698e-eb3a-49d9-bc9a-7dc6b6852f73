import logging
from typing import List, Optional

from sqlalchemy import select, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from src.db_models.appliance import Appliance
from src.db_models.property import Property
from src.db_models.relationships import UsersProperties

logger = logging.getLogger("uvicorn")


class ApplianceDoesNotExist(Exception):
    pass


class ApplianceService:
    def __init__(self, db: AsyncSession):
        self._db = db

    async def get_appliance_by_id(self, appliance_id: int, user_id: int) -> Optional[Appliance]:
        return (await self._db.execute(self.get_appliance_by_id_query(appliance_id, user_id))).scalar()

    async def get_appliance_by_type_or_brand_or_model(
        self,
        user_id: int,
        appliance_type: Optional[str] = None,
        brand: Optional[str] = None,
        model: Optional[str] = None,
    ) -> List[Appliance]:
        logger.info(
            f"get_appliance_by_type_or_brand_or_model(): user_id={user_id}, "
            f"type={appliance_type}, brand={brand}, model={model} ..."
        )

        appliances = (
            (
                await self._db.execute(
                    self.get_appliance_by_type_or_brand_or_model_query(user_id, appliance_type, brand, model)
                )
            )
            .scalars()
            .all()
        )

        logger.info(
            f"get_appliance_by_type_or_brand_or_model(): user_id={user_id}, type={appliance_type}, brand={brand}, "
            f"model={model} returned {len(appliances)} appliances"
        )

        return list(appliances)

    @staticmethod
    def get_appliance_by_id_query(appliance_id: int, user_id: int):
        return (
            select(Appliance)
            .join(Property, Appliance.propertyId == Property.id)
            .join(UsersProperties, Property.id == UsersProperties.propertyId)
            .where(Appliance.id == appliance_id, UsersProperties.userId == user_id)
            .options(joinedload(Appliance.property))
        )

    @staticmethod
    def get_appliance_by_type_or_brand_or_model_query(
        user_id: int, appliance_type: Optional[str] = None, brand: Optional[str] = None, model: Optional[str] = None
    ):
        conditions = []

        if appliance_type is not None:
            conditions.append(Appliance.type == appliance_type)

        if brand is not None:
            conditions.append(Appliance.brand == brand)

        if model is not None:
            conditions.append(Appliance.model == model)

        query = (
            select(Appliance)
            .join(Property, Appliance.propertyId == Property.id)
            .join(UsersProperties, Property.id == UsersProperties.propertyId)
            .where(UsersProperties.userId == user_id)
        )

        if conditions:
            query = query.where(or_(*conditions))

        return query
