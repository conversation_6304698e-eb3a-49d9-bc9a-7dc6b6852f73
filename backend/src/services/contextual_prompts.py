import logging

from sqlalchemy import select, delete, and_
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.contextual_prompt import ContextualPrompt, ContextualPromptType
from src.db_models.entity_link import EntityLink
from src.db_models.todo import Todo
from src.schemas import LinkedEntity, LinkedEntityType
from src.services.entity_links import EntityLinksService


class ContextualPromptDoesNotExist(Exception):
    pass


class ContextualPromptService:
    def __init__(
        self,
        db: AsyncSession,
        entity_link_service: EntityLinksService,
    ):
        self.logger = logging.getLogger("uvicorn")
        self._db = db
        self._entity_link_service = entity_link_service

    async def create_contextual_prompts(
        self, prompts: list[str], todo_id: int, prompt_type: ContextualPromptType, user_id: int
    ) -> list[ContextualPrompt]:
        """
        Creates contextual prompts and links them to a todo.

        Args:
            prompts: List of prompt strings to create
            todo_id: ID of the todo to link prompts to
            prompt_type: Type of contextual prompt
            user_id: ID of the user creating the prompts

        Returns:
            List of created ContextualPrompt objects
        """
        self.logger.info(f"create_contextual_prompts(): todo_id={todo_id} prompts={prompts}")
        # Verify the todo exists and belongs to the user
        todo = await self._db.scalar(select(Todo).where(Todo.id == todo_id, Todo.userId == user_id))
        if not todo:
            raise ContextualPromptDoesNotExist(
                f"Todo with id {todo_id} does not exist or does not belong to user {user_id}"
            )

        created_prompts = []

        for prompt_text in prompts:
            # Create the contextual prompt
            contextual_prompt = ContextualPrompt(type=prompt_type, prompt=prompt_text)
            self._db.add(contextual_prompt)
            await self._db.flush()
            await self._db.refresh(contextual_prompt)

            # Link the contextual prompt to the todo
            await self._entity_link_service.link(
                LinkedEntity(id=todo_id, entityType=LinkedEntityType.todos),
                LinkedEntity(id=contextual_prompt.id, entityType=LinkedEntityType.contextual_prompts),
                user_id,
            )

            created_prompts.append(contextual_prompt)

        await self._db.commit()
        return created_prompts

    async def create_contextual_prompts_with_appliance_link(
        self,
        prompts: list[str],
        todo_id: int,
        prompt_type: ContextualPromptType,
        user_id: int,
        appliance_id: int = None,
    ) -> list[ContextualPrompt]:
        """
        Creates contextual prompts and optionally links the todo to an appliance.

        Args:
            prompts: List of prompt strings to create
            todo_id: ID of the todo to link prompts to
            prompt_type: Type of contextual prompt
            user_id: ID of the user creating the prompts
            appliance_id: Optional ID of the appliance to link to the todo

        Returns:
            List of created ContextualPrompt objects
        """
        # Create the contextual prompts first
        created_prompts = await self.create_contextual_prompts(prompts, todo_id, prompt_type, user_id)

        # Link todo to appliance if appliance_id is provided
        if appliance_id:
            try:
                await self._entity_link_service.link(
                    LinkedEntity(id=todo_id, entityType=LinkedEntityType.todos),
                    LinkedEntity(id=appliance_id, entityType=LinkedEntityType.appliances),
                    user_id,
                )
                self.logger.info(f"Linked todo {todo_id} to appliance {appliance_id}")
            except ValueError as e:
                self.logger.error(f"Failed to link todo to appliance: {str(e)}")
            except Exception as e:
                self.logger.exception(f"Unexpected error linking todo to appliance: {str(e)}")

        return created_prompts

    async def delete_contextual_prompts_for_todo(self, todo_id: int, user_id: int) -> int:
        """
        Deletes all contextual prompts linked to a specific todo.

        Args:
            todo_id: ID of the todo whose prompts should be deleted
            user_id: ID of the user performing the deletion

        Returns:
            Number of prompts deleted
        """
        # Verify the todo exists and belongs to the user
        todo = await self._db.scalar(select(Todo).where(Todo.id == todo_id, Todo.userId == user_id))
        if not todo:
            raise ContextualPromptDoesNotExist(
                f"Todo with id {todo_id} does not exist or does not belong to user {user_id}"
            )
        # First, find all contextual prompts linked to this todo
        # Since "contextual_prompts" < "todos" alphabetically, links are always stored as:
        # typeA = "contextual_prompts", typeB = "todos"
        links_query = select(EntityLink).where(
            and_(
                EntityLink.typeA == LinkedEntityType.contextual_prompts.value,
                EntityLink.typeB == LinkedEntityType.todos.value,
                EntityLink.idB == todo_id,
            )
        )

        links = await self._db.scalars(links_query)
        prompt_ids = []

        for link in links:
            # Since typeA is always contextual_prompts, idA is the prompt id
            prompt_ids.append(link.idA)

        if not prompt_ids:
            return 0

        # Delete the links first
        for prompt_id in prompt_ids:
            await self._entity_link_service.unlink(
                LinkedEntity(id=todo_id, entityType=LinkedEntityType.todos),
                LinkedEntity(id=prompt_id, entityType=LinkedEntityType.contextual_prompts),
                user_id,
            )

        # Delete the contextual prompts
        result = await self._db.execute(delete(ContextualPrompt).where(ContextualPrompt.id.in_(prompt_ids)))

        return result.rowcount

    async def get_contextual_prompts_for_todo(self, todo_id: int, user_id: int) -> list[ContextualPrompt]:
        """
        Gets all contextual prompts linked to a specific todo.

        Args:
            todo_id: ID of the todo
            user_id: ID of the user requesting the prompts

        Returns:
            List of ContextualPrompt objects
        """
        # Verify the todo exists and belongs to the user
        todo = await self._db.scalar(select(Todo).where(Todo.id == todo_id, Todo.userId == user_id))
        if not todo:
            raise ContextualPromptDoesNotExist(
                f"Todo with id {todo_id} does not exist or does not belong to user {user_id}"
            )
        # Find all contextual prompts linked to this todo
        # Since "contextual_prompts" < "todos" alphabetically, links are always stored as:
        # typeA = "contextual_prompts", typeB = "todos"
        links_query = select(EntityLink).where(
            and_(
                EntityLink.typeA == LinkedEntityType.contextual_prompts.value,
                EntityLink.typeB == LinkedEntityType.todos.value,
                EntityLink.idB == todo_id,
            )
        )

        links = await self._db.scalars(links_query)
        prompt_ids = []

        for link in links:
            # Since typeA is always contextual_prompts, idA is the prompt id
            prompt_ids.append(link.idA)

        if not prompt_ids:
            return []

        # Get the contextual prompts
        prompts = await self._db.scalars(select(ContextualPrompt).where(ContextualPrompt.id.in_(prompt_ids)))

        return list(prompts)
