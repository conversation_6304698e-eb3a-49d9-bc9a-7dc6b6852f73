import logging

from sqlalchemy.ext.asyncio import AsyncSession

from src.services.chats import ChatService
from src.services.todos import TodoService


logger = logging.getLogger("uvicorn")


class TodosForAbandonedChatsService:
    def __init__(
        self, db: AsyncSession, chat_service: ChatService, todo_service: TodoService, limit: int | None = None
    ):
        self._db = db
        self._chat_service = chat_service
        self._todo_service = todo_service
        self._limit = limit

    async def create_todos_for_abandoned_chats(self) -> None:
        logger.info(f"Creating todos for abandoned chats (limit: {self._limit})")
        abandoned_chats = await self._chat_service.get_abandoned_chats(limit=self._limit)
        logger.info(f"Found {len(abandoned_chats)} abandoned chats")
        for chat in abandoned_chats:
            await self._todo_service.create_todos_for_chat(chat)
            await self._chat_service.mark_as_processed_as_abandoned(chat)

    async def create_todos_for_abandoned_chat_id(self, chat_id) -> None:
        logger.info(f"Creating todos for abandoned chat {chat_id}")
        chat = await self._chat_service.get_chat_by_id(chat_id)
        await self._todo_service.create_todos_for_chat(chat)
        await self._chat_service.mark_as_processed_as_abandoned(chat)
