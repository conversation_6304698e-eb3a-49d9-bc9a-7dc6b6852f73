from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.notification import Notification
from src.db_models.user import User
from src.schemas import NotificationCreate


class NotificationDoesNotExist(Exception):
    pass


class NotificationService:
    def __init__(self, db: AsyncSession):
        self._db = db

    @staticmethod
    def get_notifications_for_user_query(user: User):
        return select(Notification).where(Notification.userId == user.id)

    @staticmethod
    def get_notifications_for_user_by_context_query(user: User, context: str):
        return select(Notification).where(Notification.userId == user.id, Notification.context == context)

    async def __get_notification_by_user(self, notification_id: int, user: User) -> Notification:
        return await self._db.scalar(
            select(Notification).where(Notification.userId == user.id, Notification.id == notification_id)
        )

    async def create_notification(self, notification_data: NotificationCreate, user_id: int) -> Notification:
        notification = Notification(
            context=notification_data.context, userId=user_id, payload=notification_data.payload.model_dump()
        )
        self._db.add(notification)
        await self._db.flush()
        await self._db.refresh(notification)
        return notification

    async def delete_all_notifications(self, user: User):
        await self._db.execute(delete(Notification).where(Notification.userId == user.id))
        await self._db.flush()

    async def delete_notifications_by_context(self, context: str, user: User):
        await self._db.execute(
            delete(Notification).where(Notification.userId == user.id, Notification.context == context)
        )
        await self._db.flush()

    async def delete_notification_by_id(self, notification_id: int, user: User):
        notification = await self._db.scalar(
            select(Notification).where(Notification.userId == user.id, Notification.id == notification_id)
        )
        if not notification:
            raise NotificationDoesNotExist("The notification does not exist.")

        await self._db.delete(notification)
        await self._db.flush()
