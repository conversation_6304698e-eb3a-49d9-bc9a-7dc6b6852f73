import datetime
import logging

from sentry_sdk import capture_exception as sentry_capture_exception
from sqlalchemy import select, update
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.db_models.user_type import UserType
from src.integrations.clerk import <PERSON><PERSON><PERSON>, ClerkUser
from src.integrations.sendgrid import SendgridService, UserView
from src.schemas import B2BDemoRequest, ConvertGuestUserToRegisteredRequest
from src.services.hubspot_sync import HubSpotSyncService
from src.services.properties import PropertyService

logger = logging.getLogger("uvicorn")


class EmailAlreadyExistsError(Exception):
    pass


class UserService:
    def __init__(
        self,
        db: AsyncSession,
        clerk_api: ClerkAP<PERSON>,
        sendgrid_service: SendgridService,
        hubspot_sync_service: HubSpotSyncService,
        property_service: PropertyService,
    ):
        self._db = db
        self._clerk_api = clerk_api
        self._sendgrid_service = sendgrid_service
        self._hubspot_sync_service = hubspot_sync_service
        self._property_service = property_service

    async def get_or_create_user_by_clerk_id(self, clerk_id: str) -> User:
        user = await self.get_user_by_clerk_id(clerk_id)
        if not user:
            user = await self.__create_user_from_clerk_id(clerk_id)
        if not user:
            raise RuntimeError("User not found in Clerk and could not be created. Maybe Clerk id has changed.")
        return user

    async def get_user_by_clerk_id(self, clerk_id: str) -> User | None:
        return await self._db.scalar(select(User).where(User.clerkId == clerk_id, User.deletedAt.is_(None)))

    async def get_user_by_id(self, user_id: int) -> User:
        user = await self._db.scalar(select(User).where(User.id == user_id, User.deletedAt.is_(None)))
        if not user:
            raise ValueError(f"User with id {user_id} not found")
        return user

    async def __create_user_from_clerk_id(self, clerk_id: str) -> User | None:
        clerk_user = await self._clerk_api.get_user_by_clerk_id(clerk_id)
        if not clerk_user:
            raise RuntimeError("User not found in Clerk")
        return await self.__create_user_from_clerk_user(clerk_user)

    async def __create_user_from_clerk_user(self, clerk_user: ClerkUser) -> User | None:
        user_type = await self.__get_or_create_default_user_type()

        user = User(
            clerkId=clerk_user.clerk_id,
            email=clerk_user.primary_email,
            isEmailVerified=clerk_user.is_primary_email_verified,
            firstName=clerk_user.first_name,
            lastName=clerk_user.last_name,
            phoneNumber=clerk_user.primary_phone_number,
            isPhoneNumberVerified=clerk_user.is_primary_phone_number_verified,
            types=[user_type],
        )
        self._db.add(user)

        try:
            await self._db.commit()
        except IntegrityError:
            logger.info(f"User with email {clerk_user.primary_email} already exists.")
            await self._db.rollback()
            return None

        logger.info(f"User {user.id} created.")
        await self._property_service.get_first_or_create_empty_property_for_user(user)
        await self._hubspot_sync_service.sync_user(user.id)
        return user

    async def __send_welcome_email(self, user: User) -> None:
        try:
            await self._sendgrid_service.send_welcome_email(user.email, user.firstName)
        except Exception as e:
            logger.error(f"Failed to send welcome email to {user.email}: {e}")
            raise
        await self._db.execute(update(User).where(User.id == user.id).values(isWelcomeEmailSent=True))
        await self._db.commit()

    async def upsert_user_from_clerk_user(self, clerk_user: ClerkUser) -> User | None:
        if clerk_user.user_id:
            user = await self.get_user_by_id(clerk_user.user_id)
        else:
            user = await self.get_user_by_clerk_id(clerk_user.clerk_id)
        if user is None:
            logger.warning(f"User with clerk_id {clerk_user.clerk_id} not found.")
            user = await self.__create_user_from_clerk_user(clerk_user)
        else:
            user.email = clerk_user.primary_email
            user.isEmailVerified = clerk_user.is_primary_email_verified
            user.firstName = clerk_user.first_name
            user.lastName = clerk_user.last_name
            user.phoneNumber = clerk_user.primary_phone_number
            user.isPhoneNumberVerified = clerk_user.is_primary_phone_number_verified
            await self._db.flush()
            await self._hubspot_sync_service.sync_user(user.id)
        return user

    async def delete_user_by_clerk_id(self, clerk_id: str) -> None:
        user = await self._db.scalar(select(User).where(User.clerkId == clerk_id, User.deletedAt.is_(None)))
        if user is not None:
            await self.delete_user(user)

    async def __get_or_create_default_user_type(self) -> UserType:
        user_type = (await self._db.execute(select(UserType).where(UserType.name == "user"))).scalar()
        if user_type is None:
            user_type = UserType(name="user")
            self._db.add(user_type)
            await self._db.flush()
        return user_type

    async def delete_user(self, user: User) -> None:
        user.deletedAt = datetime.datetime.now()
        await self._db.flush()

    async def request_b2b_demo(self, user: User, b2b_demo_request: B2BDemoRequest) -> None:
        await self._hubspot_sync_service.sync_b2b_demo_request(b2b_demo_request)
        user_view = UserView.model_validate(user)
        await self._sendgrid_service.send_b2b_demo_request_emails(
            user=user_view,
            b2b_demo_request=b2b_demo_request,
        )

    async def create_guest_user(self) -> User:
        guest_user = User()
        self._db.add(guest_user)
        await self._db.commit()
        logger.info(f"Guest user {guest_user.id} created.")
        await self._property_service.get_first_or_create_empty_property_for_user(guest_user)
        return guest_user

    async def convert_guest_user_to_authenticated(
        self, user: User, user_details: ConvertGuestUserToRegisteredRequest
    ) -> User:
        if user.clerkId:
            raise ValueError("Guest user already has a Clerk ID.")

        user.email = str(user_details.email)
        user.firstName = user_details.firstName
        user.lastName = user_details.lastName
        try:
            await self._db.commit()
        except IntegrityError as e:
            sentry_capture_exception(e)
            logger.error(f"Failed to convert guest user {user.id} to authenticated user: {e}")
            raise EmailAlreadyExistsError("User with this email already exists.")

        clerk_user = await self._clerk_api.create_user(
            str(user_details.email),
            user_details.firstName,
            user_details.lastName,
            is_email_verified=False,
            user_id=str(user.id),
        )
        user.clerkId = clerk_user.clerk_id
        await self._db.commit()

        return user

    async def get_clerk_sign_in_token_for_user(self, user: User) -> str:
        if not user.clerkId:
            raise ValueError("User does not have a Clerk ID.")

        return await self._clerk_api.get_sign_in_token(user.clerkId)
