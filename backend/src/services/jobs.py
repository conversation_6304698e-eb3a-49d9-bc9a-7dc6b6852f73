import base64
import logging

from sentry_sdk import capture_exception as sentry_capture_exception
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.job import Job
from src.db_models.user import User
from src.integrations.sendgrid import SendgridService, UserView, JobView, PropertyView, AttachmentView
from src.services.documents import DocumentService
from src.services.hubspot_sync import HubSpotSyncService
from src.services.properties import PropertyService

logger = logging.getLogger("uvicorn")


class JobDoesNotExist(Exception):
    pass


class OperationNotAllowed(Exception):
    pass


class JobService:
    def __init__(
        self,
        db: AsyncSession,
        property_service: PropertyService,
        document_service: DocumentService,
        sendgrid_service: SendgridService,
        hubspot_sync_service: HubSpotSyncService,
    ):
        self._db = db
        self._property_service = property_service
        self._document_service = document_service
        self._sendgrid_service = sendgrid_service
        self._hubspot_sync_service = hubspot_sync_service

    async def accept_job_by_user(self, job_id: int, user: User):
        if not user.canUserAcceptJobs:
            raise OperationNotAllowed("User cannot accept jobs.")

        job = await self.get_job_by_id(job_id, user)
        if not job:
            raise JobDoesNotExist(f"Job with id {job_id} does not exist.")

        if job.status == "created":
            job.status = "user_accepted"
            await self._db.commit()
            await self._on_job_accepted_by_user(job, user)
        else:
            raise OperationNotAllowed("This job is not available for accepting.")

    async def get_job_by_id(self, job_id: int, user: User) -> Job | None:
        return (await self._db.execute(self.get_job_by_id_query(job_id, user))).scalar()

    async def _on_job_accepted_by_user(self, job: Job, user: User):
        try:
            await self._hubspot_sync_service.sync_job(job.id)
            user_view = UserView.model_validate(user)
            job_view = JobView(
                id=job.id,
                reference=job.reference,
                headline=job.headline,
                subtitle=job.subTitle,
                details=job.details,
                date=job.urgency,
                timeOfDay=job.availability,
            )
            a_property = await self._property_service.get_property_by_id_for_user(job.propertyId, user)
            property_view = PropertyView(
                id=a_property.id,
                address=f"""{a_property.address.streetLine1}\n{a_property.address.streetLine2}\n
                    {a_property.address.streetLine3}\n{a_property.address.townOrCity}\n
                    {a_property.address.postcode}\n""",
                parkingInstructions=a_property.address.parkingInstructions,
                houseAccess=a_property.address.houseAccess,
            )
            documents = (
                (await self._db.execute(self._document_service.list_documents_for_chat_query(job.chatId)))
                .scalars()
                .all()
            )

            attachments = [
                AttachmentView(
                    fileContent=base64.b64encode(
                        b"".join(
                            [
                                chunk
                                async for chunk in self._document_service.get_document_content_from_s3(
                                    document.s3Bucket, document.s3Key
                                )
                            ]
                        )
                    ).decode(),
                    fileName=document.originalFileName,
                    fileType=document.browserMimeType,
                )
                for document in documents
            ]
            await self._sendgrid_service.send_user_job_acknowledgement_email(
                user.email, user_view, job_view, attachments
            )
            await self._sendgrid_service.send_ops_job_acknowledgement_email(
                user_view, job_view, property_view, job.chatId, attachments
            )
        except Exception as e:
            logger.error(f"Error while sending job acceptance emails: {e}")
            sentry_capture_exception(e)

    @staticmethod
    def get_job_by_id_query(job_id: int, user: User):
        return select(Job).where(Job.id == job_id, Job.userId == user.id)

    @staticmethod
    def list_jobs_by_user_query(user: User):
        return select(Job).where(Job.userId == user.id)
