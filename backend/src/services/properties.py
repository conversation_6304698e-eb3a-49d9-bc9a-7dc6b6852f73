from decimal import Decimal

from sqlalchemy import select, Select, update
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.address import Address
from src.db_models.chat import Chat
from src.db_models.document import Document
from src.db_models.job import Job
from src.db_models.postcode_location import PostcodeLocation
from src.db_models.project import Project
from src.db_models.property import Property
from src.db_models.relationships import UserPropertyRelationType, UsersProperties
from src.db_models.source_link import SourceType
from src.db_models.user import User
from src.integrations.idealpostcodes import IdealPostcodesAPI
from src.integrations.landregistry import LandRegistryAPI
from src.schemas import (
    PropertyCreate,
    ManualAddressCreate,
    PropertyUpdate,
    PropertyAiUpdate,
    PropertyInfo,
    PropertyDetailsData,
)
from src.services.coordinates import CoordinateTransformer
from src.services.source_links import SourceLinksService


class UserAlreadyHasProperty(Exception):
    pass


class PropertyDoesNotExist(Exception):
    pass


class PropertyService:
    def __init__(
        self,
        db: AsyncSession,
        coordinate_transformer: CoordinateTransformer,
        idealpostcodes_api: IdealPostcodesAPI,
        land_registry_api: LandRegistryAPI,
        source_link_service: SourceLinksService,
    ):
        self._db = db
        self._coordinate_transformer = coordinate_transformer
        self._idealpostcodes_api = idealpostcodes_api
        self._land_registry_api = land_registry_api
        self._source_link_service = source_link_service

    async def create_property_for_user(
        self,
        user: User,
        property_create: PropertyCreate | None = None,
    ) -> PropertyInfo:
        if await self.has_user_property(user):
            raise UserAlreadyHasProperty("User already has a property.")
        property_created = (
            await self.__create_property(property_create, user.id)
            if property_create
            else await self.__create_empty_property()
        )
        await self.__create_user_property_relation(
            user, property_created.id, property_create.userPropertyRelationshipType if property_create else None
        )
        await self.__connect_existing_users_entities_to_property(property_created, user)
        await self._db.flush()
        await self._db.refresh(property_created)
        return self.__to_property_info(property_created)

    async def __connect_existing_users_entities_to_property(self, property_created: Property, user: User):
        """Connect existing chats, documents, projects and jobs to the property"""
        await self._db.execute(
            update(Chat)
            .where(Chat.userId == user.id, Chat.propertyId == None)  # noqa: E711
            .values(propertyId=property_created.id)
        )
        await self._db.execute(
            update(Document)
            .where(Document.userId == user.id, Document.propertyId == None)  # noqa: E711
            .values(propertyId=property_created.id)
        )
        await self._db.execute(
            update(Project)
            .where(Project.userId == user.id, Project.propertyId == None)  # noqa: E711
            .values(propertyId=property_created.id)
        )
        await self._db.execute(
            update(Job)
            .where(Job.userId == user.id, Job.propertyId == None)  # noqa: E711
            .values(propertyId=property_created.id)
        )

    async def __create_property(self, property_create: PropertyCreate, user_id: int):
        if property_create.idealPostcodesAddressId:
            address = await self.__create_address_from_idealpostcodes(property_create.idealPostcodesAddressId)
        elif property_create.manualAddress:
            address = await self.__create_address_from_manual(property_create.manualAddress)
        else:
            raise ValueError("Either manualAddress or idealPostcodesAddressId must be provided")

        property_created = Property(
            address=address,
            type=property_create.type,
            tenureType=property_create.tenureType,
        )
        self._db.add(property_created)
        await self._db.flush()
        await self._db.refresh(property_created)
        await self._source_link_service.link_creation_from_source(
            destination=property_created,
            source_type=SourceType.userInput,
            source_id=user_id,
            create_command=property_create.model_dump(exclude_unset=True),
        )
        return property_created

    async def __create_empty_property(self):
        property_created = Property()
        self._db.add(property_created)
        await self._db.flush()
        await self._db.refresh(property_created)
        return property_created

    async def __create_address_from_manual(self, manual_address: ManualAddressCreate) -> Address:
        latitude, longitude = None, None
        postcode_location = (
            await self._db.execute(
                select(PostcodeLocation).filter(PostcodeLocation.postcode == manual_address.postcode)
            )
        ).scalar()
        if postcode_location:
            latitude, longitude = postcode_location.latitude, postcode_location.longitude

        address = Address(
            streetLine1=manual_address.streetLine1,
            streetLine2=manual_address.streetLine2,
            townOrCity=manual_address.townOrCity,
            postcode=manual_address.postcode,
            country="UK",
            latitude=latitude,
            longitude=longitude,
        )
        self._db.add(address)
        return address

    async def __create_address_from_idealpostcodes(self, idealpostcodes_address_id: str) -> Address:
        resolved_address, raw_response = await self._idealpostcodes_api.resolve_address(idealpostcodes_address_id)
        address = Address(
            streetLine1=resolved_address.line_1,
            streetLine2=resolved_address.line_2,
            streetLine3=resolved_address.line_3,
            townOrCity=resolved_address.post_town,
            postcode=resolved_address.postcode,
            country="UK",
            latitude=Decimal(resolved_address.latitude) if resolved_address.latitude else None,
            longitude=Decimal(resolved_address.longitude) if resolved_address.longitude else None,
            uprn=int(resolved_address.uprn) if resolved_address.uprn else None,  # empty string if not available
            udprn=resolved_address.udprn,
            umprn=resolved_address.umprn,
            idealPostcodesOutput=raw_response,
        )
        self._db.add(address)
        return address

    async def __create_user_property_relation(
        self, user: User, property_id: int, relation_type: UserPropertyRelationType
    ):
        relation = UsersProperties(user=user, propertyId=property_id, relationType=relation_type)
        self._db.add(relation)
        return relation

    async def has_user_property(self, user) -> bool:
        return (
            await self._db.execute(select(UsersProperties).where(UsersProperties.userId == user.id))
        ).scalar() is not None

    async def get_first_or_create_empty_property_for_user(self, user: User) -> int:
        prop = await self.get_first_property_for_user(user)
        if not prop:
            prop = await self.create_property_for_user(user)

        return prop.id

    async def get_first_or_create_empty_property_for_user_id(self, user_id: int) -> int:
        user = await self._db.scalar(select(User).where(User.id == user_id))
        if not user:
            raise ValueError("User does not exist.")
        return await self.get_first_or_create_empty_property_for_user(user)

    @staticmethod
    def __to_property_info(property: Property) -> PropertyInfo:
        property_dict = property.to_dict()
        property_dict["userPropertyRelationshipType"] = (
            property.usersProperties[0].relationType if property.usersProperties else None
        )
        property_dict["sourcedDocuments"] = property.sourcedDocuments
        property_dict["address"] = property.address
        return PropertyInfo.model_validate(property_dict)

    async def get_properties_for_user(self, user: User) -> list[PropertyInfo]:
        result = (await self._db.scalars(self.get_properties_for_user_query(user))).all()
        return [self.__to_property_info(p) for p in result]

    @staticmethod
    def get_properties_for_user_query(user) -> Select[tuple[Property]]:
        return select(Property).join(UsersProperties).where(UsersProperties.userId == user.id)

    async def get_first_property_for_user(self, user: User) -> PropertyInfo | None:
        model = (await self._db.execute(self.get_properties_for_user_query(user))).scalars().first()
        return self.__to_property_info(model) if model else None

    async def get_property_by_id_for_user(self, property_id: int, user: User) -> Property | None:
        query = self.get_properties_for_user_query(user).where(Property.id == property_id)
        return (await self._db.execute(query)).scalar()

    async def does_property_belong_to_user(self, property_id: int, user: User) -> bool:
        query = self.get_properties_for_user_query(user).where(Property.id == property_id)
        return (await self._db.execute(query)).scalar() is not None

    async def delete_property_by_id_for_user(self, property_id: int, user: User):
        property_to_delete = await self.get_property_by_id_for_user(property_id, user)
        if property_to_delete:
            await self._db.delete(property_to_delete)

    async def update_property_by_id_for_user(
        self, property_id: int, property_update: PropertyUpdate, user: User
    ) -> PropertyInfo:
        property_to_update: Property = await self.get_property_by_id_for_user(property_id, user)
        if not property_to_update:
            raise PropertyDoesNotExist(f"Property id {property_id} does not exist.")
        property_to_update = await self.__apply_property_updates(
            property_to_update=property_to_update,
            command=property_update,
            source_type=SourceType.userInput,
            source_id=user.id,
        )
        return self.__to_property_info(property_to_update)

    async def update_property_by_id_for_ai(self, property_id: int, property_update: PropertyAiUpdate) -> PropertyInfo:
        property_to_update: Property = await self._db.scalar(select(Property).where(Property.id == property_id))
        if not property_to_update:
            raise PropertyDoesNotExist(f"Property id {property_id} does not exist.")
        property_details = property_update.propertyDetails
        property_to_update = await self.__apply_property_updates(
            property_to_update=property_to_update,
            command=property_details,
            source_type=property_update.srcType,
            source_id=property_update.srcId,
        )
        return self.__to_property_info(property_to_update)

    async def __apply_property_updates(
        self,
        property_to_update: Property,
        command: PropertyUpdate | PropertyDetailsData,
        source_type: SourceType,
        source_id: int,
    ) -> Property:
        update_data = await self._source_link_service.process_command_from_source(
            destination=property_to_update,
            source_type=source_type,
            source_id=source_id,
            command=command.model_dump(exclude_unset=True),
        )
        for key, value in update_data.items():
            if key == "userPropertyRelationshipType":
                property_to_update.usersProperties[0].relationType = value
            elif key == "idealPostcodesAddressId":
                if value:
                    property_to_update.address = await self.__create_address_from_idealpostcodes(value)
            elif key == "manualAddress":
                if value:
                    property_to_update.address = await self.__create_address_from_manual(ManualAddressCreate(**value))
            elif key == "address":
                if value and property_to_update.address:
                    for address_key, address_value in value.items():
                        setattr(property_to_update.address, address_key, address_value)
            else:
                setattr(property_to_update, key, value)

        await self._db.flush()
        await self._db.refresh(property_to_update)

        return property_to_update
