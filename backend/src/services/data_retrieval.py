import logging
from typing import List, Dict, Any, Optional

from src.ai_dao.QdrantDAO import QdrantDAO
from src.services.ai_jobs import JobService
from src.services.ai_appliances import ApplianceService
from src.services.ai_property import PropertyService


class DataRetrievalService:
    """Service for retrieving various types of data used by agents"""

    def __init__(
        self,
        job_service: JobService,
        appliance_service: ApplianceService,
        property_service: PropertyService,
        qdrant_dao: QdrantDAO,
    ):
        self.logger = logging.getLogger("uvicorn")
        self.job_service = job_service
        self.appliance_service = appliance_service
        self.property_service = property_service
        self.qdrant_dao = qdrant_dao

    async def get_appliances_data(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all user appliances"""
        try:
            # Always return all appliances as requested
            appliances = await self.appliance_service.get_appliance_by_type_or_brand_or_model(
                user_id=user_id, appliance_type=None, brand=None, model=None
            )
        except AttributeError as e:
            self.logger.error(f"Appliance service not properly initialized: {str(e)}")
            return []
        except ConnectionError as e:
            self.logger.error(f"Database connection error while fetching appliances: {str(e)}")
            return []
        except Exception as e:
            self.logger.exception(f"Unexpected error getting appliances data: {str(e)}")
            return []

        # This list comprehension is safe and won't raise exceptions
        return [
            {
                "id": app.id,
                "type": app.type,
                "brand": app.brand,
                "model": app.model,
                "serialNumber": app.serialNumber,
                "warranty": app.warranty,
                "dateOfPurchase": app.dateOfPurchase and app.dateOfPurchase.isoformat(),
                "otherDetails": app.otherDetails,
            }
            for app in appliances
        ]

    async def get_jobs_data(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all previous jobs for user"""
        try:
            jobs = await self.job_service.list_jobs_by_user_id(user_id)
        except AttributeError as e:
            self.logger.error(f"Job service not properly initialized: {str(e)}")
            return []
        except ConnectionError as e:
            self.logger.error(f"Database connection error while fetching jobs: {str(e)}")
            return []
        except Exception as e:
            self.logger.exception(f"Unexpected error getting jobs data: {str(e)}")
            return []

        # This list comprehension is safe and won't raise exceptions
        return [
            {
                "id": job.id,
                "headline": job.headline,
                "subTitle": job.subTitle,
                "details": job.details,
                "urgency": job.urgency,
                "status": job.status,
                "timestamp": job.timestamp and job.timestamp.isoformat(),
            }
            for job in jobs
        ]

    async def get_property_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's primary property info and their relationship"""
        property_obj = None
        relationship_obj = None

        try:
            # 1. Get the primary property
            property_obj = await self.property_service.get_user_primary_property(user_id)
        except AttributeError as e:
            self.logger.error(f"Property service not properly initialized: {str(e)}")
            return None
        except ConnectionError as e:
            self.logger.error(f"Database connection error while fetching property: {str(e)}")
            return None
        except Exception as e:
            self.logger.exception(f"Unexpected error getting primary property: {str(e)}")
            return None

        if not property_obj:
            self.logger.info(f"No primary property found for user_id={user_id}")
            return None

        try:
            # 2. Get the relationship object for this user and property
            relationship_obj = await self.property_service.get_user_property_relationship(user_id, property_obj.id)
        except ConnectionError as e:
            self.logger.error(f"Database connection error while fetching relationship: {str(e)}")
            # Continue without relationship data
        except Exception as e:
            self.logger.exception(f"Unexpected error getting property relationship: {str(e)}")
            # Continue without relationship data

        # 3. Extract the relation type value (safe operation)
        relation_type_value = relationship_obj and relationship_obj.relationType and relationship_obj.relationType.value

        # 4. Return the property details including the relation type (safe operations)
        return {
            "id": property_obj.id,
            "type": property_obj.type and property_obj.type.value,
            "tenureType": property_obj.tenureType and property_obj.tenureType.value,
            "numberOfBedrooms": property_obj.numberOfBedrooms,
            "numberOfBathrooms": property_obj.numberOfBathrooms,
            "numberOfFloors": property_obj.numberOfFloors,
            "sizeInSqft": property_obj.sizeInSqft,
            "condition": property_obj.condition and property_obj.condition.value,
            "epcRating": property_obj.epcRating and property_obj.epcRating.value,
            # Include the user's relationship type to this property
            "userRelationType": relation_type_value,
            "address": (
                {
                    "street": property_obj.address.streetLine1,
                    "city": property_obj.address.townOrCity,
                    "postcode": property_obj.address.postcode,
                }
                if property_obj.address
                else None
            ),
        }

    async def get_documents_data(self, user_id: int, query: str) -> List[Dict[str, Any]]:
        """Search relevant documents using QdrantDAO"""
        if not query.strip():
            return []

        search_results = []
        try:
            search_results = await self.qdrant_dao.query_file_chunk(str(user_id), query, limit=5)
        except AttributeError as e:
            self.logger.error(f"QdrantDAO not properly initialized: {str(e)}")
            return []
        except ConnectionError as e:
            self.logger.error(f"Qdrant connection error while searching documents: {str(e)}")
            return []
        except ValueError as e:
            self.logger.error(f"Invalid query parameters for document search: {str(e)}")
            return []
        except Exception as e:
            self.logger.exception(f"Unexpected error searching documents: {str(e)}")
            return []

        # Logging is safe and won't raise exceptions
        for result in search_results:
            self.logger.info(f"get_documents_data(): fileKey={result.fileKey}")

        # This list comprehension is safe and won't raise exceptions
        return [
            {
                "documentId": result.documentId,
                "content": result.content,
                "metadata": result.metadata,
                "score": result.score,
                "fileKey": result.fileKey,
            }
            for result in search_results
        ]

    async def get_chat_history_data(self, user_id: int, query: str) -> List[Dict[str, Any]]:
        """Search previous chat history using QdrantDAO"""
        if not query.strip():
            return []

        search_results = []
        try:
            search_results = await self.qdrant_dao.search_chat_history(query, str(user_id), limit=3)
        except AttributeError as e:
            self.logger.error(f"QdrantDAO not properly initialized: {str(e)}")
            return []
        except ConnectionError as e:
            self.logger.error(f"Qdrant connection error while searching chat history: {str(e)}")
            return []
        except ValueError as e:
            self.logger.error(f"Invalid query parameters for chat history search: {str(e)}")
            return []
        except Exception as e:
            self.logger.exception(f"Unexpected error searching chat history: {str(e)}")
            return []

        # Logging is safe and won't raise exceptions
        for result in search_results:
            self.logger.info(f"get_chat_history_data(): chatSummary={result.chatSummary}")

        # This list comprehension is safe and won't raise exceptions
        return [
            {
                "chatId": result.chatId,
                "chatSummary": result.chatSummary,
                "score": result.score,
                "conversationHistory": result.conversationHistory,
            }
            for result in search_results
        ]
