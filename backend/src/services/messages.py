import json
import logging
import traceback
from typing import As<PERSON><PERSON>enerator, <PERSON><PERSON>

from sentry_sdk import capture_exception as sentry_capture_exception
from sqlalchemy import select, update, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import flag_modified

from src.ai_schemas import JobSummary
from src.db_models.chat import Chat
from src.db_models.document import Document
from src.db_models.job import Job
from src.db_models.message import Message
from src.db_models.project import Project
from src.db_models.user import User
from src.integrations.aiengine import AbstractAIEngineAPI, UserMessage, AttachmentDetails
from src.schemas import (
    SendMessageRequest,
    SendMessageResponse,
    ResponseMessage,
    ResponseMessageMetadata,
    StreamSendMessageResponse,
)
from src.services.properties import PropertyService

logger = logging.getLogger("uvicorn")


class MessageService:
    def __init__(self, db: AsyncSession, property_service: PropertyService, ai_engine_api: AbstractAIEngineAPI):
        self._db = db
        self._property_service = property_service
        self._ai_engine_api = ai_engine_api

    async def send_user_message_and_get_system_response_stream(
        self, message_request: SendMessageRequest, user: User
    ) -> AsyncGenerator[str, None]:
        logger.info(f"Processing streaming message for user {user.id}, chat {message_request.chatId}")
        chat, db_user_message, attachments_details = await self._prepare_message_and_chat(message_request, user)

        ai_stream = self._ai_engine_api.generate_response_stream(
            UserMessage(
                userId=user.id,
                chatId=chat.id,
                messageId=db_user_message.id,
                message=message_request.message,
                attachments=attachments_details,
            )
        )

        db_ai_message = Message(
            chat=chat,
            content="",
            type="text",
            senderType="system",
            additionalData=None,
        )
        self._db.add(db_ai_message)
        await self._db.flush()
        yield f"data: {json.dumps({'type': 'stream_send_message_response', 'data': StreamSendMessageResponse(
            chatId=chat.id,
            userMessageId=db_user_message.id,
            systemMessageId=db_ai_message.id,
        ).model_dump()})}\n"

        accumulated_content = ""
        #

        try:
            async for sse_event in ai_stream:
                event_type = None
                event_data_str = None
                lines = sse_event.strip().split("\n")
                for line in lines:
                    line = line.strip()
                    if line == "":
                        continue
                    match line.split(":", 1):
                        case ["event", event_type]:
                            event_type = event_type.strip()
                            yield f"{line}\n"
                        case ["data", event_data_str]:
                            try:
                                event_data_str_stripped = event_data_str.strip()
                                if event_data_str_stripped and event_data_str_stripped != "Stream finished":
                                    event_data = json.loads(event_data_str_stripped)
                                    match event_data.get("type"):
                                        case "content":
                                            content_chunk = event_data.get("data", "")
                                            if isinstance(content_chunk, str):
                                                accumulated_content += content_chunk
                                        case "final_data":
                                            final_additional_data = event_data.get("data", {})
                                            logger.info(f"Received final_data for chat {chat.id}")

                                            db_ai_message.content = accumulated_content
                                            db_ai_message.type = final_additional_data.get("responseType", "text")
                                            db_ai_message.additionalData = final_additional_data or None
                                            await self._db.flush()

                                            received_job_summary_dict = final_additional_data.get("jobSummary")
                                            if received_job_summary_dict:
                                                try:
                                                    job_summary = JobSummary(**received_job_summary_dict)
                                                    job_id = await self._handle_job_summary(job_summary, chat, user)
                                                    db_ai_message.additionalData["jobSummary"]["jobId"] = job_id
                                                    event_data["data"]["jobSummary"]["jobId"] = job_id
                                                    flag_modified(db_ai_message, "additionalData")
                                                    await self._db.flush()

                                                except Exception as e:
                                                    sentry_capture_exception(e)
                                                    logger.error(
                                                        f"Failed to create/update job from streaming AI response: {e}"
                                                    )
                                                    logger.error(
                                                        f"JobSummary received "
                                                        f"in final_data: {received_job_summary_dict}"
                                                    )

                                            await self._db.commit()
                                            logger.info(
                                                f"Saved AI message {db_ai_message.id} for chat "
                                                f"{chat.id} after final data provided."
                                            )

                                        case _:
                                            logger.warning("unknown event_data type")

                                    yield f"data: {json.dumps(event_data)}\n"

                            except json.JSONDecodeError as e:
                                logger.warning(f"Could not decode JSON data from SSE event: {event_data_str}")
                                logger.error(f"Error decoding JSON: {e}", exc_info=True)
                            except Exception as e:
                                logger.error(f"Error processing SSE data part: {e}", exc_info=True)
                        case _:
                            logger.warning(f"unknown line format in line: {line}")
                            yield f"data: {line}\n"

                if event_type == "end":
                    logger.info(f"Stream ended for chat {chat.id}")

                elif event_type == "error":
                    logger.error(f"Received error event from AI engine stream for chat {chat.id}: {event_data_str}")
                    break

        except Exception as e:
            logger.error(f"Error consuming AI stream for chat {chat.id}: {e}\n{traceback.format_exc()}")
            sentry_capture_exception(e)
            error_payload = {"code": "BACKEND_STREAM_ERROR", "message": "Error processing AI response stream."}
            yield f"event: error\ndata: {json.dumps(error_payload)}\n\n"
            yield "event: end\ndata: Stream finished with backend error\n\n"

    async def _prepare_message_and_chat(
        self, message_request: SendMessageRequest, user: User
    ) -> Tuple[Chat, Message, list[AttachmentDetails]] | None:
        """Gets/creates chat, saves user message, prepares attachment details."""
        if message_request.chatId:
            chat = await self._get_chat_by_id_for_user(message_request.chatId, user)
        else:
            chat = await self._create_new_chat(user, message_request.message.content[:50])

        db_user_message = Message(
            chat=chat,
            user=user,
            content=message_request.message.content,
            type=message_request.message.type,
            senderType="user",
            additionalData=message_request.message.additionalData.model_dump(),
        )
        self._db.add(db_user_message)
        await self._db.flush()

        attachments_details = None
        if message_request.attachments:
            await self._db.execute(
                update(Document)
                .where(
                    Document.id.in_(attachment.documentId for attachment in message_request.attachments),
                    Document.userId == user.id,
                )
                .values(messageId=db_user_message.id)
            )
            documents = await self._db.execute(
                select(Document).where(
                    Document.id.in_(attachment.documentId for attachment in message_request.attachments),
                    Document.userId == user.id,
                )
            )
            attachments_details = (
                AttachmentDetails(
                    documentId=document.id,
                    documentS3Key=document.s3Key,
                    documentS3Bucket=document.s3Bucket,
                    documentS3Url=f"s3://{document.s3Bucket}/{document.s3Key}",
                    userId=document.userId,
                    type=document.fileExtension,
                )
                for document in documents.scalars()
            )

        await self._db.commit()
        await self._db.refresh(db_user_message)

        return chat, db_user_message, attachments_details

    async def send_user_message_and_get_system_response(
        self, message_request: SendMessageRequest, user: User
    ) -> SendMessageResponse:
        logger.info(f"Processing non-streaming message for user {user.id}, chat {message_request.chatId}")
        chat, db_user_message, attachments_details = await self._prepare_message_and_chat(message_request, user)

        ai_response = await self._ai_engine_api.generate_response(
            UserMessage(
                userId=user.id,
                chatId=chat.id,
                messageId=db_user_message.id,
                message=message_request.message,
                attachments=attachments_details,
            )
        )
        db_ai_message = Message(
            chat=chat,
            content=ai_response.data.response.content,
            type=ai_response.data.response.type,
            senderType="system",
            additionalData=ai_response.data.additionalData,
        )
        self._db.add(db_ai_message)
        await self._db.flush()
        response_additional_data = ResponseMessageMetadata(
            timestamp=db_ai_message.timestamp,
            **ai_response.data.additionalData,
        )

        received_job_summary = ai_response.data.additionalData.get("jobSummary")
        if received_job_summary:
            try:  # enclose in try block to avoid crashing the whole process of saving and passing AI response
                job_summary = JobSummary(**ai_response.data.additionalData["jobSummary"])
                job_id = await self._handle_job_summary(job_summary, chat, user)
                response_additional_data.jobSummary["jobId"] = job_id
                db_ai_message.additionalData["jobSummary"]["jobId"] = job_id
                flag_modified(db_ai_message, "additionalData")
            except Exception as e:
                sentry_capture_exception(e)
                logger.error(f"Failed to create job from AI response: {e}")
                logger.error(f"JobSummary received: {ai_response.data.additionalData['jobSummary']}")

        return SendMessageResponse(
            chatId=chat.id,
            userMessageId=db_user_message.id,
            message=ResponseMessage(
                content=ai_response.data.response.content,
                type=ai_response.data.response.type,
                additionalData=response_additional_data,
            ),
            systemMessageId=db_ai_message.id,
            attachments=[],
        )

    async def _handle_job_summary(self, job_summary: JobSummary, chat: Chat, user: User) -> int:
        if job_summary.jobId:  # update existing
            db_job = (
                await self._db.execute(select(Job).where(Job.id == job_summary.jobId, Job.userId == user.id))
            ).scalar_one()
            db_job.headline = job_summary.jobHeadline
            db_job.subTitle = job_summary.jobSubTitle
            db_job.details = job_summary.jobDetails
            db_job.urgency = job_summary.jobDate
            db_job.availability = job_summary.jobTimeOfDay
        else:  # create new
            users_property = await self._property_service.get_first_property_for_user(user)
            db_project = Project(
                headline=job_summary.jobHeadline,
                subTitle=job_summary.jobSubTitle,
                details=job_summary.jobDetails,
                urgency=job_summary.jobDate,
                chat=chat,
                user=user,
                propertyId=users_property.id if users_property else None,
            )
            self._db.add(db_project)
            db_job = Job(
                headline=job_summary.jobHeadline,
                subTitle=job_summary.jobSubTitle,
                details=job_summary.jobDetails,
                urgency=job_summary.jobDate,
                availability=job_summary.jobTimeOfDay,
                status="created",
                chat=chat,
                user=user,
                project=db_project,
                propertyId=users_property.id if users_property else None,
            )
            self._db.add(db_job)
            await self._db.flush()

        return db_job.id

    @staticmethod
    def list_chats_for_user_query(user: User):
        return (
            select(Chat)
            .join(Message, Message.chatId == Chat.id)
            .where(Chat.userId == user.id)
            .order_by(func.max(Message.timestamp).desc())
            .group_by(Chat.id)
        )

    @staticmethod
    def list_messages_for_chat_query(chat_id: int, user: User):
        return (
            select(Message)
            .where(Message.chatId == chat_id, Message.chat.has(Chat.userId == user.id))
            .order_by(Message.timestamp.desc())
        )

    async def _get_chat_by_id_for_user(self, chat_id: int, user: User) -> Chat:
        return (await self._db.execute(select(Chat).where(Chat.id == chat_id, Chat.userId == user.id))).scalar_one()

    async def _create_new_chat(self, user: User, title: str):
        users_property = await self._property_service.get_first_property_for_user(user)
        chat = Chat(user=user, title=title, propertyId=users_property.id if users_property else None)
        self._db.add(chat)
        return chat
