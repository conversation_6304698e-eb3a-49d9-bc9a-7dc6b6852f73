import asyncio
import datetime
import logging
from datetime import timed<PERSON><PERSON>

from sentry_sdk import capture_exception as sentry_capture_exception
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import or_

from src.db_models.job import Job
from src.db_models.user import User
from src.integrations.hubspot import Hub<PERSON><PERSON><PERSON><PERSON>, JobWithDocumentsUrls
from src.schemas import B2BDemoRequest
from src.services.documents import DocumentService

logger = logging.getLogger("uvicorn")


class HubSpotSyncService:
    def __init__(self, db: AsyncSession, hubspot_api: HubSpotAPI, document_service: DocumentService, retry_delay=1.0):
        self._db = db
        self._hubspot_api = hubspot_api
        self._document_service = document_service
        self._retry_delay = retry_delay

    async def __try(self, fn):
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                await fn()
                await self._db.commit()
                return
            except Exception as e:
                logger.error(f"HubSpotSyncService failure (attempt: {attempt + 1} / {max_attempts}): {e}")
                sentry_capture_exception(e)
                await asyncio.sleep(self._retry_delay)

    async def sync_users(self):
        query = (
            select(User.id)
            .where(User.hubspotId.is_(None), User.deletedAt.is_(None), User.clerkId.is_not(None))
            .limit(self._hubspot_api.get_batch_size_limit())
        )
        user_ids = (await self._db.scalars(query)).all()

        async def fn():
            users = (await self._db.scalars(select(User).where(User.id.in_(user_ids)))).all()
            logger.info(f"Syncing {len(users)} users to HubSpot")
            await self._hubspot_api.upsert_contacts(list(users))

        await self.__try(fn)

    async def sync_user(self, user_id: int):
        async def fn():
            user = await self._db.scalar(select(User).where(User.id == user_id))
            logger.info(f"Syncing user id {user.id} to HubSpot")
            await self._hubspot_api.upsert_contacts([user])

        await self.__try(fn)

    async def sync_jobs(self):
        cutoff_time = datetime.datetime.now() - timedelta(
            seconds=self._document_service.get_presigned_url_expiration_in_seconds()
        )
        logger.info(f"sync_jobs cutoff_time = {cutoff_time}")
        query = (
            select(Job.id)
            .join(User, Job.userId == User.id)
            .where(
                or_(Job.hubspotId.is_(None), Job.hubspotSyncAt.is_(None), Job.hubspotSyncAt < cutoff_time),
                User.deletedAt.is_(None),
                User.hubspotId.is_not(None),
                Job.status == "user_accepted",
            )
            .limit(self._hubspot_api.get_batch_size_limit())
        )
        logger.info(f"query = {str(query)}")
        job_ids = (await self._db.scalars(query)).all()
        logger.info(f"Found {len(job_ids)} jobs to sync to HubSpot")

        async def fn():
            jobs = (await self._db.scalars(select(Job).where(Job.id.in_(job_ids)))).all()
            jobs_with_docs: list[JobWithDocumentsUrls] = [await self.__to_job_with_documents_urls(job) for job in jobs]
            logger.info(f"Syncing {len(jobs_with_docs)} jobs to HubSpot")
            await self._hubspot_api.upsert_job_tickets(jobs_with_docs)

        await self.__try(fn)

    async def __to_job_with_documents_urls(self, job: Job) -> JobWithDocumentsUrls:
        document_urls = await self._document_service.get_presigned_urls_of_chat_documents(job.chatId)
        return JobWithDocumentsUrls(job, document_urls)

    async def sync_job(self, job_id: int):
        async def fn():
            job = await self._db.scalar(select(Job).where(Job.id == job_id))
            job_with_docs = await self.__to_job_with_documents_urls(job)
            logger.info(
                f"Syncing job id {job.id} (along with {len(job_with_docs.document_urls)} "
                f"documents from chat id {job.chatId}) to HubSpot"
            )
            await self._hubspot_api.upsert_job_ticket(job_with_docs)

        await self.__try(fn)

    async def sync_b2b_demo_request(self, b2b_demo_request: B2BDemoRequest):
        async def fn():
            await self._hubspot_api.create_b2b_demo_request_ticket(b2b_demo_request)

        await self.__try(fn)
