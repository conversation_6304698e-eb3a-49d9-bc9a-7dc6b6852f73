import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy import select, delete, update
from sqlalchemy.ext.asyncio import AsyncSession
from src.agents.ContextualPromptsAgent import ContextualPromptAgent

from src.db_models.chat import Chat
from src.db_models.source_link import SourceType
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.integrations.aiengine import AbstractAIEngineAPI
from src.schemas import (
    LinkedEntity,
    LinkedEntityType,
    FindTodo,
    TodoCreate,
    TodoShortInfo,
    TodoUpdate,
    TodoAiCreate,
    TodoAiUpdate,
    TodoFullInfo,
)
from src.services.entity_links import EntityLinksService
from src.services.source_links import SourceLinksService


logger = logging.getLogger("uvicorn")


class TodoDoesNotExist(Exception):
    pass


class TodoService:
    def __init__(
        self,
        db: AsyncSession,
        source_link_service: SourceLinksService,
        ai_engine_service: AbstractAIEngineAPI,
        entity_link_service: EntityLinksService,
        expiration_after_minutes: int,
        contextual_prompt_agent: "ContextualPromptAgent" = None,
    ):
        self._db = db
        self._source_link_service = source_link_service
        self._ai_engine_service = ai_engine_service
        self._entity_link_service = entity_link_service
        self._expiration_after_minutes = expiration_after_minutes
        self._contextual_prompt_agent = contextual_prompt_agent

    def _run_contextual_prompts_in_background(self, user_id: int, todo: Todo):
        """Run contextual prompt generation in background without blocking."""
        if self._contextual_prompt_agent:
            # Create a fire-and-forget task
            asyncio.create_task(self._generate_contextual_prompts_with_error_handling(user_id, todo))
            logger.info(f"Started background contextual prompt generation " f"for todo_id={todo.id}")
        else:
            logger.warning("ContextualPromptAgent not available, " "skipping contextual prompt generation")

    async def _generate_contextual_prompts_with_error_handling(self, user_id: int, todo: Todo):
        """Generate contextual prompts with comprehensive error handling."""
        try:
            await self._contextual_prompt_agent.generate_contextual_prompts(user_id, todo)
        except Exception as e:
            # Log the error but don't let it crash the application
            logger.exception(
                f"Error generating contextual prompts for todo_id={todo.id}, " f"user_id={user_id}: {str(e)}"
            )

    @staticmethod
    def get_todos_for_user_query(user: User):
        return select(Todo).where(Todo.userId == user.id)

    def get_suggested_todos_query(self, user: User, limit: int | None = None):
        """Returns suggested by alfie todos, ready to accept or reject by the user."""
        q = (
            self.get_todos_for_user_query(user)
            .where(Todo.type == TodoType.systemCreated)
            .order_by(Todo.created_at.desc())
        )

        if limit:
            q = q.limit(limit)

        return q

    def get_accepted_todos_query(self, user: User):
        """Returns todos that were created by the system and accepted by the user or created directly by the user."""
        return self.get_todos_for_user_query(user).where(
            Todo.type.in_([TodoType.userCreated, TodoType.systemCreatedUserAccepted])
        )

    async def __get_todo_by_user(self, todo_id: int, user: User) -> Todo:
        return await self._db.scalar(select(Todo).where(Todo.userId == user.id, Todo.id == todo_id))

    async def __create_todo(
        self,
        todo_dict: dict,
        user: User,
        todo_type: TodoType,
        source_type: SourceType,
        source_id: int,
    ) -> Todo:
        todo = Todo(**todo_dict, userId=user.id, type=todo_type)
        self._db.add(todo)
        await self._db.flush()
        await self._db.refresh(todo)

        await self._source_link_service.link_creation_from_source(
            destination=todo,
            source_type=source_type,
            source_id=source_id,
            create_command=todo_dict,
        )

        return todo

    async def create_todo_by_user(self, todo_data: TodoCreate, user: User) -> Todo:
        todo_dict = todo_data.model_dump(exclude_unset=True)
        todo = await self.__create_todo(
            todo_dict=todo_dict,
            user=user,
            todo_type=TodoType.userCreated,
            source_type=SourceType.userInput,
            source_id=user.id,
        )

        # Generate contextual prompts in the background
        self._run_contextual_prompts_in_background(user.id, todo)

        return todo

    async def create_todo_by_ai(self, todo_data: TodoAiCreate, user: User) -> Todo:
        todo_dict = todo_data.todo.model_dump(exclude_unset=True)
        todo = await self.__create_todo(
            todo_dict=todo_dict,
            user=user,
            todo_type=TodoType.systemCreated,
            source_type=todo_data.srcType,
            source_id=todo_data.srcId,
        )
        if todo_data.srcType != SourceType.chat:
            return todo

        await self._entity_link_service.link(
            LinkedEntity(id=todo.id, entityType=LinkedEntityType.todos),
            LinkedEntity(id=todo_data.srcId, entityType=LinkedEntityType.chats),
            user.id,
        )
        return todo

    async def __update_todo(self, todo: Todo, source_type: SourceType, source_id: int, command_dict: dict) -> Todo:
        command = await self._source_link_service.process_command_from_source(
            destination=todo,
            source_type=source_type,
            source_id=source_id,
            command=command_dict,
        )
        for key, value in command.items():
            setattr(todo, key, value)
        await self._db.flush()
        return todo

    async def update_todo_by_user(self, todo_id: int, todo_update: TodoUpdate, user: User) -> Todo:
        todo = await self.__get_todo_by_user(todo_id, user)
        if not todo:
            raise TodoDoesNotExist("The ToDo does not exist.")
        updated_todo = await self.__update_todo(
            todo=todo,
            source_type=SourceType.userInput,
            source_id=user.id,
            command_dict=todo_update.model_dump(exclude_unset=True),
        )

        # Generate contextual prompts in the background
        self._run_contextual_prompts_in_background(user.id, updated_todo)

        return updated_todo

    async def update_todo_by_ai(self, todo_id: int, todo_update: TodoAiUpdate) -> Todo:
        todo = await self._db.scalar(select(Todo).where(Todo.id == todo_id))
        return await self.__update_todo(
            todo=todo,
            source_type=todo_update.srcType,
            source_id=todo_update.srcId,
            command_dict=todo_update.todo.model_dump(exclude_unset=True),
        )

    async def delete_todo_by_user(self, todo_id: int, user: User):
        if not await self.__get_todo_by_user(todo_id, user):
            raise TodoDoesNotExist("The ToDo does not exist.")

        await self._db.execute(delete(Todo).where(Todo.id == todo_id))

    async def accept_todo_by_user(self, todo_id: int, user: User):
        return await self._modify_todo_type(todo_id, user, TodoType.systemCreatedUserAccepted)

    async def reject_todo_by_user(self, todo_id: int, user: User):
        return await self._modify_todo_type(todo_id, user, TodoType.systemCreatedUserRejected)

    async def _modify_todo_type(self, todo_id: int, user: User, new_type: TodoType):
        query = self.get_todos_for_user_query(user).where(Todo.id == todo_id, Todo.type == TodoType.systemCreated)
        todo = await self._db.scalar(query)
        if not todo:
            raise TodoDoesNotExist(f"A system created ToDo of id {todo_id} does not exist.")

        command = await self._source_link_service.process_command_from_source(
            destination=todo,
            source_type=SourceType.userInput,
            source_id=user.id,
            command={"type": new_type},
        )
        todo.type = command["type"]
        await self._db.flush()

    async def get_todo(self, todo_id: int, user: User) -> TodoFullInfo:
        todo = await self.__get_todo_by_user(todo_id, user)
        if not todo:
            raise TodoDoesNotExist(f"A ToDo of id {todo_id} does not exist.")
        sources = await self._source_link_service.get_source_links(todo)
        todo_data = TodoShortInfo.model_validate(todo, from_attributes=True)
        return TodoFullInfo(**todo_data.model_dump(), sources=sources)

    async def create_todos_for_chat(self, chat: Chat) -> list[TodoFullInfo]:
        todos: list[FindTodo] = await self._ai_engine_service.find_todos(chat)
        results = []
        logger.info(f"Found {len(todos)} todos for chat {chat.id}")
        for todo in todos:
            todo_ai_create = TodoAiCreate(
                todo=TodoCreate(
                    name=todo.title,
                    description=todo.description,
                    dueDate=todo.dueDate,
                ),
                srcType=SourceType.chat,
                srcId=chat.id,
            )
            todo_obj = await self.create_todo_by_ai(todo_ai_create, chat.user)
            logger.info(f"Created todo {todo_obj.id} for chat {chat.id}")
            results.append(todo_obj)

        return results

    async def expire_todos(self) -> int:
        """
        Expire todos that are in systemCreated status and older than the expiration threshold.
        Changes their status to systemCreatedSystemExpired.
        Returns the number of todos that were expired.
        """
        expiration_cutoff = datetime.utcnow() - timedelta(minutes=self._expiration_after_minutes)

        # Update todos that are systemCreated and older than the cutoff
        result = await self._db.execute(
            update(Todo)
            .where(Todo.type == TodoType.systemCreated, Todo.created_at < expiration_cutoff)
            .values(type=TodoType.systemCreatedSystemExpired)
        )

        await self._db.commit()
        return result.rowcount
