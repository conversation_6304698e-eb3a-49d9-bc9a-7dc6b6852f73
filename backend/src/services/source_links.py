import logging
from typing import Union, Any, TypeVar

from sqlalchemy import func, select, delete
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models import BaseModel
from src.db_models.appliance import Appliance
from src.db_models.bill import Bill
from src.db_models.chat import Chat
from src.db_models.document import Document, DocumentCategoryType
from src.db_models.insurance import Insurance
from src.db_models.legal import Legal
from src.db_models.notification import InformationFromDocumentSavedNotification
from src.db_models.property import Property
from src.db_models.source_link import SourceLink, SourceType
from src.db_models.todo import Todo
from src.db_models.user import User
from src.schemas import FieldSource, ChatSource, DocumentSource, UserSource, NotificationCreate
from src.services.notifications import NotificationService

logger = logging.getLogger("uvicorn")

Destination = TypeVar("Destination", bound=BaseModel)


class SourceLinksService:
    def __init__(self, db: AsyncSession, notification_service: NotificationService):
        self._db = db
        self._notification_service = notification_service

    @staticmethod
    def __select(src_type: SourceType, src_id: int):
        match src_type:
            case SourceType.chat:
                return select(Chat).where(Chat.id == src_id)
            case SourceType.document:
                return select(Document).where(Document.id == src_id)
            case SourceType.userInput:
                return select(User).where(User.id == src_id)
            case _:
                raise ValueError(f"Unsupported source type: {src_type}")

    async def __does_source_exist(self, src_type: SourceType, src_id: int) -> bool:
        return (await self._db.execute(self.__select(src_type, src_id))).scalar() is not None

    async def link_creation_from_source(
        self, destination: Destination, source_type: SourceType, source_id: int, create_command: dict[str, Any]
    ):
        await self.__link_command(destination, source_type, source_id, create_command)
        await self.__notify(destination, source_type, source_id)

    FilteredCommand = dict[str, Any]

    async def process_command_from_source(
        self, destination: Destination, source_type: SourceType, source_id: int, command: dict[str, Any]
    ) -> FilteredCommand:
        if not await self.__does_source_exist(source_type, source_id):
            raise ValueError(f"Source of type {source_type} with ID {source_id} does not exist.")

        if source_type == SourceType.chat or source_type == SourceType.document:
            user_input_fields = (
                await self._db.scalars(
                    select(SourceLink).where(
                        SourceLink.destTable == destination.__tablename__,
                        SourceLink.destId == destination.id,
                        SourceLink.srcType == SourceType.userInput,
                    )
                )
            ).all()
            for user_input_field in user_input_fields:
                if user_input_field.destField in command:
                    logger.info(f"User input field {user_input_field.destField} will not be overwritten.")
                    del command[user_input_field.destField]

        await self.__link_command(destination, source_type, source_id, command)
        await self.__notify(destination, source_type, source_id)
        return command

    async def __notify(self, destination: Destination, source_type: SourceType, source_id: int):
        if source_type == SourceType.document:
            logger.info(f"Source type (source id: {source_id}) is 'document'. Sending notification about it.")
            await self.__notify_about_information_from_document_saved(destination=destination, document_id=source_id)

    async def __link_command(
        self, destination: Destination, source_type: SourceType, source_id: int, command: dict[str, Any]
    ):
        for field, value in command.items():
            if value is None:
                await self._db.execute(
                    delete(SourceLink).where(
                        SourceLink.destTable == destination.__tablename__,
                        SourceLink.destId == destination.id,
                        SourceLink.destField == field,
                    )
                )
            else:
                await self._db.execute(
                    insert(SourceLink)
                    .values(
                        destTable=destination.__tablename__,
                        destId=destination.id,
                        destField=field,
                        srcType=source_type,
                        srcId=source_id,
                    )
                    .on_conflict_do_update(
                        index_elements=["destTable", "destId", "destField"],
                        set_={"srcType": source_type, "srcId": source_id, "updated_at": func.now()},
                    )
                )

    @staticmethod
    def __destination_to_document_category_type(destination: Destination) -> DocumentCategoryType:
        match destination.__tablename__:
            case Appliance.__tablename__:
                return DocumentCategoryType.appliance
            case Property.__tablename__:
                return DocumentCategoryType.propertyDetails
            case Insurance.__tablename__:
                return DocumentCategoryType.insurance
            case Legal.__tablename__:
                return DocumentCategoryType.legal
            case Bill.__tablename__:
                return DocumentCategoryType.billsAndSubscriptions
            case Todo.__tablename__:
                return DocumentCategoryType.todo
            case _:
                return DocumentCategoryType.other

    async def __notify_about_information_from_document_saved(self, destination: Destination, document_id: int):
        document = await self._db.scalar(select(Document).where(Document.id == document_id))
        if document is None:
            raise ValueError(f"Document with ID {document_id} does not exist.")
        payload = InformationFromDocumentSavedNotification(
            fileName=document.originalFileName,
            category=self.__destination_to_document_category_type(destination),
            documentId=document.id,
        )
        data = NotificationCreate(context=document.uploadContext, payload=payload)
        user_id = document.userId
        await self._notification_service.create_notification(data, user_id=user_id)

    @staticmethod
    def __to_source_schema(
        source: Union[Chat, Document, User, Appliance],
    ) -> Union[ChatSource, DocumentSource, UserSource]:
        match source:
            case Chat():
                return ChatSource(id=source.id, title=source.title)
            case Document():
                return DocumentSource(id=source.id)
            case User():
                return UserSource(id=source.id)
            case _:
                raise ValueError(f"Unsupported source type: {type(source)}")

    async def get_source_links(self, destination: Destination) -> list[FieldSource]:
        source_links = await self._db.scalars(
            select(SourceLink).where(
                SourceLink.destTable == destination.__tablename__, SourceLink.destId == destination.id
            )
        )
        sources: dict[str, Union[Chat, Document, User, Appliance]] = {}
        result: list[FieldSource] = []
        for source_link in source_links.all():
            key = f"{source_link.srcType}_{source_link.srcId}"
            source = (
                sources[key]
                if key in sources
                else (await self._db.execute(self.__select(source_link.srcType, source_link.srcId))).scalar()
            )
            sources[key] = source
            result.append(FieldSource(destinationField=source_link.destField, source=self.__to_source_schema(source)))
        return result
