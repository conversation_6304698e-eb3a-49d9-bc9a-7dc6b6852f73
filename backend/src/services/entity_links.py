from typing import Type, TypeVar, List
from sqlalchemy import delete, or_, and_, select
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.appliance import Appliance
from src.db_models.chat import Chat
from src.db_models.contextual_prompt import ContextualPrompt
from src.db_models.document import Document
from src.db_models.entity_link import EntityLink
from src.db_models.insurance import Insurance
from src.db_models.job import Job
from src.db_models.legal import Legal
from src.db_models.project import Project
from src.db_models.todo import Todo
from src.db_models.user import User
from src.db_models.message import Message as MessageDb
from src.schemas import (
    LinkedEntity,
    LinkedEntityInfo,
    LinkedEntityType,
    UserLinkedEntityInfo,
    ChatLinkedEntityInfo,
    ProjectLinkedEntityInfo,
    DocumentLinkedEntityInfo,
    TodoLinkedEntityInfo,
    ApplianceLinkedEntityInfo,
    InsuranceLinkedEntityInfo,
    JobLinkedEntityInfo,
    LegalLinkedEntityInfo,
    UserDetails,
    ChatInfo,
    DocumentInfo,
    TodoShortInfo,
    ApplianceInfo,
    JobInfo,
    ContextualPromptLinkedEntityInfo,
    Message,
)

T = TypeVar("T")
NUMBER_OF_PREVIEW_CHAT_MESSAGES = 5


class EntityLinksService:
    """
    Links entities together (unidirectionally).
    """

    def __init__(self, db: AsyncSession):
        self._db = db

    @staticmethod
    def _order_entities(a: LinkedEntity, b: LinkedEntity) -> tuple[str, int, str, int]:
        if (a.entityType.value, a.id) <= (b.entityType.value, b.id):
            return a.entityType.value, a.id, b.entityType.value, b.id
        else:
            return b.entityType.value, b.id, a.entityType.value, a.id

    async def _validate_entity_exists(self, entity: LinkedEntity):
        """Validate that an entity exists in the database"""
        match entity.entityType:
            case LinkedEntityType.users:
                await self._get_entity_or_raise(User, entity.id)
            case LinkedEntityType.chats:
                await self._get_entity_or_raise(Chat, entity.id)
            case LinkedEntityType.projects:
                await self._get_entity_or_raise(Project, entity.id)
            case LinkedEntityType.documents:
                await self._get_entity_or_raise(Document, entity.id)
            case LinkedEntityType.todos:
                await self._get_entity_or_raise(Todo, entity.id)
            case LinkedEntityType.appliances:
                await self._get_entity_or_raise(Appliance, entity.id)
            case LinkedEntityType.insurances:
                await self._get_entity_or_raise(Insurance, entity.id)
            case LinkedEntityType.jobs:
                await self._get_entity_or_raise(Job, entity.id)
            case LinkedEntityType.legals:
                await self._get_entity_or_raise(Legal, entity.id)
            case LinkedEntityType.contextual_prompts:
                await self._get_entity_or_raise(ContextualPrompt, entity.id)
            case _:
                raise Exception(f"Unsupported entity type: {entity.entityType}")

    async def link(self, a: LinkedEntity, b: LinkedEntity, user_id: int):
        await self._validate_entity_exists(a)
        await self._validate_entity_exists(b)
        type_a, id_a, type_b, id_b = self._order_entities(a, b)
        await self._db.execute(
            insert(EntityLink)
            .values(typeA=type_a, idA=id_a, typeB=type_b, idB=id_b, userId=user_id)
            .on_conflict_do_nothing()
        )

    async def unlink(self, a: LinkedEntity, b: LinkedEntity, user_id: int):
        type_a, id_a, type_b, id_b = self._order_entities(a, b)

        await self._db.execute(
            delete(EntityLink).where(
                and_(
                    EntityLink.typeA == type_a,
                    EntityLink.idA == id_a,
                    EntityLink.typeB == type_b,
                    EntityLink.idB == id_b,
                )
            )
        )

    async def unlink_all(self, entity: LinkedEntity, user_id: int):
        await self._db.execute(
            delete(EntityLink).where(
                and_(
                    EntityLink.userId == user_id,
                    or_(
                        and_(
                            EntityLink.typeA == entity.entityType.value,
                            EntityLink.idA == entity.id,
                        ),
                        and_(EntityLink.typeB == entity.entityType.value, EntityLink.idB == entity.id),
                    ),
                )
            )
        )

    async def _get_entity_or_raise(self, model_class: Type[T], entity_id: int) -> T:
        entity = await self._db.scalar(select(model_class).where(entity_id == model_class.id))
        if entity is None:
            raise ValueError(f"{model_class.__name__} with id {entity_id} not found")
        return entity

    async def _get_linked_entity_info(self, linked_type: LinkedEntityType, linked_id: int) -> LinkedEntityInfo:
        match linked_type:
            case LinkedEntityType.users:
                user = await self._get_entity_or_raise(User, linked_id)
                user_details = UserDetails.model_validate(user)
                return UserLinkedEntityInfo(id=linked_id, entity=user_details)
            case LinkedEntityType.chats:
                chat = await self._get_entity_or_raise(Chat, linked_id)
                stmt = (
                    select(MessageDb)
                    .where(MessageDb.chatId == linked_id)
                    .order_by(MessageDb.timestamp.desc())
                    .limit(NUMBER_OF_PREVIEW_CHAT_MESSAGES)
                )
                last_messages_db = (await self._db.scalars(stmt)).all()
                messages_info = [Message.model_validate(msg) for msg in last_messages_db]
                chat_info = ChatInfo.model_validate(chat)
                return ChatLinkedEntityInfo(id=linked_id, entity=chat_info, lastMessages=messages_info)
            case LinkedEntityType.projects:
                return ProjectLinkedEntityInfo(id=linked_id)
            case LinkedEntityType.documents:
                document = await self._get_entity_or_raise(Document, linked_id)
                document_info = DocumentInfo.model_validate(document)
                return DocumentLinkedEntityInfo(id=linked_id, entity=document_info)
            case LinkedEntityType.todos:
                todo = await self._get_entity_or_raise(Todo, linked_id)
                todo_info = TodoShortInfo.model_validate(todo)
                return TodoLinkedEntityInfo(id=linked_id, entity=todo_info)
            case LinkedEntityType.appliances:
                appliance = await self._get_entity_or_raise(Appliance, linked_id)
                appliance_info = ApplianceInfo.model_validate(appliance)
                return ApplianceLinkedEntityInfo(id=linked_id, entity=appliance_info)
            case LinkedEntityType.insurances:
                return InsuranceLinkedEntityInfo(id=linked_id)
            case LinkedEntityType.jobs:
                job = await self._get_entity_or_raise(Job, linked_id)
                job_info = JobInfo.model_validate(job)
                return JobLinkedEntityInfo(id=linked_id, entity=job_info)
            case LinkedEntityType.legals:
                return LegalLinkedEntityInfo(id=linked_id)
            case LinkedEntityType.contextual_prompts:
                contextual_prompt = await self._get_entity_or_raise(ContextualPrompt, linked_id)
                return ContextualPromptLinkedEntityInfo(
                    id=linked_id, type=contextual_prompt.type, prompt=contextual_prompt.prompt
                )
            case _:
                raise Exception(f"Unsupported entity type: {linked_type}")

    async def _to_linked_entity_info(self, base_entity: LinkedEntity, link: EntityLink) -> LinkedEntityInfo:
        if link.typeA == base_entity.entityType.value and link.idA == base_entity.id:
            linked_type = LinkedEntityType(link.typeB)
            linked_id = link.idB
        else:
            linked_type = LinkedEntityType(link.typeA)
            linked_id = link.idA
        return await self._get_linked_entity_info(linked_type, linked_id)

    async def get_links(self, entity: LinkedEntity, user_id: int) -> List[LinkedEntityInfo]:
        results = await self._db.scalars(
            select(EntityLink).where(
                and_(
                    EntityLink.userId == user_id,
                    or_(
                        and_(
                            EntityLink.typeA == entity.entityType.value,
                            EntityLink.idA == entity.id,
                        ),
                        and_(EntityLink.typeB == entity.entityType.value, EntityLink.idB == entity.id),
                    ),
                )
            )
        )
        return [await self._to_linked_entity_info(entity, link) for link in results]
