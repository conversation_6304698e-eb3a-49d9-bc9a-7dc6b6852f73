import json
from datetime import datetime, timed<PERSON><PERSON>

import aioboto3
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src import settings
from src.db_models.chat import Chat
from src.db_models.job import Job, JobStatus
from src.db_models.message import Message


class ChatService:
    def __init__(
        self,
        db: AsyncSession,
        aioboto3_session: aioboto3.Session,
        abandoned_chat_after_minutes: int,
    ):
        self._db = db
        self._aioboto3_session = aioboto3_session
        self._abandoned_chat_after_minutes = abandoned_chat_after_minutes

    async def enqueue_abandoned_chats(self, limit: int | None = None) -> None:
        """
        Enqueues abandoned chats for processing.
        This method fetches abandoned chats and enqueue them to sqs queue.
        """
        assert (
            settings.ABANDONED_CHATS_PROCESSING_QUEUE_URL
        ), "ABANDONED_CHATS_PROCESSING_QUEUE_URL is not set in settings."
        abandoned_chats = await self.get_abandoned_chats(limit=limit)
        async with self._aioboto3_session.client("sqs") as sqs:
            for chat in abandoned_chats:
                message = {"chat_id": chat.id}
                await sqs.send_message(
                    QueueUrl=settings.ABANDONED_CHATS_PROCESSING_QUEUE_URL,
                    MessageBody=json.dumps(message),
                    MessageDeduplicationId=str(f"abandoned-chat-{chat.id}"),
                    MessageGroupId="default",
                )

    async def get_chat_by_id(self, chat_id) -> Chat:
        query = (
            select(Chat).where(Chat.id == chat_id).options(selectinload(Chat.messages)).options(selectinload(Chat.user))
        )
        return (await self._db.execute(query)).scalars().one()

    async def get_abandoned_chats(self, limit: int | None = None) -> list[Chat]:
        last_message = (
            select(
                Message.chatId.label("chatId"),
                func.max(Message.timestamp).label("last_timestamp"),
            ).group_by(Message.chatId)
        ).subquery()

        user_accepted_jobs = (
            select(Job.id)
            .where(
                Job.chatId == Chat.id,
                Job.status == JobStatus.USER_ACCEPTED,
            )
            .correlate(Chat)
        )

        query = (
            select(Chat)
            .join(last_message, Chat.id == last_message.c.chatId)
            .outerjoin(Chat.jobs)
            .where(
                ~user_accepted_jobs.exists(),
                # chat is abandoned after the delta
                last_message.c.last_timestamp
                < datetime.utcnow() - timedelta(minutes=self._abandoned_chat_after_minutes),
                Chat.was_processed_as_abandoned.is_not(True),
            )
            .options(selectinload(Chat.messages))
            .options(selectinload(Chat.user))
            .order_by(Chat.created_at)
        )

        if limit is not None:
            query = query.limit(limit)

        return list((await self._db.execute(query)).scalars().all())

    async def mark_as_processed_as_abandoned(self, chat: Chat) -> None:
        chat.was_processed_as_abandoned = True
        self._db.add(chat)
        await self._db.commit()
