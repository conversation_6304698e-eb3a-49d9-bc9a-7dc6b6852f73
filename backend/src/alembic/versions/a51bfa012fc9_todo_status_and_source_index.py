"""todo status and source index

Revision ID: a51bfa012fc9
Revises: ea0c10398ae6
Create Date: 2025-07-30 11:25:19.020897

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a51bfa012fc9'
down_revision: Union[str, None] = 'ea0c10398ae6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE todos SET status = 'created' WHERE status IS NULL")
    op.create_index(op.f('ix_todos_source'), 'todos', ['source'], unique=False)
    op.create_index(op.f('ix_todos_status'), 'todos', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_todos_status'), table_name='todos')
    op.drop_index(op.f('ix_todos_source'), table_name='todos')
    # ### end Alembic commands ###
