"""add contextual_prompt table

Revision ID: d4979ee6a334
Revises: f9208a112a83
Create Date: 2025-07-17 18:25:32.217683

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd4979ee6a334'
down_revision: Union[str, None] = 'f9208a112a83'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contextual_prompts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('prompt', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contextual_prompts_id'), 'contextual_prompts', ['id'], unique=False)
    op.create_index(op.f('ix_contextual_prompts_type'), 'contextual_prompts', ['type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_contextual_prompts_type'), table_name='contextual_prompts')
    op.drop_index(op.f('ix_contextual_prompts_id'), table_name='contextual_prompts')
    op.drop_table('contextual_prompts')
    # ### end Alembic commands ###
