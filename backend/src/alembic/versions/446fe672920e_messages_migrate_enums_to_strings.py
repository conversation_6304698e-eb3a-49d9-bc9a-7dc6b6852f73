"""messages migrate enums to strings

Revision ID: 446fe672920e
Revises: cea155c99d24
Create Date: 2025-06-30 20:42:53.821186

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '446fe672920e'
down_revision: Union[str, None] = 'cea155c99d24'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('messages', 'type',
               existing_type=postgresql.ENUM('text', 'image', 'diagnostic_report', name='message_type'),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('messages', 'senderType',
               existing_type=postgresql.ENUM('user', 'system', 'customer_support', name='sender_type'),
               type_=sa.String(),
               existing_nullable=False)
    op.create_index('ix_messages_chat_id_timestamp', 'messages', ['chatId', sa.text('timestamp DESC')], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_messages_chat_id_timestamp', table_name='messages')
    op.alter_column('messages', 'senderType',
               existing_type=sa.String(),
               type_=postgresql.ENUM('user', 'system', 'customer_support', name='sender_type'),
               existing_nullable=False)
    op.alter_column('messages', 'type',
               existing_type=sa.String(),
               type_=postgresql.ENUM('text', 'image', 'diagnostic_report', name='message_type'),
               existing_nullable=False)
    # ### end Alembic commands ###
