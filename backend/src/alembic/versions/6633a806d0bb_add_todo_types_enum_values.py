"""Add todo types enum values

Revision ID: 6633a806d0bb
Revises: f4e137b6307b
Create Date: 2025-06-30 13:56:25.355485

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6633a806d0bb'
down_revision: Union[str, None] = 'f4e137b6307b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("ALTER TYPE todotype ADD VALUE IF NOT EXISTS 'systemCreatedUserAccepted'")
    op.execute("ALTER TYPE todotype ADD VALUE IF NOT EXISTS 'systemCreatedUserRejected'")


def downgrade() -> None:
    pass
