"""secured entity links

Revision ID: 5bb84962bcca
Revises: 21ceb0adcd8a
Create Date: 2025-07-18 11:58:20.273082

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5bb84962bcca'
down_revision: Union[str, None] = '21ceb0adcd8a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


# I have verified that all non-nullable columns do not need a default value


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DELETE FROM entity_links")
    op.add_column('entity_links', sa.Column('userId', sa.Integer(), nullable=False))
    op.create_index(op.f('ix_entity_links_userId'), 'entity_links', ['userId'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_entity_links_userId'), table_name='entity_links')
    op.drop_column('entity_links', 'userId')
    # ### end Alembic commands ###
