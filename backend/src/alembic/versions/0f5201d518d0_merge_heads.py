"""merge heads

Revision ID: 0f5201d518d0
Revises: 6e0da1166c8f, d4979ee6a334
Create Date: 2025-07-22 14:11:14.757095

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0f5201d518d0'
down_revision: Union[str, None] = ('6e0da1166c8f', 'd4979ee6a334')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
