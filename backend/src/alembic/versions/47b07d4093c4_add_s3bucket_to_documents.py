"""add s3Bucket to documents

Revision ID: 47b07d4093c4
Revises: 58d12686a186
Create Date: 2025-01-31 10:22:52.410211

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '47b07d4093c4'
down_revision: Union[str, None] = '58d12686a186'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('s3Bucket', sa.String(), nullable=False, server_default=''))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('documents', 's3Bucket')
    # ### end Alembic commands ###
