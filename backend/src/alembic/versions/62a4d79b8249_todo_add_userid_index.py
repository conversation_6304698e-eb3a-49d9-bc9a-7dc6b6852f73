"""todo add userid index

Revision ID: 62a4d79b8249
Revises: 95a9d6e39e26
Create Date: 2025-06-30 20:33:26.426296

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '62a4d79b8249'
down_revision: Union[str, None] = '95a9d6e39e26'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_todos_userId'), 'todos', ['userId'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_todos_userId'), table_name='todos')
    # ### end Alembic commands ###
