"""migrate chat status to string

Revision ID: cea155c99d24
Revises: 62a4d79b8249
Create Date: 2025-06-30 20:38:42.443840

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'cea155c99d24'
down_revision: Union[str, None] = '62a4d79b8249'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chats', 'status',
               existing_type=postgresql.ENUM('active', 'closed', name='chat_status'),
               type_=sa.String(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chats', 'status',
               existing_type=sa.String(),
               type_=postgresql.ENUM('active', 'closed', name='chat_status'),
               existing_nullable=False)
    # ### end Alembic commands ###
