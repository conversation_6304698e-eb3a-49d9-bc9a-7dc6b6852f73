"""added an index

Revision ID: 6e0da1166c8f
Revises: 5bb84962bcca
Create Date: 2025-07-21 17:33:52.382588

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6e0da1166c8f'
down_revision: Union[str, None] = '5bb84962bcca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_messages_chatId'), 'messages', ['chatId'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_messages_chatId'), table_name='messages')
    # ### end Alembic commands ###
