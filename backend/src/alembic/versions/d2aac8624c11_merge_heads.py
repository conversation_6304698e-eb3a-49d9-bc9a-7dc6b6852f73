"""merge heads

Revision ID: d2aac8624c11
Revises: c35d8a574350, f1bb2680ea08
Create Date: 2025-07-02 14:21:34.116138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd2aac8624c11'
down_revision: Union[str, None] = ('c35d8a574350', 'f1bb2680ea08')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
