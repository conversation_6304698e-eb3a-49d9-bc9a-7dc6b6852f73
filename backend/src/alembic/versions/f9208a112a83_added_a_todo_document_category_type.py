"""added a todo document category type

Revision ID: f9208a112a83
Revises: 3613aa1eb589
Create Date: 2025-07-10 12:55:17.709216

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = 'f9208a112a83'
down_revision: Union[str, None] = '3613aa1eb589'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("ALTER TYPE documentcategorytype ADD VALUE 'todo'")
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
    # ### end Alembic commands ###
