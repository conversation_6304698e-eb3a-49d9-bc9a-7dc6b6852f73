"""todo status and source migration

Revision ID: ea0c10398ae6
Revises: 0f5201d518d0
Create Date: 2025-07-30 10:49:24.890483

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ea0c10398ae6'
down_revision: Union[str, None] = '0f5201d518d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('todos', sa.Column('source', sa.String(), nullable=True))
    op.add_column('todos', sa.Column('status', sa.String(), nullable=True))
    op.add_column('todos', sa.Column('firstShownAt', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('todos', 'firstShownAt')
    op.drop_column('todos', 'status')
    op.drop_column('todos', 'source')
    # ### end Alembic commands ###
