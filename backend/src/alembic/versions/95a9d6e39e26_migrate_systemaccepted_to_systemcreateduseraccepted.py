"""Migrate systemAccepted to systemCreatedUserAccepted

Revision ID: 95a9d6e39e26
Revises: b096a610e384
Create Date: 2025-06-30 20:24:14.310524

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '95a9d6e39e26'
down_revision: Union[str, None] = 'b096a610e384'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.execute("""
        UPDATE todos
        SET type = 'systemCreatedUserAccepted'
        WHERE type = 'systemAccepted'
    """)

def downgrade():
    op.execute("""
        UPDATE todos
        SET type = 'systemAccepted'
        WHERE type = 'systemCreatedUserAccepted'
    """)

