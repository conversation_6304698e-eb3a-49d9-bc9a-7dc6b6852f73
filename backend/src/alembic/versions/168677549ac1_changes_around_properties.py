"""changes around properties

Revision ID: 168677549ac1
Revises: dd05a0026af9
Create Date: 2024-12-23 11:54:17.047736

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '168677549ac1'
down_revision: Union[str, None] = 'dd05a0026af9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('postcode_locations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('postcode', sa.String(), nullable=False),
    sa.Column('latitude', sa.Numeric(precision=8, scale=6), nullable=False),
    sa.Column('longitude', sa.Numeric(precision=8, scale=6), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_postcode_locations_id'), 'postcode_locations', ['id'], unique=False)
    op.create_index(op.f('ix_postcode_locations_postcode'), 'postcode_locations', ['postcode'], unique=True)
    op.add_column('addresses', sa.Column('streetLine1', sa.String(), nullable=False, server_default=''))
    op.add_column('addresses', sa.Column('streetLine2', sa.String(), nullable=True))
    op.add_column('addresses', sa.Column('streetLine3', sa.String(), nullable=True))
    op.add_column('addresses', sa.Column('udprn', sa.BigInteger(), nullable=True))
    op.add_column('addresses', sa.Column('umprn', sa.BigInteger(), nullable=True))
    op.add_column('addresses', sa.Column('idealPostcodesOutput', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('addresses', 'uprn',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.alter_column('addresses', 'latitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=8, scale=6),
               existing_nullable=True)
    op.alter_column('addresses', 'longitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=8, scale=6),
               existing_nullable=True)
    op.drop_column('addresses', 'streetLine_1')
    op.drop_column('addresses', 'streetLine_2')
    op.alter_column('properties', 'type',
               existing_type=postgresql.ENUM('detached', 'semi_detached', 'terraced', 'flat', name='propertytype'),
               nullable=True)
    op.drop_constraint('user_types_name_key', 'user_types', type_='unique')
    op.create_index(op.f('ix_user_types_name'), 'user_types', ['name'], unique=True)
    op.alter_column('users_properties', 'relationType',
               existing_type=postgresql.ENUM('owner', 'tenant', 'guarantor', name='userpropertyrelationtype'),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users_properties', 'relationType',
               existing_type=postgresql.ENUM('owner', 'tenant', 'guarantor', name='userpropertyrelationtype'),
               nullable=False)
    op.drop_index(op.f('ix_user_types_name'), table_name='user_types')
    op.create_unique_constraint('user_types_name_key', 'user_types', ['name'])
    op.alter_column('properties', 'type',
               existing_type=postgresql.ENUM('detached', 'semi_detached', 'terraced', 'flat', name='propertytype'),
               nullable=False)
    op.add_column('addresses', sa.Column('streetLine_2', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('addresses', sa.Column('streetLine_1', sa.VARCHAR(), autoincrement=False, nullable=False, server_default=''))
    op.alter_column('addresses', 'longitude',
               existing_type=sa.Numeric(precision=8, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=True)
    op.alter_column('addresses', 'latitude',
               existing_type=sa.Numeric(precision=8, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=True)
    op.alter_column('addresses', 'uprn',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.drop_column('addresses', 'idealPostcodesOutput')
    op.drop_column('addresses', 'umprn')
    op.drop_column('addresses', 'udprn')
    op.drop_column('addresses', 'streetLine3')
    op.drop_column('addresses', 'streetLine2')
    op.drop_column('addresses', 'streetLine1')
    op.drop_index(op.f('ix_postcode_locations_postcode'), table_name='postcode_locations')
    op.drop_index(op.f('ix_postcode_locations_id'), table_name='postcode_locations')
    op.drop_table('postcode_locations')
    # ### end Alembic commands ###
