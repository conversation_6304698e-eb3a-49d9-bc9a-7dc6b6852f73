"""Add was processed as abandoned

Revision ID: f1bb2680ea08
Revises: c913f4afcc6d
Create Date: 2025-07-01 07:03:00.310962

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1bb2680ea08'
down_revision: Union[str, None] = 'c913f4afcc6d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chats', sa.Column('was_processed_as_abandoned', sa.<PERSON>(), nullable=True))
    op.create_index(op.f('ix_chats_was_processed_as_abandoned'), 'chats', ['was_processed_as_abandoned'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_chats_was_processed_as_abandoned'), table_name='chats')
    op.drop_column('chats', 'was_processed_as_abandoned')
    # ### end Alembic commands ###
