"""migrate quote status to string

Revision ID: c913f4afcc6d
Revises: da6532fcd57f
Create Date: 2025-07-01 07:00:15.074487

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c913f4afcc6d'
down_revision: Union[str, None] = 'da6532fcd57f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('quotes', 'status',
               existing_type=postgresql.ENUM('open', 'accepted', 'rejected', 'cancelled', name='quote_status'),
               type_=sa.String(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('quotes', 'status',
               existing_type=sa.String(),
               type_=postgresql.ENUM('open', 'accepted', 'rejected', 'cancelled', name='quote_status'),
               existing_nullable=False)
    # ### end Alembic commands ###
