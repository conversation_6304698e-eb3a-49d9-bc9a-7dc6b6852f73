"""Migrate todos type enum to str

Revision ID: b096a610e384
Revises: 6633a806d0bb
Create Date: 2025-06-30 20:21:46.897904

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b096a610e384'
down_revision: Union[str, None] = '6633a806d0bb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('todos', 'type',
               existing_type=postgresql.ENUM('userCreated', 'systemCreated', 'systemAccepted', 'systemCreatedUserAccepted', 'systemCreatedUserRejected', name='todotype'),
               type_=sa.String(),
               existing_nullable=False)
    op.create_index(op.f('ix_todos_type'), 'todos', ['type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_todos_type'), table_name='todos')
    op.alter_column('todos', 'type',
               existing_type=sa.String(),
               type_=postgresql.ENUM('userCreated', 'systemCreated', 'systemAccepted', 'systemCreatedUserAccepted', 'systemCreatedUserRejected', name='todotype'),
               existing_nullable=False)
    # ### end Alembic commands ###
