"""migrate job status to string

Revision ID: da6532fcd57f
Revises: 446fe672920e
Create Date: 2025-06-30 20:51:49.963867

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'da6532fcd57f'
down_revision: Union[str, None] = '446fe672920e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('jobs', 'status',
               existing_type=postgresql.ENUM('incomplete', 'created', 'user_accepted', 'quoting_started', 'quoting_completed', 'more_quotes_requested', 'booked', 'completed', 'disputed', 'paid', 'cancelled', name='job_status'),
               type_=sa.String(),
               existing_nullable=False)
    op.create_index(op.f('ix_jobs_status'), 'jobs', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_jobs_status'), table_name='jobs')
    op.alter_column('jobs', 'status',
               existing_type=sa.String(),
               type_=postgresql.ENUM('incomplete', 'created', 'user_accepted', 'quoting_started', 'quoting_completed', 'more_quotes_requested', 'booked', 'completed', 'disputed', 'paid', 'cancelled', name='job_status'),
               existing_nullable=False)
    # ### end Alembic commands ###
