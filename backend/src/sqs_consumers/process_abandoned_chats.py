import asyncio
import json

from src.dependencies import get_todos_for_abandoned_chats_service


todos_for_abandoned_chats_service = get_todos_for_abandoned_chats_service()


async def _process_message(message):
    print(f"Processing chat: {message}")
    # await todos_for_abandoned_chats_service.create_todos_for_abandoned_chat_id(chat_id=message["chat_id"])
    print(f"Chat processed {message}")


def handler(event, context):
    for record in event["Records"]:
        message = json.loads(record["body"])
        asyncio.run(_process_message(message))

    return {"statusCode": 200}
