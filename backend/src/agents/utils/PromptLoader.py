import os
import logging

logger = logging.getLogger(__name__)


class PromptLoader:

    def __init__(self, default_prompt_version=None):
        self.default_prompt_version = default_prompt_version
        self.logger = logging.getLogger("uvicorn")

    def load_system_prompt(self, prompt_version=None):
        if prompt_version is None:
            if self.default_prompt_version is None:
                raise ValueError("No prompt version specified and no default set")
            prompt_version = self.default_prompt_version

        # Try multiple possible paths to find the prompt file based on the ai-engine project structure
        current_file_dir = os.path.dirname(os.path.abspath(__file__))  # This will be where this class is located

        possible_paths = [
            f"prompts/{prompt_version}",  # Relative to current working directory
            f"../prompts/{prompt_version}",  # Relative to src directory
            f"/app/prompts/{prompt_version}",  # Docker/Fargate absolute path
            # For the structure ai-engine/prompts and ai-engine/src/agents or src/utils
            os.path.join(os.path.dirname(os.path.dirname(current_file_dir)), "prompts", prompt_version),
            # Additional paths based on different working directory possibilities
            os.path.join(os.getcwd(), "prompts", prompt_version),
            os.path.join(os.getcwd(), "ai-engine", "prompts", prompt_version),
            # Environment variable options
            os.path.join(os.environ.get("PROMPT_DIR", ""), prompt_version),
        ]

        # Add environment variable for complete custom path if needed
        env_prompt_path = os.environ.get("DIAGNOSTIC_AGENT_PROMPT_PATH")
        if env_prompt_path:
            possible_paths.insert(0, env_prompt_path)

        # Log the current directory and paths for debugging
        logger.info(f"Current working directory: {os.getcwd()}")
        logger.info(f"Module directory: {current_file_dir}")
        logger.info(f"Possible prompt paths: {possible_paths}")

        for prompt_file_path in possible_paths:
            try:
                logger.info(f"Attempting to load system prompt from: {prompt_file_path}")
                with open(prompt_file_path, "r", encoding="utf-8") as file:
                    content = file.read()
                    logger.info(f"Successfully loaded system prompt from: {prompt_file_path}")
                    return content
            except FileNotFoundError:
                logger.debug(f"System prompt file not found at: {prompt_file_path}")
                continue

        raise FileNotFoundError(
            f"Could not find prompt file for version: {prompt_version} in any of the checked locations")
