import pytest
import json
from datetime import datetime
from unittest.mock import Mo<PERSON>, Async<PERSON>ock, patch

from src.agents.ContextualPromptsAgent import ContextualPromptAgent, ContextualPrompts
from src.agents.rag.rag_context import RAGContext
from src.db_models.contextual_prompt import ContextualPromptType
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User


@pytest.fixture
def mock_services():
    """Create mock services for the ContextualPromptAgent"""
    return {
        "job_service": <PERSON><PERSON>(),
        "appliance_service": <PERSON><PERSON>(),
        "property_service": <PERSON><PERSON>(),
        "qdrant_dao": <PERSON><PERSON>(),
        "gemini_client": <PERSON><PERSON>(),
        "contextual_prompts_service": <PERSON><PERSON>(),
    }


@pytest.fixture
def contextual_prompt_agent(mock_services):
    """Create a ContextualPromptAgent with mocked dependencies"""
    agent = ContextualPromptAgent(
        job_service=mock_services["job_service"],
        appliance_service=mock_services["appliance_service"],
        property_service=mock_services["property_service"],
        qdrant_dao=mock_services["qdrant_dao"],
        gemini_client=mock_services["gemini_client"],
        contextual_prompts_service=mock_services["contextual_prompts_service"],
    )
    return agent


@pytest.fixture
def sample_todo():
    """Create a sample todo for testing"""
    return Todo(
        id=123,
        name="Service my boiler",
        description="Annual boiler service needed",
        dueDate=datetime(2024, 12, 31),
        userId=1,
        type=TodoType.userCreated,
    )


@pytest.fixture
def sample_user():
    """Create a sample user for testing"""
    return User(id=1, email="<EMAIL>", clerkId="test_clerk_id")


@pytest.mark.asyncio
async def test_generate_contextual_prompts_with_appliances(
    contextual_prompt_agent, mock_services, sample_todo, sample_user
):
    """Test that contextual prompts are generated successfully when appliances are found"""

    # Mock the DataRetrievalService responses via its internal methods
    with patch.object(
        contextual_prompt_agent.data_retrieval_service,
        "get_appliances_data",
        return_value=[{"id": 3, "name": "Boiler", "type": "boiler"}],
    ):
        with patch.object(contextual_prompt_agent.data_retrieval_service, "get_jobs_data", return_value=[]):
            with patch.object(
                contextual_prompt_agent.data_retrieval_service,
                "get_property_data",
                return_value={"address": "123 Test St", "type": "house"},
            ):

                # Mock the contextual prompts service
                mock_services["contextual_prompts_service"].delete_contextual_prompts_for_todo = AsyncMock()
                mock_services["contextual_prompts_service"].create_contextual_prompts_with_appliance_link = AsyncMock()

                # Mock the Gemini client response
                mock_contextual_prompts = ContextualPrompts(
                    prompts=[
                        "Ready to book boiler service?",
                        "Confirm job details for engineer?",
                        "Find Gas Safe registered engineer?",
                    ],
                    appliance_id=3,
                )

                with patch.object(
                    contextual_prompt_agent, "_generate_contextual_prompt", return_value=mock_contextual_prompts
                ):
                    # Execute the method
                    await contextual_prompt_agent.generate_contextual_prompts(sample_user.id, sample_todo)

                    # Verify contextual prompts service calls
                    mock_services[
                        "contextual_prompts_service"
                    ].delete_contextual_prompts_for_todo.assert_called_once_with(sample_todo.id, sample_user.id)
                    mock_services["contextual_prompts_service"].create_contextual_prompts_with_appliance_link.assert_called_once_with(
                        mock_contextual_prompts.prompts, 
                        sample_todo.id, 
                        ContextualPromptType.todoPrompt, 
                        sample_user.id,
                        mock_contextual_prompts.appliance_id
                    )


@pytest.mark.asyncio
async def test_generate_contextual_prompt_with_rag_context(contextual_prompt_agent, mock_services, sample_todo):
    """Test the internal _generate_contextual_prompt method with RAG context"""

    # Create RAG context with sample data
    rag_context = RAGContext(
        appliances=[{"id": 3, "name": "Boiler", "type": "boiler"}],
        jobs=[{"id": 456, "title": "Previous boiler repair", "status": "completed"}],
        property_info={"address": "123 Test St", "type": "house"},
        documents=[],
        chat_history=[],
    )

    # Mock Gemini client response
    mock_response_content = json.dumps(
        {
            "prompts": [
                "Ready to book boiler service?",
                "Need Gas Safe engineer details?",
                "Schedule maintenance appointment?",
            ],
            "appliance_id": 3,
        }
    )

    mock_services["gemini_client"].process_text_prompt_gemini_flash = AsyncMock(return_value=mock_response_content)

    # Execute the method
    result = await contextual_prompt_agent._generate_contextual_prompt(rag_context, sample_todo)

    # Verify the result
    assert isinstance(result, ContextualPrompts)
    assert len(result.prompts) == 3
    assert result.appliance_id == 3
    assert "Ready to book boiler service?" in result.prompts

    # Verify Gemini client was called
    mock_services["gemini_client"].process_text_prompt_gemini_flash.assert_called_once()

    # Check that the prompt contains the expected context
    call_args = mock_services["gemini_client"].process_text_prompt_gemini_flash.call_args[0][0]
    assert str(sample_todo.id) in call_args
    assert sample_todo.name in call_args
    assert "APPLIANCES:" in call_args
    assert "RECENT JOBS:" in call_args
    assert "PROPERTY:" in call_args


@pytest.mark.asyncio
async def test_generate_contextual_prompt_gemini_error_handling(contextual_prompt_agent, mock_services, sample_todo):
    """Test that errors from Gemini client are handled gracefully"""

    # Create minimal RAG context
    rag_context = RAGContext(appliances=[], jobs=[], property_info={}, documents=[], chat_history=[])

    # Mock Gemini client to raise an exception
    mock_services["gemini_client"].process_text_prompt_gemini_flash = AsyncMock(
        side_effect=Exception("Gemini API error")
    )

    # Execute the method
    result = await contextual_prompt_agent._generate_contextual_prompt(rag_context, sample_todo)

    # Verify fallback behavior - should return default prompts
    assert isinstance(result, ContextualPrompts)
    assert result.prompts == [
                "Need help with this task",
                "Share more details",
                "Find qualified local professionals"
            ]
    assert result.appliance_id is None
