import asyncio
import json
import logging
from typing import List, Union, Async<PERSON>enerator, Callable, Pattern, Awaitable, Dict, Any

from src.ai_schemas import ImageUrl


# from langchain_together import Together


class Tool:

    def __init__(self, name: str, handler: Callable[..., Awaitable[str]]):
        """
        Args:
            name (str): The name of the tool, must match what the LLM is trained to call.
            handler (Callable): An async function that executes the tool's logic and returns a context string.
        """
        self.name = name
        self.handler = handler


class Command:
    def __init__(self, pattern: Pattern[str], handler: Callable[[str], Union[asyncio.Task, None]]):
        self.pattern = pattern
        self.handler = handler


class StreamingChunkHandler:
    """Handles raw text chunks from an LLM streamprocesses inline commands and yields content."""

    def __init__(self, commands: list[Command], tools: List[Tool] = None):
        self._commands = commands
        self._tools: Dict[str, Tool] = {tool.name: tool for tool in tools} if tools else {}
        self.logger = logging.getLogger("uvicorn")
        self._buffer: str = ""
        self._pending_tasks: List[asyncio.Task] = []
        self.full_response_for_history: str = ""
        self.accumulated_text_for_processing: str = ""
        self.accumulated_image_urls: List[ImageUrl] = []
        self.accumulated_clickable_urls: List[ImageUrl] = []
        self.job_summary = None
        self._detected_tool_calls: List[Dict[str, Any]] = []

    async def execute_tool_calls(self, tool_calls: List[Dict[str, Any]], **kwargs) -> str:
        """
        Concurrently executes a list of tool calls from the LLM.

        Args:
            tool_calls (List[Dict]): The list of tool calls from the LangChain chunk.
            **kwargs: Additional context (like user_id, chat_id) to pass to tool handlers.

        Returns:
            str: A consolidated string of all tool results to be used as enhanced context.
        """
        tasks = []
        for call in tool_calls:
            tool_name = call.get("name")
            if tool_name in self._tools:
                tool = self._tools[tool_name]
                # Pass both the llms args and the contextual kwargs to the handler
                combined_args = {**call.get("args", {}), **kwargs}
                tasks.append(asyncio.create_task(tool.handler(**combined_args)))
                self.logger.info(f"Dispatching tool call: {tool_name} with args: {combined_args}")
            else:
                self.logger.warning(f"LLM tried to call unknown tool: {tool_name}")

        if not tasks:
            return ""

        results = await asyncio.gather(*tasks, return_exceptions=True)

        enhanced_context_parts = []
        for result in results:
            if isinstance(result, Exception):
                self.logger.exception(f"Tool execution failed: {result}")
                # Optionally add an error message to the context
                enhanced_context_parts.append(f"Context retrieval failed for a tool: {result}")
            elif isinstance(result, str):
                enhanced_context_parts.append(result)

        return "\n\n".join(enhanced_context_parts)

    @staticmethod
    def _detect_incomplete_token_at_end(buffer: str) -> int:
        incomplete_token_start = buffer.rfind("[[")
        closing_mark = "]]"
        if incomplete_token_start == -1:
            incomplete_token_start = buffer.rfind("[")
            closing_mark = "]"
        is_incomplete = incomplete_token_start != -1 and buffer.find(closing_mark, incomplete_token_start) == -1
        return incomplete_token_start if is_incomplete else -1

    @staticmethod
    def get_content(obj):
        """Extracts content from a LangChain LLM chunk object."""
        content = getattr(obj, "content", "")
        if isinstance(content, str):
            return content
        if isinstance(content, list):
            return "".join(b.get("text", "") for b in content if isinstance(b, dict) and b.get("type") == "text")
        return ""

    async def _process_text_and_commands_chunk(self, chunk_text: str) -> AsyncGenerator[str, None]:
        """Processes raw text chunk for inline commands and yields content."""
        self.full_response_for_history += chunk_text  # This was part of original handle, now takes chunk_text
        self._buffer += chunk_text
        while True:
            first_match, first_command = None, None
            matches = [
                (cmd.pattern.search(self._buffer), cmd) for cmd in self._commands if cmd.pattern.search(self._buffer)
            ]
            if matches:
                matches.sort(key=lambda m: m[0].start())
                first_match, first_command = matches[0]

            if first_match and first_command:
                start_index, end_index = first_match.span()
                text_before = self._buffer[:start_index]
                if text_before:
                    self.accumulated_text_for_processing += text_before
                    yield f"data: {json.dumps({'type': 'content', 'data': text_before})}\n\n"

                is_job_summary = "jobSummary" in first_command.pattern.pattern

                # Correctly handle jobSummary and other commands
                if is_job_summary:
                    # For jobSummary, the content is in group 0 of its specific regex
                    command_content = first_match.group(0).strip()
                    # Store the extracted summary on the handler instance
                    self.job_summary = command_content
                else:
                    # For all other commands, assume content is in group 1
                    command_content = first_match.group(1).strip()

                self.logger.info(
                    f"Detected command: Pattern='{first_command.pattern.pattern}', Content='{command_content}'"
                )
                task = first_command.handler(command_content)
                if task:
                    self._pending_tasks.append(task)
                self._buffer = self._buffer[end_index:]
                continue
            else:
                break

        split_index = self._detect_incomplete_token_at_end(self._buffer)
        text_to_yield, self._buffer = (
            (self._buffer[:split_index], self._buffer[split_index:]) if split_index != -1 else (self._buffer, "")
        )
        if text_to_yield:
            self.accumulated_text_for_processing += text_to_yield
            yield f"data: {json.dumps({'type': 'content', 'data': text_to_yield})}\n\n"

    async def process_llm_chunk(self, llm_chunk: Any) -> AsyncGenerator[str, None]:
        """
        Processes a raw LLM chunk, accumulating tool calls and yielding content.
        This is the main entry point for chunks from the LLM stream.
        """
        if hasattr(llm_chunk, "tool_calls") and llm_chunk.tool_calls:
            self._detected_tool_calls.extend(llm_chunk.tool_calls)
            self.logger.info(f"Accumulated tool calls: {llm_chunk.tool_calls}")

        chunk_text = self.get_content(llm_chunk)
        if chunk_text:
            async for event in self._process_text_and_commands_chunk(chunk_text):
                yield event

    def get_detected_tool_calls(self) -> List[Dict[str, Any]]:
        """Returns the tool calls detected so far."""
        return self._detected_tool_calls

    async def finalize(self) -> AsyncGenerator[str, None]:
        if self._buffer:
            self.logger.warning(f"Yielding remaining buffer during finalization: {self._buffer}")
            self.accumulated_text_for_processing += self._buffer
            yield f"data: {json.dumps({'type': 'content', 'data': self._buffer})}\n\n"

        if self._pending_tasks:
            yield f"data: {json.dumps({'type': 'content',
                                       'data': '\n\nPlease wait a few seconds, I\'m looking for images...'})}\n\n"
            results = await asyncio.gather(*self._pending_tasks, return_exceptions=True)
            fallback_strings = []
            for res in results:
                if isinstance(res, Exception):
                    self.logger.exception(f"Async task failed: {res}")
                elif isinstance(res, tuple) and len(res) == 2 and isinstance(res[1], list):
                    self.accumulated_image_urls.extend(res[1])
                elif isinstance(res, list):
                    self.accumulated_clickable_urls.extend(res)
                elif isinstance(res, str):
                    fallback_strings.append(res)

            if fallback_strings:
                for s in fallback_strings:
                    yield f"data: {json.dumps({'type': 'content', 'data': '\n' + s})}\n\n"
