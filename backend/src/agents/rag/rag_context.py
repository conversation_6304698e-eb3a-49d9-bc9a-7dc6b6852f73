
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

class RAGContext(BaseModel):
    """Context information from RAG searches"""

    appliances: List[Dict[str, Any]] = Field(default_factory=list, description="User's appliances information")
    jobs: List[Dict[str, Any]] = Field(default_factory=list, description="Previous jobs/repairs")
    property_info: Optional[Dict[str, Any]] = Field(default=None, description="Property general information")
    relationship_to_property_info: Optional[Dict[str, Any]] = Field(
        default=None, description="User's relationship to the Property"
    )
    documents: List[Dict[str, Any]] = Field(default_factory=list, description="Relevant documents")
    chat_history: List[Dict[str, Any]] = Field(default_factory=list, description="Relevant past conversations")
    summary: str = Field(default="", description="Contextual summary for the agent")
