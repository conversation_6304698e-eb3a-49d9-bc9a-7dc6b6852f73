import asyncio
import logging
import os
from typing import List

import google.generativeai as genai
from langchain_core.tools import tool

from src.agents.rag.rag_context import RAGContext
from src.ai_dao.QdrantDAO import QdrantDAO
from src.services.ai_appliances import ApplianceService
from src.services.ai_jobs import JobService
from src.services.ai_property import PropertyService
from src.services.data_retrieval import DataRetrievalService


class RAGAgent:
    def __init__(
        self,
        job_service: JobService,
        appliance_service: ApplianceService,
        property_service: PropertyService,
        qdrant_dao: QdrantDAO,
    ):
        self.logger = logging.getLogger("uvicorn")
        self.job_service = job_service
        self.appliance_service = appliance_service
        self.property_service = property_service
        self.qdrant_dao = qdrant_dao
        
        # Initialize the data retrieval service
        self.data_retrieval_service = DataRetrievalService(
            job_service=job_service,
            appliance_service=appliance_service,
            property_service=property_service,
            qdrant_dao=qdrant_dao,
        )

        self.gemini_api_key = os.environ.get("GEMINI_API_KEY")
        if not self.gemini_api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")

        genai.configure(api_key=self.gemini_api_key)
        self.gemini_model = genai.GenerativeModel("gemini-2.5-flash")

    async def search_and_summarize(self, user_id: int, context_messages: List[str], first_message: str) -> RAGContext:
        """
        Main method to search all data sources and return summarized context

        Args:
            user_id: The user ID (set by system, not by user)
            context_messages: Last 5 messages from conversation
            first_message: users first message

        Returns:
            RAGContext with relevant information and summary
        """
        try:
            self.logger.info(f"RAGAgent.search_and_summarize() for user_id={user_id}")

            # Prepare search query from context
            search_query = await self._prepare_search_query(context_messages)
            self.logger.info(f"RAGAgent search query: {search_query}")

            # Gather data from all sources using the data retrieval service
            # Gather data from all sources using the data retrieval service
            appliances_data = await self.data_retrieval_service.get_appliances_data(user_id)
            jobs_data = await self.data_retrieval_service.get_jobs_data(user_id)
            property_data = await self.data_retrieval_service.get_property_data(user_id)
            documents_data = await self.data_retrieval_service.get_documents_data(user_id, search_query)
            chat_history_data = await self.data_retrieval_service.get_chat_history_data(user_id, search_query)

            # Create RAG context
            rag_context = RAGContext(
                appliances=appliances_data,
                jobs=jobs_data,
                property_info=property_data,
                documents=documents_data,
                chat_history=chat_history_data,
            )

            # Generate contextual summary using Gemini
            summary = await self._generate_contextual_summary(rag_context, context_messages, first_message)
            rag_context.summary = summary

            self.logger.info(f"RAGAgent completed search, summary length: {len(summary)}")
            self.logger.info(f"RAGAgent completed search, summary: {summary}")
            return rag_context

        except Exception as e:
            self.logger.exception(f"Error in RAGAgent.search_and_summarize: {str(e)}")
            # Return empty context instead of failing
            return RAGContext(summary="Unable to retrieve context information")

    async def _prepare_search_query(self, context_messages: List[str]) -> str:
        """Extract and optimize search query from conversation context using Gemini"""
        if not context_messages:
            return ""

        try:
            # Join the context messages for analysis
            conversation_context = "\n".join([f"Message {i + 1}: {msg}" for i, msg in enumerate(context_messages)])

            prompt = f"""
            You are a search query optimizer for a property maintenance system. Based on the conversation context below, 
            generate the most effective search query for retrieving relevant documents from a vector database.

            The vector database contains these types of documents:
            - Property manuals and warranties (appliance manuals, boiler manuals, HVAC guides)
            - Maintenance guides and repair instructions
            - Property certificates (gas safety, electrical, EPC certificates)
            - Insurance documents and claims
            - Property surveys and inspection reports
            - Bills and invoices (utility bills, repair bills, service invoices)
            - Property legal documents (tenancy agreements, property deeds)
            - Chat history summaries of previous conversations about property issues

            CONVERSATION CONTEXT:
            {conversation_context}

            Generate a search query that will find the most relevant documents. Focus on:
            - Key appliances or systems mentioned
            - Specific problems or issues described
            - Types of documents that would be helpful
            - Technical terms and brands mentioned

            Return only the optimized search query (maximum 10-15 words), nothing else.

            Examples:
            - "What appliances do I have?" -> "appliances brands models warranties"
            - "My boiler is making noise" -> "boiler noise troubleshooting manual repair"
            - "When was my gas certificate renewed?" -> "gas safety certificate renewal date"
            - "How do I fix my washing machine?" -> "washing machine repair manual troubleshooting"
            """

            response = await self.gemini_model.generate_content_async(
                prompt,
                generation_config={
                    "temperature": 0.2,  # Lower temperature for more consistent results
                    "max_output_tokens": 5000,
                    "top_p": 0.8,
                },
            )

            optimized_query = response.text.strip()

            # Fallback to original logic if Gemini fails or returns empty response
            if not optimized_query or len(optimized_query) < 3:
                self.logger.warning("Gemini returned empty/short query, using fallback")
                return context_messages[-1][:200] if context_messages else ""

            self.logger.info(f"Original query: {context_messages[-1][:100]}...")
            self.logger.info(f"Optimized query: {optimized_query}")

            return optimized_query

        except Exception as e:
            self.logger.exception(f"Error optimizing search query with Gemini: {str(e)}")
            # Fallback to original simple logic
            return context_messages[-1][:200] if context_messages else ""






    async def _generate_contextual_summary(self, rag_context: RAGContext, context_messages: List[str], first_message: str) -> str:
        try:
            # Prepare prompt for Gemini
            conversation_context = "\n".join([f"Message: {msg}" for msg in context_messages[-3:]])

            data_summary = []

            if rag_context.appliances:
                data_summary.append(f"APPLIANCES: {rag_context.appliances}")

            if rag_context.jobs:
                data_summary.append(f"RECENT JOBS: {rag_context.jobs}")

            if rag_context.property_info:
                data_summary.append(f"PROPERTY: {rag_context.property_info}")

            if rag_context.documents:
                # Include actual document content instead of just count
                doc_contents = []
                for doc in rag_context.documents[:7]:  # Limit to top 7 documents to avoid token overflow
                    file_key = doc.get("fileKey", "Unknown file")
                    content = doc.get("content", "").strip()
                    score = doc.get("score", 0)
                    # Truncate content if too long to avoid token limits
                    # if len(content) > 500:
                    #     content = content[:500] + "..."
                    doc_contents.append(f"[{file_key}] (Score: {score:.2f}): {content}")

                doc_summary = f"Found {len(rag_context.documents)} relevant documents:\n" + "\n".join(doc_contents)
                data_summary.append(f"DOCUMENTS: {doc_summary}")

            if rag_context.chat_history:
                # Include actual chat history content
                chat_contents = []
                for chat in rag_context.chat_history[:2]:  # Limit to top 2 chats
                    chat_summary = chat.get("chatSummary", "").strip()
                    score = chat.get("score", 0)
                    # Truncate if too long
                    # if len(chat_summary) > 300:
                    #     chat_summary = chat_summary[:300] + "..."
                    chat_contents.append(f"(Score: {score:.2f}): {chat_summary}")

                chat_summary = f"Found {len(rag_context.chat_history)} similar past conversations:\n" + "\n".join(
                    chat_contents
                )
                data_summary.append(f"PAST CHATS: {chat_summary}")

            prompt = f"""
            Based on the conversation context and available user data, provide a concise summary (max 1000 words but could be less) 
            of the most relevant information that would help answer the user's current inquiry.

            USER FIRST MESSAGE:
            {first_message}
            
            CONVERSATION CONTEXT:
            {conversation_context}
            
            CONVERSATION CONTEXT END

            Please treat APPLIANCES, JOBS, PROPERTY section as more important as these are retrieved from the database. 
            Especially if you have some conflicts with retrieved documents treat APPLIANCES, JOBS, PROPERTY as source of truth.
            
            Note the user's relationship to the property (e.g., 'owner', 'tenant') if available in the PROPERTY section,
            as this might influence how you answer or provide advice.
            
            IMPORTANT! User relationship to property is important in context of fixes and modification of the property. For example user needs some permissions if he is a tenant

            AVAILABLE DATA:
            {chr(10).join(data_summary)}

            Focus on information that directly relates to the user's current question or issue. 
            Be specific about appliances, property details, or past issues that might be relevant.
            If no relevant information is found, state that clearly.
            """

            self.logger.info(f"=====>RAGAgent: data for context creation: {prompt}")

            response = await self.gemini_model.generate_content_async(
                prompt, generation_config={"temperature": 0.3, "max_output_tokens": 200000}
            )

            return_value = response.text.strip() + f"\nUSERS PROPERTY DETAILS: {rag_context.property_info}"

            return return_value

        except Exception as e:
            self.logger.exception(f"Error generating contextual summary: {str(e)}")
            return "Context information retrieved but unable to generate summary."


# Create the LangChain tool
@tool
async def search_property_context(context_messages: List[str], first_message: str) -> str:
    """
    Search and retrieve relevant context about the user's property, appliances, past jobs, and documents.

    Use this tool when you need information about:
    - User's appliances (types, brands, models)
    - Past repairs or maintenance (previous jobs/chats)
    - Property details (number of rooms, property type, user's relationship to property, etc.)
    - Property documents (gas certificates, manuals, bills, insurance, surveys, etc.)

    Args:
        context_messages: Last few messages from the conversation for context
        first_message: Users first message - often gives the idea of the main topic

    Returns:
        Contextual summary of relevant property and maintenance information
    """
    # This will be injected by the DiagnosticAgentStreaming
    # The actual implementation will be bound when the tool is used
    return "Tool not properly initialized"


def create_rag_tool(rag_agent: RAGAgent, user_id: int):
    """Create a bound RAG tool with user_id set"""

    async def bound_search_property_context(context_messages: List[str], first_message: str) -> str:
        """Bound version of the search tool with user_id"""
        rag_context = await rag_agent.search_and_summarize(user_id, context_messages, first_message)
        return rag_context.summary

    # Update the tool function
    bound_search_property_context.__name__ = "search_property_context"
    bound_search_property_context.__doc__ = search_property_context.__doc__

    return tool(bound_search_property_context)
