import logging
import unittest

from src.agents.JobSummaryExtractor import JobSummaryExtractor
from src.ai_schemas import JobSummary


class TestJobSummaryExtractor(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.extractor = JobSummaryExtractor()
        # Set up a real logger for testing with console output
        self.extractor.logger = logging.getLogger("test_job_summary_extractor")
        self.extractor.logger.setLevel(logging.INFO)

        # Add console handler if it doesn't exist
        if not self.extractor.logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter("%(name)s - %(levelname)s - %(message)s")
            console_handler.setFormatter(formatter)
            self.extractor.logger.addHandler(console_handler)

    def test_extract_job_summary_original_format(self):
        """Test extraction with the original [[jobSummary = {...}]] format."""
        response = """[[
jobSummary = {
  jobHeadline: "Leaking Kitchen Tap",
  jobSubTitle: "Kitchen Mixer Tap Leaking from Base",
  jobDetails: "Kitchen mixer tap (2-5 years old) is constantly leaking from the base even when turned off. This suggests worn internal components such as washers or O-rings that need replacement. The leak is persistent and requires professional attention.",
  jobDate: "Urgent (within 24-48 hours)",
  jobTimeOfDay: "Weekdays after 6 PM"
}
]]"""

        processed_response, job_summary = self.extractor.extract_job_summary(response)

        import sys

        print(f"processed_response={processed_response}", file=sys.stderr)
        # Verify the job summary block was removed
        self.assertNotIn("jobSummary", processed_response)
        # self.assertIn("Here's some response text before", processed_response.strip())
        #
        # # Verify job summary was extracted correctly
        self.assertIsNotNone(job_summary)
        self.assertIsInstance(job_summary, JobSummary)
        self.assertEqual(job_summary.jobHeadline, "Leaking Kitchen Tap")
        self.assertEqual(job_summary.jobSubTitle, "Kitchen Mixer Tap Leaking from Base")
        self.assertEqual(job_summary.jobDate, "Urgent (within 24-48 hours)")
        self.assertEqual(job_summary.jobTimeOfDay, "Weekdays after 6 PM")
        self.assertIn("Kitchen mixer tap", job_summary.jobDetails)

    def test_extract_job_summary_special_chars(self):
        """Test extraction with the original [[jobSummary = {...}]] format."""
        response = """[[
jobSummary = {
        "jobHeadline": "Air Conditioning Installation - Whole Flat Cooling",
        "jobSubTitle": "Multi-Split A/C System for 1-Bedroom Flat with Balcony",
        "jobDetails": "Seeking qualified A/C installers to quote for a multi-split air conditioning system for a 490 sq ft, 1-bedroom flat on the 4th floor of Tudor Court, SE16. Property layout includes: Reception Room (13\'0" x 19\'8"), Bedroom (8\'8" x 10\'3"), Kitchen (5\'6" x 7\'4"), and balcony for outdoor unit placement. Tenant requires whole-flat cooling solution with indoor units in reception room and bedroom, and outdoor unit positioned on balcony. Property falls under Building Safety Act (2022) considerations. Floorplan available. Landlord permission will be sought once quotes are received. Professional assessment and detailed quotation required including all installation costs, warranties, and building compliance requirements.",
        "jobDate": "Flexible timing",
        "jobTimeOfDay": "Evenings after 5pm"}
]]"""

        processed_response, job_summary = self.extractor.extract_job_summary(response)

        import sys

        print(f"processed_response={processed_response}", file=sys.stderr)
        # Verify the job summary block was removed
        self.assertNotIn("jobSummary", processed_response)
        # self.assertIn("Here's some response text before", processed_response.strip())
        #
        # # Verify job summary was extracted correctly
        self.assertIsNotNone(job_summary)
        self.assertIsInstance(job_summary, JobSummary)
        self.assertEqual(job_summary.jobHeadline, "Air Conditioning Installation - Whole Flat Cooling")
        self.assertEqual(job_summary.jobSubTitle, "Multi-Split A/C System for 1-Bedroom Flat with Balcony")
        self.assertEqual(job_summary.jobDate, "Flexible timing")
        self.assertEqual(job_summary.jobTimeOfDay, "Evenings after 5pm")
        self.assertIn("Seeking qualified A/C installers", job_summary.jobDetails)
