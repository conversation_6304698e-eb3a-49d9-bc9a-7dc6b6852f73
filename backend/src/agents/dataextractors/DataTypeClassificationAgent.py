from google import genai
from google.genai import types
import os
import logging
from pathlib import Path
from typing import List

from src.services.DocumentSummarizationService import DocumentSummarizationService


class DataTypeClassificationAgent:
    def __init__(self):
        self.logger = logging.getLogger("uvicorn")

        if "GEMINI_API_KEY" not in os.environ:
            raise KeyError("GEMINI_API_KEY environment variable is required")
        self.genai_client = genai.Client()

        self.document_service = DocumentSummarizationService()

        # available data tables
        self.data_tables = [
            "Property",
            "Insurance",
            "Bills",
            "Appliances",
            "Legal",
            "Building",
            "Neighbourhood Info"
        ]

        self.classification_prompt = """
            Based on the provided content, determine which of the following data tables are relevant:
            - Property: Details about a property including address, type, size, features, condition, etc.
            - Insurance: Details about insurance policies including type, provider, policy number, coverage, etc.
            - Bills: Information about utility bills and subscriptions including provider, account number, costs, etc.
            - Appliances: Details about household appliances including type, brand, model, warranty, etc.
            - Legal: Legal documents related to property ownership, tenancy, etc.
            - Building: Information about the building or development including name, management, facilities, etc.
            - Neighbourhood Info: Details about the neighborhood including affluence, crime, demographics, etc.
    
            If none of these tables are relevant, respond with "NOT_RELEVANT".
    
            Respond with a JSON array of relevant table names or "NOT_RELEVANT", for example:
            ["Property", "Building"] or ["NOT_RELEVANT"]
    
            Content for classification: 
            
            
        """

    async def classify_file(self, file_path: Path, content_of_file: str) -> List[str]:
        try:
            self.logger.info("DataTypeClassificationAgent.classify_file():")

            response = await self.genai_client.aio.models.generate_content(
                model="gemini-2.5-flash",
                contents=self.classification_prompt + content_of_file,
                config=types.GenerateContentConfig(
                    temperature=0.1,
                    top_p=0.9,
                    max_output_tokens=200000,
                )
            )

            self.logger.info(f"DataTypeClassificationAgent.classify_file() LLM response : {response}")
            self.logger.info(f"DataTypeClassificationAgent.classify_file() LLM response.text : {response.text}")
            result = self._parse_classification_response(response.text)
            self.logger.info(f"File classification result: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Error classifying file {file_path}: {str(e)}")
            raise

    async def classify_message(self, message: str) -> List[str]:
        """Classify a chat message to determine relevant data tables."""
        try:
            self.logger.info("Classifying chat message")

            # Use Gemini to classify the message
            response = await self.genai_client.aio.models.generate_content(
                model="gemini-2.5-flash",
                contents=self.classification_prompt + message,
                config=types.GenerateContentConfig(
                    temperature=0.1,
                    top_p=0.9,
                    max_output_tokens=200000,
                )
            )

            self.logger.info(f"DataTypeClassificationAgent.classify_file() LLM response : {response.text}")
            result = self._parse_classification_response(response.text)
            self.logger.info(f"Message classification result: {result}")
            return result

        except Exception as e:
            self.logger.exception(f"Error classifying message: {str(e)}")
            raise

    def _parse_classification_response(self, response: str) -> List[str]:
        import json
        import re

        try:
            if "NOT_RELEVANT" in response:
                return ["NOT_RELEVANT"]

            match = re.search(r'\[.*?\]', response, re.DOTALL)
            if match:
                result = json.loads(match.group(0))

                if isinstance(result, list):
                    valid_tables = []
                    for item in result:
                        if item == "NOT_RELEVANT":
                            return ["NOT_RELEVANT"]
                        if item in self.data_tables:
                            valid_tables.append(item)

                    return valid_tables if valid_tables else ["NOT_RELEVANT"]

            return ["NOT_RELEVANT"]

        except Exception as e:
            self.logger.exception(f"Error parsing classification response: {str(e)}")
            return ["NOT_RELEVANT"]
