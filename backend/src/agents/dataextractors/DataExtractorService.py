import json
import logging
from pathlib import Path

from src.agents.dataextractors.AppliancesManager import AppliancesManager
from src.agents.dataextractors.DataExtractorAgent import DataExtractorAgent
from src.agents.dataextractors.DataTypeClassificationAgent import DataTypeClassificationAgent
from src.agents.dataextractors.DocumentLabelingAgent import DocumentLabellingAgent
from src.agents.dataextractors.FilesChunkingAgent import FilesChunkingAgent
from src.agents.dataextractors.PropertyDetailsManager import PropertyDetailsManager
from src.ai_dao import QdrantDAO
from src.ai_schemas import RelevantDocumentClassification, NotRelevantDocumentClassification
from src.integrations.backend import (
    BackendAPI,
    UpdateDocumentStatusRequest,
    CreateApplianceRequest,
    UpdateApplianceRequest,
    ApplianceData,
    UpdatePropertyDetailsRequest,
    PropertyDetailsData,
)
from src.services.DocumentSummarizationService import DocumentSummarizationService
from src.services.ai_property import PropertyService
from src.services.appliances import ApplianceService


class DataExtractorService:
    def __init__(
            self,
            backend_api: BackendAPI,
            appliance_service: ApplianceService,
            qdrant_dao: QdrantDAO,
            property_service: PropertyService = None
    ):
        self.logger = logging.getLogger("uvicorn")

        self._backend_api: BackendAPI = backend_api
        self._appliance_service: ApplianceService = appliance_service
        self._property_service: PropertyService = property_service
        self.document_labeling_agent = DocumentLabellingAgent()
        self.file_chunking_agent = FilesChunkingAgent(qdrant_dao=qdrant_dao)
        self.classification_agent = DataTypeClassificationAgent()
        self.extraction_agent = DataExtractorAgent()
        self.document_summarization_service = DocumentSummarizationService()
        self.appliances_manager = AppliancesManager()
        self.property_details_manager = PropertyDetailsManager()
        self.category_mapping = {
            "Property": "propertyDetails",
            "Insurance": "insurance",
            "Bills": "billsAndSubscriptions",
            "Legal": "legal",
            "Appliances": "appliance",
            "Inventory Records": "propertyDetails",
            "Other": "other",
        }

    async def process_file(self, input_data: Path, document_id: int, user_id: int, bucket_name: str, file_key: str):
        content = None

        try:
            await self._backend_api.update_document_status(
                document_id, UpdateDocumentStatusRequest(status="processing")
            )
            category_and_label = await self.document_labeling_agent.process_file(input_data)

            match category_and_label:
                case RelevantDocumentClassification():
                    content = await self.document_summarization_service.process_file_gemini(input_data, None)
                    short_ai_description = await self.document_summarization_service.get_ai_short_description(content)
                    await self._backend_api.update_document_status(
                        document_id,
                        UpdateDocumentStatusRequest(
                            status="processing",
                            category=self.category_mapping[category_and_label.category],
                            label=category_and_label.label,
                            aiGeneratedFileName=short_ai_description,
                        ),
                    )

                    # Extract data from file
                    extracted_tiles_data = await self.extract_data_from_file(input_data, content)
                    self.logger.info(f"DataExtractorService() extracted_tiles_data= \n{extracted_tiles_data}\n")

                    if extracted_tiles_data["status"] == "SUCCESS":
                        for table_name in extracted_tiles_data["relevant_tables"]:
                            match table_name:
                                case "Appliances":
                                    try:
                                        # Get appliance data from extraction results
                                        appliance_data = extracted_tiles_data["extracted_data"]["Appliances"]

                                        # Parse JSON if it's a string
                                        if isinstance(appliance_data, str):
                                            appliance_data = json.loads(appliance_data)

                                        self.logger.info(f"Processing appliance data: {appliance_data}")

                                        appliances_to_process = appliance_data["appliances"]
                                        self.logger.info(f"Found {len(appliances_to_process)} appliances to process")

                                        # Get user's existing appliances from database once
                                        user_appliances_from_db = (
                                            await self._appliance_service.get_appliance_by_type_or_brand_or_model(
                                                user_id
                                            )
                                        )

                                        # Process each appliance
                                        for i, single_appliance_data in enumerate(appliances_to_process):
                                            try:
                                                self.logger.info(
                                                    f"Processing appliance {i + 1}/{len(appliances_to_process)}: {single_appliance_data}"
                                                )

                                                # Determine what action to take (insert/update/do_nothing)
                                                commands_for_backend = (
                                                    await self.appliances_manager.insert_or_update_appliance(
                                                        single_appliance_data, user_appliances_from_db
                                                    )
                                                )

                                                command = commands_for_backend["command"]

                                                match command:
                                                    case "insert":
                                                        # Create new appliance
                                                        self.logger.info(f"Creating new appliance {i + 1}")

                                                        # Convert extracted data to ApplianceData format
                                                        appliance_data_obj = self._convert_to_appliance_data(
                                                            single_appliance_data, user_id
                                                        )

                                                        self.logger.info(
                                                            f"Creating new appliance: {appliance_data_obj}"
                                                        )

                                                        create_request = CreateApplianceRequest(
                                                            appliance=appliance_data_obj,
                                                            srcType="document",
                                                            srcId=document_id,
                                                            userId=user_id,
                                                        )

                                                        result = await self._backend_api.create_appliance(
                                                            create_request
                                                        )
                                                        self.logger.info(
                                                            f"Successfully created appliance {i + 1} with ID: {result.get('id')}"
                                                        )

                                                    case "update":
                                                        # Update existing appliance
                                                        appliance_id = commands_for_backend["id"]
                                                        fields_to_update = commands_for_backend["fields"]

                                                        self.logger.info(
                                                            f"Updating appliance {i + 1} (ID: {appliance_id}) with fields: {fields_to_update}"
                                                        )

                                                        # Create partial appliance data with only fields to update
                                                        partial_appliance_data = self._create_partial_appliance_data(
                                                            single_appliance_data, fields_to_update
                                                        )

                                                        update_request = UpdateApplianceRequest(
                                                            appliance=partial_appliance_data,
                                                            srcType="document",
                                                            srcId=document_id,
                                                        )

                                                        await self._backend_api.update_appliance(
                                                            appliance_id, update_request
                                                        )
                                                        self.logger.info(
                                                            f"Successfully updated appliance {i + 1} (ID: {appliance_id})"
                                                        )

                                                    case "do_nothing":
                                                        self.logger.info(
                                                            f"Appliance {i + 1} data already exists - no action needed"
                                                        )

                                                    case _:
                                                        self.logger.warning(
                                                            f"Unknown command for appliance {i + 1}: {command}"
                                                        )

                                            except Exception as e:
                                                self.logger.exception(f"Error processing appliance {i + 1}: {str(e)}")
                                                # Continue processing other appliances even if this one fails
                                                continue

                                        self.logger.info(
                                            f"Completed processing all {len(appliances_to_process)} appliances"
                                        )

                                    except Exception as e:
                                        self.logger.exception(f"Error processing appliance data: {str(e)}")
                                        # Continue processing other tables even if this one fails

                                case "Property":
                                    try:
                                        # Get property data from extraction results
                                        property_data = extracted_tiles_data["extracted_data"]["Property"]

                                        # Parse JSON if it's a string
                                        if isinstance(property_data, str):
                                            property_data = json.loads(property_data)

                                        self.logger.info(f"Processing property data: {property_data}")

                                        user_property = await self._property_service.get_user_primary_property(user_id)

                                        if user_property is None:
                                            self.logger.warning(
                                                f"No property found for user {user_id} - skipping property extraction"
                                            )
                                            continue

                                        commands_for_backend = (
                                            await self.property_details_manager.update_property_details(
                                                property_data, user_property, user_id
                                            )
                                        )

                                        command = commands_for_backend["command"]

                                        match command:
                                            case "update":
                                                # Update existing property
                                                property_id = commands_for_backend["id"]
                                                fields_to_update = commands_for_backend["fields"]
                                                update_data = commands_for_backend["update_data"]

                                                self.logger.info(
                                                    f"Updating property {property_id} with fields: {fields_to_update}"
                                                )

                                                # Create property details data with only fields to update
                                                property_details_data = self._create_property_details_data(update_data)

                                                update_request = UpdatePropertyDetailsRequest(
                                                    propertyDetails=property_details_data,
                                                    srcType="document",
                                                    srcId=document_id,
                                                )

                                                await self._backend_api.update_property_details(
                                                    property_id, update_request
                                                )
                                                self.logger.info(f"Successfully updated property {property_id}")

                                            case "do_nothing":
                                                self.logger.info("Property data already exists - no action needed")

                                            case _:
                                                self.logger.warning(f"Unknown command: {command}")

                                    except Exception as e:
                                        self.logger.exception(f"Error processing property data: {str(e)}")
                                        # Continue processing other tables even if this one fails

                                case _:
                                    # TODO: Implement other data types
                                    self.logger.info(f"Extraction of {table_name} not implemented yet")

                    else:
                        self.logger.info(f"No relevant data tables found for documentId={document_id}")

                    await self._backend_api.update_document_status(
                        document_id, UpdateDocumentStatusRequest(status="processingCompleted")
                    )

                    await self.file_chunking_agent.process_file(input_data, document_id, user_id,
                                                                category_and_label.category,
                                                                bucket_name, file_key)

                    return True, content

                case NotRelevantDocumentClassification():
                    await self._backend_api.update_document_status(
                        document_id, UpdateDocumentStatusRequest(status="irrelevant")
                    )
                    return False, content

        except Exception as e:
            await self._backend_api.update_document_status(
                document_id, UpdateDocumentStatusRequest(status="error", errorMessage=str(e))
            )
            self.logger.exception(f"Error processing file: {str(e)}")

        return False, content

    def _convert_to_appliance_data(self, extracted_data: dict, user_id: int) -> ApplianceData:
        """Convert extracted appliance data to ApplianceData object for creation."""

        return ApplianceData(
            type=extracted_data.get("appliance_type"),
            brand=extracted_data.get("brand"),
            model=extracted_data.get("model"),
            serialNumber=extracted_data.get("serial_number"),
            warranty=extracted_data.get("warranty_details"),
            otherDetails=extracted_data.get("other_details"),
            userId=user_id,  # Assuming user_id maps to property - adjust as needed
        )

    def _create_property_details_data(self, update_data: dict) -> PropertyDetailsData:
        # Handle address object creation
        address_data = update_data.get("address")
        if address_data:
            from src.integrations.backend import Address

            address_obj = Address(**address_data)
            update_data_copy = update_data.copy()
            update_data_copy["address"] = address_obj
            return PropertyDetailsData(**update_data_copy)

        return PropertyDetailsData(**update_data)

    def _create_partial_appliance_data(self, extracted_data: dict, fields_to_update: list) -> ApplianceData:
        """Create ApplianceData object with only the fields that need to be updated."""

        # Start with empty data
        update_data = {}

        # Map extracted field names to ApplianceData field names
        field_mapping = {
            "type": "appliance_type",
            "brand": "brand",
            "model": "model",
            "serialNumber": "serial_number",
            "warranty": "warranty_details",
            "dateOfPurchase": "purchase_date",
            "otherDetails": "other_details",
        }

        # Only include fields that need to be updated
        for field in fields_to_update:
            if field in field_mapping:
                extracted_key = field_mapping[field]
                value = extracted_data.get(extracted_key)

                update_data[field] = value

        return ApplianceData(**update_data)

    async def extract_data_from_file(self, input_data: Path, content_of_file: str):
        relevant_tables = await self.classification_agent.classify_file(input_data, content_of_file)

        if relevant_tables == ["NOT_RELEVANT"]:
            return {"status": "NOT_RELEVANT", "message": "No relevant data tables found"}

        if relevant_tables != ["NOT_RELEVANT"]:
            extracted_data = await self.extraction_agent.extract_data(relevant_tables, content_of_file)
            # Exemplar response for Alex GasCertificate.pdf
            # Extracted data:
            # { 'status': 'SUCCESS',
            #   'relevant_tables': ['Property', 'Appliances'],
            #   'extracted_data': {
            #       'Property': {'property_address': 'Flat 60 Tudor Court',
            # 'type_of_property': 'Flat', 'relation_to_property': None, 'property_subgroup_residential': None,
            # 'type_of_ownership': None, 'size_sqft': None, 'floor_of_unit': None, 'has_balcony_terrace': None,
            # 'balcony_terrace_details': None, 'has_garden': None, 'garden_details': None, 'has_swimming_pool': None,
            # 'swimming_pool_details': None, 'number_of_bedrooms': None, 'number_of_bathrooms': None,
            # 'number_of_floors': None, 'has_floor_plan_document': None, 'has_property_survey_document': None,
            # 'last_sold_price': None, 'condition': None, 'length_of_ownership': None, 'architectural_type': None,
            # 'property_valuation': None, 'listing_or_conservation_status': None, 'type_of_lock': None,
            # 'type_of_construction': None, 'proportion_of_flat_roof_percentage': None,
            # 'has_epc_certificate_document': None, 'epc_rating': None},
            #       'Appliances': {'appliance_type': 'Boiler',
            # 'brand': 'Maxol', 'model': 'Micro turbo', 'serial_number': None, 'warranty_details': None,
            # 'purchase_date': None, 'other_details': "Location: Kitchen. Landlord's appliance. Gas safety check on
            #  2020-01-29: appliance inspected (Yes), serviced (No), safe to use (Yes). Operating Pressure: 18 Mbar.
            # Heat Input: 15.9 kW/h. Flue Type: RS. Safety device operation, ventilation, chimney visual condition,
            # flue visual condition, flue performance checks all satisfactory/Pass. Approved CO alarm fitted,
            # in date, and testing satisfactory.", 'invoice_receipt_information': None}}}
            return {"status": "SUCCESS", "relevant_tables": relevant_tables, "extracted_data": extracted_data}
        else:
            return {"status": "NOT_RELEVANT", "message": "No relevant data tables found"}

    async def extract_data_from_chat_messages(self, input_data: str):

        relevant_tables = await self.classification_agent.classify_message(input_data)
        content = input_data

        if relevant_tables != ["NOT_RELEVANT"]:
            extracted_data = await self.extraction_agent.extract_data(relevant_tables, content)
            return {"status": "SUCCESS", "relevant_tables": relevant_tables, "extracted_data": extracted_data}
        else:
            return {"status": "NOT_RELEVANT", "message": "No relevant data tables found"}
