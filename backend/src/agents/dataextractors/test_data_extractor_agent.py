import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from src.agents.dataextractors.DataExtractorAgent import DataExtractorAgent


class TestDataExtractorAgent:
    
    @pytest.fixture
    def mock_environment(self):
        """Mock environment and dependencies."""
        with patch.dict('os.environ', {'GEMINI_API_KEY': 'test_key'}):
            with patch('google.generativeai.configure'), \
                 patch('google.generativeai.GenerativeModel') as mock_model:
                
                # Mock the model and its response
                mock_response = Mock()
                mock_response.text = '{"appliances": [{"appliance_type": "Test Appliance"}]}'
                mock_model.return_value.generate_content_async = AsyncMock(return_value=mock_response)
                
                yield mock_model

    @pytest.fixture
    def mock_prompt_loader(self):
        """Mock PromptLoader."""
        with patch('src.agents.dataextractors.DataExtractorAgent.PromptLoader') as mock_loader:
            mock_loader.return_value.load_system_prompt.return_value = "Test prompt for {table}"
            yield mock_loader

    @pytest.fixture
    def agent(self, mock_environment, mock_prompt_loader):
        """Create DataExtractorAgent instance with mocked dependencies."""
        return DataExtractorAgent()

    @pytest.mark.asyncio
    async def test_extract_data_not_relevant(self, agent):
        """Test extract_data returns NO_DATA_EXTRACTED when NOT_RELEVANT is in relevant_tables."""
        result = await agent.extract_data(["NOT_RELEVANT"], "test content")
        assert result == {"status": "NO_DATA_EXTRACTED"}

    @pytest.mark.asyncio
    async def test_extract_data_with_valid_tables(self, agent):
        """Test extract_data processes only tables that have prompts."""
        relevant_tables = ["Property", "Appliances", "Bills", "Legal"]
        content = "Test document content"
        
        # Mock the _extract_table_data method to return predictable results
        async def mock_extract_table_data(table, content):
            return {f"{table.lower()}_data": f"extracted_{table.lower()}_info"}
        
        with patch.object(agent, '_extract_table_data', side_effect=mock_extract_table_data):
            result = await agent.extract_data(relevant_tables, content)
        
        # Only Property and Appliances should be processed (Bills/Legal are commented out in _prompt_map)
        assert "Property" in result
        assert "Appliances" in result
        assert "Bills" not in result  # Not in _prompt_map
        assert "Legal" not in result  # Not in _prompt_map
        
        assert result["Property"]["property_data"] == "extracted_property_info"
        assert result["Appliances"]["appliances_data"] == "extracted_appliances_info"

    @pytest.mark.asyncio
    async def test_extract_data_handles_extraction_exceptions(self, agent):
        """Test extract_data handles exceptions from individual table extractions."""
        relevant_tables = ["Property", "Appliances"]
        content = "Test document content"
        
        # Mock _extract_table_data to raise exception for Property, succeed for Appliances
        async def mock_extract_table_data(table, content):
            if table == "Property":
                raise ValueError("Property extraction failed")
            return {f"{table.lower()}_data": f"extracted_{table.lower()}_info"}
        
        with patch.object(agent, '_extract_table_data', side_effect=mock_extract_table_data):
            result = await agent.extract_data(relevant_tables, content)
        
        # Property should have error, Appliances should succeed
        assert "Property" in result
        assert "Appliances" in result
        assert result["Property"]["error"] == "Property extraction failed"
        assert result["Appliances"]["appliances_data"] == "extracted_appliances_info"

    @pytest.mark.asyncio
    async def test_extract_data_empty_relevant_tables(self, agent):
        """Test extract_data with empty relevant_tables list."""
        result = await agent.extract_data([], "test content")
        assert result == {}

    @pytest.mark.asyncio
    async def test_extract_data_tables_not_in_prompt_map(self, agent):
        """Test extract_data ignores tables not in _prompt_map."""
        relevant_tables = ["NonExistentTable", "AnotherInvalidTable"]
        content = "Test document content"
        
        result = await agent.extract_data(relevant_tables, content)
        
        # No tables should be processed since none are in _prompt_map
        assert result == {}

    @pytest.mark.asyncio
    async def test_extract_data_processed_tables_sync(self, agent):
        """Test that processed_tables correctly syncs with results array."""
        relevant_tables = ["Property", "Bills", "Appliances", "Legal", "Insurance"]
        content = "Test document content"
        
        # Mock successful extraction for valid tables
        async def mock_extract_table_data(table, content):
            return {f"{table.lower()}_result": f"success_{table.lower()}"}
        
        with patch.object(agent, '_extract_table_data', side_effect=mock_extract_table_data):
            result = await agent.extract_data(relevant_tables, content)
        
        # Only Property and Appliances should be processed (in _prompt_map)
        assert len(result) == 2
        assert "Property" in result
        assert "Appliances" in result
        
        # Results should match the processed tables
        assert result["Property"]["property_result"] == "success_property"
        assert result["Appliances"]["appliances_result"] == "success_appliances"

    @pytest.mark.asyncio
    async def test_extract_data_mixed_success_failure(self, agent):
        """Test extract_data with mixed success and failure scenarios."""
        relevant_tables = ["Property", "Appliances"]
        content = "Test document content"
        
        # Mock one success, one failure
        call_count = 0
        async def mock_extract_table_data(table, content):
            nonlocal call_count
            call_count += 1
            if call_count == 1:  # First call (Property)
                return {"property_data": "success"}
            else:  # Second call (Appliances)
                raise RuntimeError("Appliances extraction failed")
        
        with patch.object(agent, '_extract_table_data', side_effect=mock_extract_table_data):
            result = await agent.extract_data(relevant_tables, content)
        
        assert "Property" in result
        assert "Appliances" in result
        assert result["Property"]["property_data"] == "success"
        assert result["Appliances"]["error"] == "Appliances extraction failed"