import os
import logging
import pytest

from unittest.mock import AsyncMock, MagicMock, patch



logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_data_extractor")


class TestPropertyDetailsManager:

    @pytest.fixture(scope="session", autouse=True)
    def set_required_env_vars_for_imports(self):
        variables_to_set = {
            "ANTHROPIC_API_KEY": "dummy_anthropic_key_for_import",
            "GEMINI_API_KEY": "dummy_gemini_key_for_import",
            "PSE_API_KEY": "dummy_pse_key_for_import",
            "PSE_CX": "dummy_pse_cx_for_import",
            "IS_AWS": "true",
            "LANGSMITH_API_KEY": "dummy_langsmith_key_for_import",
            "TOGETHER_AI_API_KEY": "dummy_together_key_for_import",
            "AI_ENGINE_API_KEY": "dummy_ai_engine_key_for_import",
            "OPEN_AI_API_KEY": "dummy_ai_engine_key_for_import",
            "QDRANT_DB_URL": "dummy_ai_engine_key_for_import",
            "QDRANT_API_KEY": "dummy_ai_engine_key_for_import",
            "QDRANT_COLLECTION_NAME": "dummy_collection"
        }

        for key, value in variables_to_set.items():
            os.environ[key] = value

    @pytest.fixture
    def property_manager(self):
        """Create PropertyDetailsManager instance"""
        from src.agents.dataextractors.PropertyDetailsManager import PropertyDetailsManager
        return PropertyDetailsManager()

    @pytest.fixture
    def mock_property(self):
        """Create mock property object"""
        mock_property = MagicMock()
        mock_property.id = 123
        mock_property.type = None
        mock_property.tenureType = None
        mock_property.sizeInSqft = None
        mock_property.numberOfBedrooms = None
        mock_property.numberOfBathrooms = None
        mock_property.condition = None
        mock_property.architecturalType = None
        mock_property.address = None
        mock_property.hasBalconyTerrace = None
        mock_property.balconyTerraceDetails = None
        mock_property.hasGarden = None
        mock_property.gardenDetails = None
        mock_property.hasSwimmingPool = None
        mock_property.swimmingPoolDetails = None
        mock_property.onFloorLevel = None
        mock_property.numberOfFloors = None
        mock_property.lastSoldPriceInGbp = None
        mock_property.yearsOfOwnership = None
        mock_property.valuationInGbp = None
        mock_property.conservationStatus = None
        mock_property.typeOfLock = None
        mock_property.typeOfConstruction = None
        mock_property.proportionOfFlatRoof = None
        mock_property.epcRating = None
        return mock_property

    @pytest.fixture
    def user_id(self):
        """Return a test user ID"""
        return 456

    @pytest.mark.asyncio
    async def test_update_property_details_with_new_address_data(self, property_manager, mock_property, user_id):
        """Test updating property with new address data"""
        logger.info("Testing PropertyDetailsManager update with new address data")

        extracted_data = {
            "address": {
                "streetLine1": "123 Main Street",
                "streetLine2": "Flat 4B",
                "townOrCity": "London",
                "postcode": "SW1A 1AA",
                "houseAccess": "Main entrance",
                "parkingInstructions": "Visitor parking available"
            },
            "type": "flat",
            "numberOfBedrooms": 2
        }

        result = await property_manager.update_property_details(extracted_data, mock_property, user_id)

        assert result["command"] == "update"
        assert result["id"] == 123
        assert "address" in result["fields"]
        assert result["update_data"]["address"]["streetLine1"] == "123 Main Street"
        assert result["update_data"]["address"]["townOrCity"] == "London"
        assert result["update_data"]["userId"] == user_id
        assert result["update_data"]["propertyId"] == 123

        logger.info("PASS Test passed: PropertyDetailsManager update with new address data")

    @pytest.mark.asyncio
    async def test_update_property_details_concatenation(self, property_manager, user_id):
        """Test concatenation of string fields"""
        logger.info("Testing PropertyDetailsManager string concatenation")

        mock_property = MagicMock()
        mock_property.id = 123
        mock_property.gardenDetails = "Small front garden"
        mock_property.architecturalType = "Period property"
        # Mock all other attributes as None
        for attr in ["type", "tenureType", "sizeInSqft", "numberOfBedrooms", "numberOfBathrooms",
                     "condition", "address", "hasBalconyTerrace", "balconyTerraceDetails", "hasGarden",
                     "hasSwimmingPool", "swimmingPoolDetails", "onFloorLevel", "numberOfFloors",
                     "lastSoldPriceInGbp", "yearsOfOwnership", "valuationInGbp", "conservationStatus",
                     "typeOfLock", "typeOfConstruction", "proportionOfFlatRoof", "epcRating"]:
            setattr(mock_property, attr, None)

        extracted_data = {
            "gardenDetails": "Large back garden with patio",
            "architecturalType": "Victorian terrace"
        }

        result = await property_manager.update_property_details(extracted_data, mock_property, user_id)

        assert result["command"] == "update"
        assert "gardenDetails" in result["fields"]
        assert "architecturalType" in result["fields"]
        assert "Large back garden with patio" in result["update_data"]["gardenDetails"]
        assert "Small front garden" in result["update_data"]["gardenDetails"]
        assert "Victorian terrace" in result["update_data"]["architecturalType"]
        assert "Period property" in result["update_data"]["architecturalType"]
        assert result["update_data"]["userId"] == user_id
        assert result["update_data"]["propertyId"] == 123

        logger.info("PASS Test passed: PropertyDetailsManager string concatenation")

    @pytest.mark.asyncio
    async def test_update_property_details_do_nothing(self, property_manager, user_id):
        """Test do_nothing when data already exists"""
        logger.info("Testing PropertyDetailsManager do_nothing scenario")

        mock_property = MagicMock()
        mock_property.id = 123
        mock_property.type = MagicMock()
        mock_property.type.value = "flat"
        mock_property.numberOfBedrooms = 2
        mock_property.numberOfBathrooms = 1
        # Mock all other attributes as None
        for attr in ["tenureType", "sizeInSqft", "condition", "architecturalType", "address",
                     "hasBalconyTerrace", "balconyTerraceDetails", "hasGarden", "gardenDetails",
                     "hasSwimmingPool", "swimmingPoolDetails", "onFloorLevel", "numberOfFloors",
                     "lastSoldPriceInGbp", "yearsOfOwnership", "valuationInGbp", "conservationStatus",
                     "typeOfLock", "typeOfConstruction", "proportionOfFlatRoof", "epcRating"]:
            setattr(mock_property, attr, None)

        extracted_data = {
            "type": "flat",
            "numberOfBedrooms": 2,
            "numberOfBathrooms": 1
        }

        result = await property_manager.update_property_details(extracted_data, mock_property, user_id)

        assert result["command"] == "do_nothing"

        logger.info("PASS Test passed: PropertyDetailsManager do_nothing scenario")

    @pytest.mark.asyncio
    async def test_update_property_details_error_handling(self, property_manager, user_id):
        """Test error handling in PropertyDetailsManager"""
        logger.info("Testing PropertyDetailsManager error handling")

        # Pass invalid data to trigger an error
        extracted_data = {"invalid": "data"}
        mock_property = None  # This should cause an error

        result = await property_manager.update_property_details(extracted_data, mock_property, user_id)

        assert result["command"] == "do_nothing"

        logger.info("PASS Test passed: PropertyDetailsManager error handling")

    @pytest.mark.asyncio
    async def test_update_property_details_with_user_and_property_ids(self, property_manager, mock_property, user_id):
        """Test that userId and propertyId are correctly included in update_data"""
        logger.info("Testing PropertyDetailsManager includes userId and propertyId in update_data")

        extracted_data = {
            "type": "house",
            "numberOfBedrooms": 3,
            "condition": "excellent"
        }

        result = await property_manager.update_property_details(extracted_data, mock_property, user_id)

        assert result["command"] == "update"
        assert result["id"] == 123
        assert result["update_data"]["userId"] == user_id
        assert result["update_data"]["propertyId"] == 123
        assert result["update_data"]["type"] == "house"
        assert result["update_data"]["numberOfBedrooms"] == 3
        assert result["update_data"]["condition"] == "excellent"

        logger.info("PASS Test passed: PropertyDetailsManager includes userId and propertyId correctly")