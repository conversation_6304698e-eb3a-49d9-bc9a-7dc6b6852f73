import pytest
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path
import logging

from src.agents.dataextractors.FilesChunkingAgent import FilesChunkingAgent
from src.ai_dao import QdrantDAO


class TestFilesChunkingAgent:
    """Test suite for FilesChunkingAgent class"""

    @pytest.fixture
    def mock_qdrant_dao(self):
        mock = MagicMock(spec=QdrantDAO)
        return mock

    @pytest.fixture
    def mock_gemini_client(self):
        """Mock GeminiClient"""
        mock_client = Mock()
        mock_client.process_file_gemini_flash = AsyncMock()
        return mock_client

    @pytest.fixture
    def mock_qdrant_dao(self):
        """Mock QdrantDAO"""
        mock_dao = Mock()
        mock_dao.upsert_file_chunk = AsyncMock()
        return mock_dao

    @pytest.fixture
    def mock_prompt_loader(self):
        """Mock PromptLoader"""
        mock_loader = Mock()
        mock_loader.load_system_prompt = Mock(return_value="test_prompt")
        return mock_loader

    @pytest.fixture
    def sample_building_survey_chunks(self):
        """Sample chunks data for building survey"""
        return {
            "chunks": [
                {
                    "chunk_id": "survey-12345-summary",
                    "metadata": {
                        "chunk_type": "summary",
                        "document_type": "Building Survey",
                        "property_address": "11 Florence Street, Anytown, AA11 1AA",
                        "report_number": "12345",
                        "survey_date": "19 Jan 2018",
                        "client_name": "Mr J Smith"
                    },
                    "content": "This is a Level 3 Building Survey Report for 11 Florence Street"
                },
                {
                    "chunk_id": "survey-12345-section-5.4",
                    "metadata": {
                        "chunk_type": "section_analysis",
                        "document_type": "Building Survey",
                        "property_address": "11 Florence Street, Anytown, AA11 1AA",
                        "report_number": "12345",
                        "section_number": "5.4",
                        "section_title": "Floors",
                        "condition_rating": "3"
                    },
                    "content": "Section 5.4 Floors, Condition Rating 3: The floors on both ground and first floors..."
                }
            ]
        }

    @pytest.fixture
    def sample_gas_safety_chunks(self):
        """Sample chunks data for gas safety record"""
        return {
            "chunks": [
                {
                    "chunk_id": "gas-cert-GS123-appliance-1",
                    "metadata": {
                        "chunk_type": "appliance_check",
                        "document_type": "Gas Safety Record",
                        "property_address": "123 Test Street",
                        "serial_no": "GS123",
                        "inspection_date": "2024-01-15",
                        "appliance_location": "Kitchen",
                        "appliance_type": "Boiler",
                        "make": "Maxol",
                        "model": "Micro turbo",
                        "is_safe_to_use": "Yes"
                    },
                    "content": "Appliance: Maxol Micro turbo Boiler located in Kitchen. All checks passed."
                }
            ]
        }

    @pytest.fixture
    def sample_inventory_chunks(self):
        """Sample chunks data for check-in inventory"""
        return {
            "chunks": [
                {
                    "chunk_id": "inventory-test-street-living-room-sofa",
                    "metadata": {
                        "chunk_type": "item_condition",
                        "document_type": "Check-in Inventory",
                        "property_address": "123 Test Street",
                        "visit_date": "2024-01-15",
                        "room": "Living Room/Lounge",
                        "item": "Sofa",
                        "item_ref": "2.1",
                        "condition": "Good",
                        "cleanliness": "Excellent"
                    },
                    "content": "Living Room/Lounge - Sofa. Condition: Good, Cleanliness: Excellent."
                }
            ]
        }

    @pytest.fixture
    def agent(self, mock_gemini_client, mock_qdrant_dao, mock_prompt_loader):
        """Create FilesChunkingAgent instance with mocked dependencies"""
        with patch('src.agents.dataextractors.FilesChunkingAgent.GeminiClient', return_value=mock_gemini_client), \
                patch('src.agents.dataextractors.FilesChunkingAgent.QdrantDAO', return_value=mock_qdrant_dao), \
                patch('src.agents.dataextractors.FilesChunkingAgent.PromptLoader', return_value=mock_prompt_loader):
            # Reset class variables before each test
            FilesChunkingAgent._initialized = False
            FilesChunkingAgent._prompt_loader = None

            agent = FilesChunkingAgent(qdrant_dao=mock_qdrant_dao)
            return agent

    def test_initialization_class_variables(self, mock_prompt_loader):
        """Test that class variables are properly initialized"""
        with patch('src.agents.dataextractors.FilesChunkingAgent.PromptLoader', return_value=mock_prompt_loader):
            # Reset class state
            FilesChunkingAgent._initialized = False
            FilesChunkingAgent._prompt_loader = None

            FilesChunkingAgent.initialize()

            assert FilesChunkingAgent._initialized is True
            assert FilesChunkingAgent._prompt_loader is not None
            mock_prompt_loader.load_system_prompt.assert_called_once_with("chunks_generator_prompt")

    def test_initialization_only_once(self, mock_prompt_loader):
        """Test that initialization only happens once"""
        with patch('src.agents.dataextractors.FilesChunkingAgent.PromptLoader', return_value=mock_prompt_loader):
            # Reset class state
            FilesChunkingAgent._initialized = False
            FilesChunkingAgent._prompt_loader = None

            FilesChunkingAgent.initialize()
            FilesChunkingAgent.initialize()  # Call again

            # Should only be called once
            mock_prompt_loader.load_system_prompt.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_file_building_survey_success(self, agent, mock_gemini_client, mock_qdrant_dao,
                                                        sample_building_survey_chunks):
        """Test successful processing of building survey document"""
        # Setup
        input_file = Path("test_survey.pdf")
        document_id = 123
        user_id = 456
        document_category = "survey"

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(sample_building_survey_chunks)

        # Execute
        await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        mock_gemini_client.process_file_gemini_flash.assert_called_once_with(
            input_file, agent._agent_prompt, True
        )

        # Check that upsert_file_chunk was called for each chunk
        assert mock_qdrant_dao.upsert_file_chunk.call_count == 2

        # Verify first chunk call
        first_call = mock_qdrant_dao.upsert_file_chunk.call_args_list[0]
        assert first_call[1]['user_id'] == str(user_id)
        assert first_call[1]['document_id'] == str(document_id)
        assert first_call[1]['chunk_id'] == "survey-12345-summary"
        assert first_call[1]['content'] == "This is a Level 3 Building Survey Report for 11 Florence Street"
        assert first_call[1]['metadata']['document_category'] == document_category
        assert 'document_type' not in first_call[1]['metadata']

    @pytest.mark.asyncio
    async def test_process_file_gas_safety_success(self, agent, mock_gemini_client, mock_qdrant_dao,
                                                   sample_gas_safety_chunks):
        """Test successful processing of gas safety record"""
        # Setup
        input_file = Path("test_gas_cert.pdf")
        document_id = 789
        user_id = 101
        document_category = "certificate"

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(sample_gas_safety_chunks)

        # Execute
        await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        mock_qdrant_dao.upsert_file_chunk.assert_called_once()

        call_args = mock_qdrant_dao.upsert_file_chunk.call_args[1]
        assert call_args['chunk_id'] == "gas-cert-GS123-appliance-1"
        assert call_args['metadata']['appliance_type'] == "Boiler"
        assert call_args['metadata']['is_safe_to_use'] == "Yes"
        assert call_args['metadata']['document_category'] == document_category

    @pytest.mark.asyncio
    async def test_process_file_inventory_success(self, agent, mock_gemini_client, mock_qdrant_dao,
                                                  sample_inventory_chunks):
        """Test successful processing of check-in inventory"""
        # Setup
        input_file = Path("test_inventory.pdf")
        document_id = 111
        user_id = 222
        document_category = "inventory"

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(sample_inventory_chunks)

        # Execute
        await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        mock_qdrant_dao.upsert_file_chunk.assert_called_once()

        call_args = mock_qdrant_dao.upsert_file_chunk.call_args[1]
        assert call_args['chunk_id'] == "inventory-test-street-living-room-sofa"
        assert call_args['metadata']['room'] == "Living Room/Lounge"
        assert call_args['metadata']['condition'] == "Good"
        assert call_args['metadata']['document_category'] == document_category

    @pytest.mark.asyncio
    async def test_process_file_json_decode_error(self, agent, mock_gemini_client, mock_qdrant_dao, caplog):
        """Test handling of JSON decode error"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        mock_gemini_client.process_file_gemini_flash.return_value = "invalid json"

        # Execute
        with caplog.at_level(logging.ERROR):
            await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        assert "Error parsing JSON" in caplog.text
        mock_qdrant_dao.upsert_file_chunk.assert_not_called()

    @pytest.mark.asyncio
    async def test_process_file_no_chunks_in_response(self, agent, mock_gemini_client, mock_qdrant_dao, caplog):
        """Test handling when no chunks are found in response"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps({"no_chunks": []})

        # Execute
        with caplog.at_level(logging.WARNING):
            await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        assert "No chunks found in JSON data" in caplog.text
        mock_qdrant_dao.upsert_file_chunk.assert_not_called()

    @pytest.mark.asyncio
    async def test_process_file_chunk_insertion_error(self, agent, mock_gemini_client, mock_qdrant_dao, caplog):
        """Test handling of individual chunk insertion errors"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        chunks_data = {
            "chunks": [
                {
                    "chunk_id": "test-chunk-1",
                    "metadata": {"document_type": "test"},
                    "content": "test content 1"
                },
                {
                    "chunk_id": "test-chunk-2",
                    "metadata": {"document_type": "test"},
                    "content": "test content 2"
                }
            ]
        }

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(chunks_data)

        # Make the first chunk insertion fail, second succeed
        mock_qdrant_dao.upsert_file_chunk.side_effect = [
            Exception("Database error"),
            None
        ]

        # Execute
        with caplog.at_level(logging.ERROR):
            await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        assert "Error inserting chunk test-chunk-1" in caplog.text
        assert mock_qdrant_dao.upsert_file_chunk.call_count == 2

    @pytest.mark.asyncio
    async def test_process_file_gemini_client_error(self, agent, mock_gemini_client, mock_qdrant_dao, caplog):
        """Test handling of GeminiClient processing error"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        mock_gemini_client.process_file_gemini_flash.side_effect = Exception("Gemini processing error")

        # Execute
        with caplog.at_level(logging.ERROR):
            await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        assert "Error parsing file" in caplog.text
        mock_qdrant_dao.upsert_file_chunk.assert_not_called()

    @pytest.mark.asyncio
    async def test_process_file_metadata_document_type_removal(self, agent, mock_gemini_client, mock_qdrant_dao):
        """Test that document_type is removed from metadata and document_category is added"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "new_category"

        chunks_data = {
            "chunks": [
                {
                    "chunk_id": "test-chunk",
                    "metadata": {
                        "document_type": "should_be_removed",
                        "other_field": "should_remain"
                    },
                    "content": "test content"
                }
            ]
        }

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(chunks_data)

        # Execute
        await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        call_args = mock_qdrant_dao.upsert_file_chunk.call_args[1]
        metadata = call_args['metadata']

        assert 'document_type' not in metadata
        assert metadata['document_category'] == document_category
        assert metadata['other_field'] == "should_remain"

    @pytest.mark.asyncio
    async def test_process_file_bucket_and_file_key_extraction(self, agent, mock_gemini_client, mock_qdrant_dao):
        """Test correct extraction of bucket name and file key"""
        # Setup
        input_file = Path("test_document.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        chunks_data = {
            "chunks": [
                {
                    "chunk_id": "test-chunk",
                    "metadata": {},
                    "content": "test content"
                }
            ]
        }

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(chunks_data)

        # Execute
        await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        call_args = mock_qdrant_dao.upsert_file_chunk.call_args[1]
        assert call_args['bucket_name'] == "bucket"
        assert call_args['file_key'] == "fileKey"

    @pytest.mark.asyncio
    async def test_process_file_chunk_with_missing_fields(self, agent, mock_gemini_client, mock_qdrant_dao):
        """Test handling of chunks with missing fields"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        chunks_data = {
            "chunks": [
                {
                    # Missing chunk_id, content, and metadata
                }
            ]
        }

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(chunks_data)

        # Execute
        await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        call_args = mock_qdrant_dao.upsert_file_chunk.call_args[1]
        assert call_args['chunk_id'] == ""
        assert call_args['content'] == ""
        assert call_args['metadata'] == {"document_category": document_category}

    @pytest.mark.asyncio
    async def test_process_file_empty_chunks_array(self, agent, mock_gemini_client, mock_qdrant_dao, caplog):
        """Test handling of empty chunks array"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps({"chunks": []})

        # Execute
        with caplog.at_level(logging.INFO):
            await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        assert "Successfully processed 0 chunks" in caplog.text
        mock_qdrant_dao.upsert_file_chunk.assert_not_called()

    def test_logger_initialization(self, agent):
        """Test that logger is properly initialized"""
        assert agent.logger is not None
        assert agent.logger.name == "uvicorn"

    @pytest.mark.asyncio
    async def test_successful_processing_logs_chunk_content(self, agent, mock_gemini_client, mock_qdrant_dao, caplog):
        """Test that successful chunk processing logs the content"""
        # Setup
        input_file = Path("test_file.pdf")
        document_id = 123
        user_id = 456
        document_category = "test"

        chunks_data = {
            "chunks": [
                {
                    "chunk_id": "test-chunk-1",
                    "metadata": {},
                    "content": "This is test content for chunk 1"
                }
            ]
        }

        mock_gemini_client.process_file_gemini_flash.return_value = json.dumps(chunks_data)

        # Execute
        with caplog.at_level(logging.INFO):
            await agent.process_file(input_file, document_id, user_id, document_category, "bucket", "fileKey")

        # Verify
        assert "Successfully inserted chunk test-chunk-1" in caplog.text
        assert "This is test content for chunk 1" in caplog.text
        assert "Successfully processed 1 chunks" in caplog.text


if __name__ == "__main__":
    pytest.main([__file__])