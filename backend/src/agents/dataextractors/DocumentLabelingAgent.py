import base64
import io
import json
import logging
import time
from pathlib import Path
from typing import Union

from PIL import Image

from src.ai_clients.GeminiClient import GeminiClient
from src.ai_schemas import RelevantDocumentClassification, NotRelevantDocumentClassification


class DocumentLabellingAgent:
    categories_and_labels = {
        "Property": [
            "Property Deeds",
            "Floor Plans",
            "Home Survey",
            "Property Boundaries",
            "Planning Permissions",
            "Property photo",
            "Property video",
            "Renovation Plans",
            "Contractor Agreements",
            "Building Control Certificates",
            "Receipts for Major Works",
            "Before/After Photos",
            "Service History Records",
            "Annual Maintenance Tasks",
            "Contractor Contact Information",
        ],
        "Insurance": ["Home Insurance Policy", "Contents Insurance", "Warranty Certificates", "Claims History"],
        "Bills": [
            "Water Bills",
            "Electricity Bill (Usage)",
            "Gas Bill (Usage)",
            "Council Tax / Property Tax",
            "Rent / Mortgage",
            "Internet / Broadband Bill",
            "Telephone Bill (Landline / Mobile)",
            "TV Bill (Cable/Satellite Subscription / TV License)",
            "Loan Repayments / Credit Cards",
            "Subscription Services (e.g., Streaming, Software)",
            "Utility Company Contracts",
        ],
        "Appliances": [
            "User Manuals",
            "Warranty Cards",
            "Guarantee",
            "Purchase Receipts",
            "Service Records",
            "Gas Certificates",
            "Electrical Safety Certificates",
            "Energy Performance Certificate (EPC)",
        ],
        "Legal": [
            "Mortgage Agreement",
            "Property Title",
            "Leasehold Documents (if applicable)",
            "Party Wall Agreements",
            "Rights of Way",
        ],
        "Inventory Records": ["Home Contents Inventory", "Valuable Items Documentation", "Photos of Belongings"],
        "Other": [],
    }

    _labelling_prompt = None

    def __init__(self):
        self.logger = logging.getLogger("uvicorn")
        self.gemini_client = GeminiClient()

        if DocumentLabellingAgent._labelling_prompt is None:
            DocumentLabellingAgent._labelling_prompt = self._construct_labelling_prompt()
            self.logger.info(f"prompt==============\n\n {DocumentLabellingAgent._labelling_prompt} \n\n")

        self.labelling_prompt = DocumentLabellingAgent._labelling_prompt

    @classmethod
    def _construct_labelling_prompt(cls) -> str:
        categories_str = ""
        for category, labels in cls.categories_and_labels.items():
            labels_str = "\n".join([f"* {label}" for label in labels])
            categories_str += f"{category}\n{labels_str}\n\n"

        prompt = f"""
            You are a document classification agent specialized in property management documents.
            Analyze the content of this document and categorize it according to the following categories and labels:

            {categories_str}

            If the document is not related to property management or services, classify it as "NOT_RELEVANT".

            If the document belongs to a valid category but doesn't match any existing label, you can assign a new label.

            Return ONLY a JSON response with the following structure:
            For relevant documents:
            {{
                "category": "[Category Name]",
                "label": "[Label Name]",
                "isNewLabel": true|false
            }}

            For non-relevant documents:
            {{
                "category": "NOT_RELEVANT"
            }}

            Focus on the document's content, purpose, and usage in property management context to make your decision.
            """
        return prompt

    @staticmethod
    def encode_image(image: Image.Image) -> str:
        """Encode PIL Image to base64."""
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode("utf-8")

    @staticmethod
    def get_media_type(file_path):
        extension = file_path.lower().split(".")[-1]
        media_types = {
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "webp": "image/webp",
            "heic": "image/heic",
            "heif": "image/heif",
        }
        return media_types.get(extension)

    async def process_pdf(
        self, pdf_path: Path
    ) -> Union[RelevantDocumentClassification, NotRelevantDocumentClassification]:
        self.logger.info(f"DocumentLabelingAgent analysing PDF using Gemini: {pdf_path}")

        try:
            # Generate content asynchronously using the Gemini model.
            response = await self.gemini_client.process_pdf_gemini_flash(pdf_path, self.labelling_prompt)

            classification_result = self._extract_classification_from_response(response)

            self.logger.info(f"Successfully processed PDF with Gemini: {pdf_path}")
            self.logger.info(f"Classification result: {classification_result}")

            return classification_result

        except Exception as e:
            self.logger.exception(f"Error processing PDF with Gemini {pdf_path}: {str(e)}")
            raise

    async def process_image(
        self, image_path: Path
    ) -> Union[RelevantDocumentClassification, NotRelevantDocumentClassification]:
        self.logger.info(f"DocumentLabelingAgent: Processing image with Gemini: {image_path}")

        try:

            mime_type = self.get_media_type(str(image_path))
            if not mime_type:
                raise ValueError(f"Unsupported image format: {image_path}")

            response = await self.gemini_client.process_image_gemini_flash(image_path, mime_type, self.labelling_prompt)

            self.logger.info(f"LLM RESPONSE: {response}")
            classification_result = self._extract_classification_from_response(response)

            self.logger.info(f"Successfully processed image with Gemini: {image_path}")

            return classification_result

        except Exception as e:
            self.logger.exception(f"Error processing image with Gemini {image_path}: {str(e)}")
            raise

    def _extract_classification_from_response(
        self, response_text: str
    ) -> Union[RelevantDocumentClassification, NotRelevantDocumentClassification]:
        """Extract and validate classification from LLM response, returning a Pydantic model."""
        try:
            # Try to find JSON object in the response
            start_idx = response_text.find("{")
            end_idx = response_text.rfind("}") + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                parsed_json = json.loads(json_str)

                # Validate and create appropriate Pydantic model
                if "category" in parsed_json:
                    if parsed_json["category"] == "NOT_RELEVANT":
                        return NotRelevantDocumentClassification(category="NOT_RELEVANT")

                    if "label" in parsed_json and "isNewLabel" in parsed_json:
                        # Verify the category is valid
                        if parsed_json["category"] in self.categories_and_labels or parsed_json["category"] == "Other":
                            return RelevantDocumentClassification(**parsed_json)

            # If we couldn't find valid JSON, return a default "Other" classification
            return RelevantDocumentClassification(category="Other", label="Unclassified Document", isNewLabel=True)

        except json.JSONDecodeError as e:
            self.logger.exception(f"Failed to parse JSON from response: {response_text}. Error: {e}")
            return RelevantDocumentClassification(category="Other", label="Unclassified Document", isNewLabel=True)
        except Exception as e:
            self.logger.exception(f"Unexpected error parsing response: {response_text}. Error: {e}")
            return RelevantDocumentClassification(category="Other", label="Unclassified Document", isNewLabel=True)

    async def process_file(
        self, file_path: Path
    ) -> Union[RelevantDocumentClassification, NotRelevantDocumentClassification]:
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        start_time = time.time()

        if file_path.suffix.lower() == ".pdf":
            classification_result = await self.process_pdf(file_path)
        else:
            classification_result = await self.process_image(file_path)

        processing_time = time.time() - start_time
        self.logger.info(f"Completed classification of {file_path} with Gemini in {processing_time:.2f} seconds")

        return classification_result
