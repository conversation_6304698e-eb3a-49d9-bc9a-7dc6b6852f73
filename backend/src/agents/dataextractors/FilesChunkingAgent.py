import logging
import json
from pathlib import <PERSON>

from typing import Optional, ClassVar

from src.agents.utils.PromptLoader import Prompt<PERSON>oader
from src.ai_clients.GeminiClient import Gemini<PERSON><PERSON>
from src.ai_dao.QdrantDAO import QdrantDAO


class FilesChunkingAgent:
    _agent_prompt: ClassVar[str]

    _prompt_loader: ClassVar[Optional[PromptLoader]] = None

    _initialized: ClassVar[bool] = False

    @classmethod
    def initialize(cls):
        if not cls._initialized:
            cls._prompt_loader = PromptLoader()
            cls._initialized = True
            cls._agent_prompt = cls._prompt_loader.load_system_prompt("chunks_generator_prompt")
            logging.getLogger("uvicorn").info(f"Preloaded prompt: chunks_generator_prompt")

    def __init__(self, qdrant_dao: QdrantDAO):
        self.logger = logging.getLogger("uvicorn")

        FilesChunkingAgent.initialize()

        self._gemini_client = GeminiClient()
        self._qdrant_dao = qdrant_dao

    async def process_file(self, input_data: Path, document_id: int, user_id: int, document_category: str,
                           bucket_name: str, file_key: str):
        try:
            json_with_chunks = await self._gemini_client.process_file_gemini_flash(input_data, self._agent_prompt, True)

            # Parse the JSON string
            chunks_data = json.loads(json_with_chunks)


            # Iterate through chunks and insert into Qdrant
            if "chunks" in chunks_data:
                for chunk in chunks_data["chunks"]:
                    try:
                        # Extract chunk data
                        chunk_id = chunk.get("chunk_id", "")
                        content = chunk.get("content", "")
                        metadata = chunk.get("metadata", {})

                        # Remove document_type from metadata and add document_category
                        if "document_type" in metadata:
                            del metadata["document_type"]
                        metadata["document_category"] = document_category

                        # Insert chunk into Qdrant
                        await self._qdrant_dao.upsert_file_chunk(
                            user_id=str(user_id),
                            document_id=str(document_id),
                            chunk_id=chunk_id,
                            metadata=metadata,
                            content=content,
                            bucket_name=bucket_name,
                            file_key=file_key
                        )

                        self.logger.info(f"Successfully inserted chunk {chunk_id} for document {document_id}\nchunk=\n {content} \n\n")

                    except Exception as e:
                        self.logger.exception(f"Error inserting chunk {chunk.get('chunk_id', 'unknown')}: {str(e)}")
                        continue

                self.logger.info(
                    f"Successfully processed {len(chunks_data['chunks'])} chunks for document {document_id}")
            else:
                self.logger.warning(f"No chunks found in JSON data for document {document_id}")

        except json.JSONDecodeError as e:
            self.logger.exception(f"FilesChunkingAgent: Error parsing JSON for file {str(input_data)}: {str(e)}")
        except Exception as e:
            self.logger.exception(f"FilesChunkingAgent: Error parsing file {str(input_data)}: {str(e)}")
