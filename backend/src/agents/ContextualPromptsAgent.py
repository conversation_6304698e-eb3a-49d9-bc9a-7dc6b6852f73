import asyncio
import json
import logging
from typing import Optional
from pydantic import BaseModel

from src.agents.prompts.contextual_prompts_template import CONTEXTUAL_PROMPTS_TEMPLATE
from src.agents.rag.rag_context import RAGContext
from src.ai_clients.GeminiClient import <PERSON><PERSON><PERSON>
from src.ai_dao.QdrantDAO import QdrantDAO
from src.db_models.contextual_prompt import ContextualPromptType
from src.db_models.todo import Todo
from src.services.ai_appliances import ApplianceService
from src.services.ai_jobs import JobService
from src.services.ai_property import PropertyService
from src.services.contextual_prompts import ContextualPromptService
from src.services.data_retrieval import DataRetrievalService


class ContextualPrompts(BaseModel):
    prompts: list[str] = []
    appliance_id: Optional[int] = None


class ContextualPromptAgent:

    def __init__(
        self,
        job_service: JobService,
        appliance_service: ApplianceService,
        property_service: PropertyService,
        qdrant_dao: QdrantDAO,
        gemini_client: GeminiClient,
        contextual_prompts_service: ContextualPromptService,
    ):
        self.logger = logging.getLogger("uvicorn")
        self.job_service = job_service
        self.appliance_service = appliance_service
        self.property_service = property_service
        self.qdrant_dao = qdrant_dao
        self.gemini_client = gemini_client
        self.contextual_prompts_service = contextual_prompts_service
        
        # Initialize the data retrieval service
        self.data_retrieval_service = DataRetrievalService(
            job_service=job_service,
            appliance_service=appliance_service,
            property_service=property_service,
            qdrant_dao=qdrant_dao,
        )

    async def generate_contextual_prompts(self, user_id: int, todo: Todo):
        """
        Main method to search all data sources and return contextual prompts
        and appliances linked to ToDo if present

        Args:
            todo: todo entity
            user_id: user id

        Returns:
            ContextualPrompts with relevant information and summary
        """
        self.logger.info(f"ContextualPromptsAgent.generate_contextual_prompts() for todo={todo}")

        appliances_data = await self.data_retrieval_service.get_appliances_data(user_id)
        jobs_data = await self.data_retrieval_service.get_jobs_data(user_id)
        property_data = await self.data_retrieval_service.get_property_data(user_id)
        # leave here for possible future extension
        # documents_data = await self.data_retrieval_service.get_documents_data(user_id, search_query)
        # chat_history_data = await self.data_retrieval_service.get_chat_history_data(user_id, search_query)

        # Create RAG context
        rag_context = RAGContext(
            appliances=appliances_data,
            jobs=jobs_data,
            property_info=property_data,
            documents=[],
            chat_history=[],
        )

        # Generate contextual summary using Gemini
        contextual_prompts = await self._generate_contextual_prompt(rag_context, todo)

        # delete old ones
        if contextual_prompts.prompts:
            await self.contextual_prompts_service.delete_contextual_prompts_for_todo(todo.id, user_id)

        # insert new prompts and link to appliance if found
        await self.contextual_prompts_service.create_contextual_prompts_with_appliance_link(
            contextual_prompts.prompts,
            todo.id,
            ContextualPromptType.todoPrompt,
            user_id,
            contextual_prompts.appliance_id
        )

        self.logger.info(f"ContextualPromptAgent completed generation: {str(contextual_prompts)}")

    async def _generate_contextual_prompt(self, rag_context: RAGContext, todo: Todo) -> ContextualPrompts:
        data_summary = []

        if rag_context.appliances:
            data_summary.append(f"APPLIANCES: {rag_context.appliances}")

        if rag_context.jobs:
            data_summary.append(f"RECENT JOBS: {rag_context.jobs}")

        if rag_context.property_info:
            data_summary.append(f"PROPERTY: {rag_context.property_info}")

        # Prepare the data summary
        data_summary_str = chr(10).join(data_summary) if data_summary else "No additional property data available"

        prompt = CONTEXTUAL_PROMPTS_TEMPLATE.substitute(
            todo_id=str(todo.id),
            todo_name=str(todo.name),
            todo_description=str(todo.description),
            todo_due_date=str(todo.dueDate),
            data_summary=data_summary_str
        )

        self.logger.info(f"=====>ContextualPromptAgent: data for contextual prompts generation: {prompt}")

        # Try to generate prompts with Gemini
        try:
            response = await self.gemini_client.process_text_prompt_gemini_flash(prompt, is_json_response=True)
        except AttributeError as e:
            self.logger.error(f"Gemini client not properly initialized: {str(e)}")
            return self._get_default_prompts()
        except ConnectionError as e:
            self.logger.error(f"Gemini API connection error: {str(e)}")
            return self._get_default_prompts()
        except TimeoutError as e:
            self.logger.error(f"Gemini API timeout: {str(e)}")
            return self._get_default_prompts()
        except Exception as e:
            self.logger.exception(f"Unexpected error calling Gemini API: {str(e)}")
            return self._get_default_prompts()

        # Try to parse the JSON response
        try:
            json_data = json.loads(response)
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON response from Gemini: {str(e)}, response: {response}")
            return self._get_default_prompts()
        except Exception as e:
            self.logger.exception(f"Unexpected error parsing JSON response: {str(e)}")
            return self._get_default_prompts()

        # Try to validate and create the Pydantic model
        try:
            contextual_prompts = ContextualPrompts(**json_data)
        except ValueError as e:
            self.logger.error(f"Invalid data structure for ContextualPrompts: {str(e)}, data: {json_data}")
            return self._get_default_prompts()
        except Exception as e:
            self.logger.exception(f"Unexpected error creating ContextualPrompts model: {str(e)}")
            return self._get_default_prompts()

        self.logger.info(f"Generated contextual prompts: {contextual_prompts.model_dump()}")

        return contextual_prompts

    def _get_default_prompts(self) -> ContextualPrompts:
        """Return default prompts when generation fails"""
        return ContextualPrompts(
            prompts=[
                "Need help with this task",
                "Share more details",
                "Find qualified local professionals"
            ],
            appliance_id=None
        )
