import logging
import re
import os

import aiohttp
import json
from typing import List, Tuple, Optional
import traceback
import io
from PIL import Image
from urllib.parse import urlparse
from google import genai
from google.genai import types
import asyncio

from src.ai_schemas import ImageUrl

# TODO
# WARNING:  No valid indices found in Gemini response, using first images


class ImageAgentGemini:
    def __init__(self):
        """Initialize the ImageAgentGemini with necessary configurations."""
        self.logger = logging.getLogger("uvicorn")

        # API Keys
        self.pse_api_key = os.environ.get("PSE_API_KEY")
        if not self.pse_api_key:
            self.logger.warning("PSE_API_KEY not found in environment variables")

        self.pse_cx = os.environ.get("PSE_CX")
        if not self.pse_cx:
            self.logger.warning("PSE_CX (Custom Search Engine ID) not found in environment variables")

        # Configure Gemini
        self.gemini_api_key = os.environ.get("GOOGLE_API_KEY")
        if not self.gemini_api_key:
            self.logger.warning("GOOGLE_API_KEY not found in environment variables")
        else:
            self.genai_client = genai.Client()

        # API Endpoints
        self.pse_endpoint = "https://customsearch.googleapis.com/customsearch/v1"

    async def search_images(self, query: str, max_results: int = 30) -> List[dict]:
        try:
            # Create tmp_images directory if it doesn't exist
            os.makedirs("tmp_images", exist_ok=True)

            # Append home UK context to queries to narrow results
            if "not sure" not in query.lower():
                # Expand with relevant keywords to get more home maintenance specific results
                query_final = f"{query} uk home vertical image"
            else:
                query_final = query

            self.logger.info(f"Searching for images with query: '{query_final}'")

            # PSE can only return 10 results per page, so we need to make multiple calls
            all_images = []
            pages_to_fetch = (max_results + 9) // 10  # Calculate number of pages needed

            # Gather all images
            all_images = []

            async with aiohttp.ClientSession() as session:
                # First, fetch all search results
                for page in range(pages_to_fetch):
                    start_index = page * 10 + 1  # PSE uses 1-based indexing

                    # Build the parameters for the PSE API request
                    params = {
                        "q": query_final,
                        "cx": self.pse_cx,
                        "key": self.pse_api_key,
                        "searchType": "image",
                        "num": 10,  # Max 10 per request
                        "start": start_index,
                        "gl": "uk",  # Set locale to UK
                        "safe": "active",  # Safe search setting
                        "imgType": "photo",  # Focus on photos
                    }

                    async with session.get(self.pse_endpoint, params=params) as response:
                        self.logger.info(f"Params: '{params}'")
                        if response.status != 200:
                            error_text = await response.text()
                            self.logger.exception(f"PSE API error: {response.status} - {error_text}")
                            # If the first page fails, return empty list; otherwise, use what we have
                            if page == 0:
                                return []
                            break

                        result = await response.json()

                    # Extract image items from the result
                    items = result.get("items", [])
                    if not items:
                        # No more results, or no results for this page
                        break

                    # Convert PSE image format to match our expected format
                    for item in items:
                        image_data = {
                            "imageUrl": item.get("link", ""),
                            "title": item.get("title", ""),
                            "source": item.get("displayLink", ""),
                            "height": item.get("image", {}).get("height", 0),
                            "width": item.get("image", {}).get("width", 0),
                            "thumbnail": item.get("image", {}).get("thumbnailLink", ""),
                            "context": item.get("image", {}).get("contextLink", ""),
                        }
                        all_images.append(image_data)

                    # stop if we have enough images
                    if len(all_images) >= max_results or "nextPage" not in result.get("queries", {}):
                        break

            self.logger.info(f"Received {len(all_images)} images from PSE search API")

            if len(all_images) == 0:
                self.logger.warning(f"No images found for query: '{query_final}'")
                return []

            # Pre-filter images that are unlikely to be useful
            filtered_images = []
            for img in all_images:
                # Skip images without URLs
                if "imageUrl" not in img or not img["imageUrl"]:
                    continue

                # Check URL for common signs of problematic images
                url = img["imageUrl"].lower()

                # Skip small thumbnail images that often fail
                if "thumb" in url or "icon" in url:
                    continue

                # Skip likely placeholder images
                if "placeholder" in url or "default" in url:
                    continue

                filtered_images.append(img)

            self.logger.info(f"Pre-filtered to {len(filtered_images)} potentially useful images")

            # Save images to tmp_images directory
            filtered_images = filtered_images[:max_results]

            # Now download and save the filtered images using a new session
            # async with aiohttp.ClientSession() as download_session:
            #     saved_images = await self.save_images_to_folder(filtered_images, query, download_session)
            #
            # return saved_images
            return filtered_images

        except Exception as e:
            self.logger.exception(f"Error searching for images: {str(e)}")
            self.logger.exception(f"Stack trace: {traceback.format_exc()}")
            return []

    async def download_and_process_image_with_session(
        self, session: aiohttp.ClientSession, url: str, max_size: int = 500 * 1024
    ) -> Optional[bytes]:
        """
        Download an image from a URL using a provided session and process it to ensure it's a JPEG under 500KB.

        Args:
            session: Active aiohttp ClientSession to use for the request
            url: The URL of the image
            max_size: Maximum size in bytes (500KB for more reliable Gemini requests)

        Returns:
            Processed image bytes or None if failed
        """
        try:
            # Add timeout and proper headers to appear like a browser
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.9",
                "Referer": "https://www.google.com/",
            }

            try:
                # Use a timeout to avoid hanging on slow connections
                self.logger.info(f"Trying to download image from {url}")
                async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=6)) as response:
                    if response.status != 200:
                        self.logger.warning(f"Failed to download image from {url}: HTTP status {response.status}")
                        return None

                    # Check content type to ensure it's an image
                    content_type = response.headers.get("Content-Type", "")
                    if not content_type.startswith("image/"):
                        self.logger.warning(f"URL returned non-image content type: {content_type} from {url}")
                        return None

                    # Get image data directly into memory
                    image_bytes = await response.read()

                    # Verify we got actual data
                    if len(image_bytes) < 100:  # Too small to be a valid image
                        self.logger.warning(f"Downloaded image too small ({len(image_bytes)} bytes) from {url}")
                        return None

                    # Open the image with PIL
                    img = Image.open(io.BytesIO(image_bytes))

                    # Get original dimensions
                    width, height = img.size

                    # Verify dimensions are reasonable
                    if width < 50 or height < 50:
                        self.logger.warning(f"Image too small: {width}x{height} from {url}")
                        return None

                    # Check if the image is already small enough
                    if len(image_bytes) <= max_size and (img.format.lower() == "jpeg" or img.format.lower() == "jpg"):
                        return image_bytes

                    # ALWAYS convert to JPEG regardless of original format
                    # Start with a moderate quality
                    quality = 85
                    target_size = max_size

                    # Start with reasonable dimensions (limit max dimension)
                    if max(width, height) > 1000:
                        scale = 1000 / max(width, height)
                        width = int(width * scale)
                        height = int(height * scale)
                        img = img.resize((width, height), Image.LANCZOS)

                    # If image has alpha channel (like PNG), convert to RGB
                    if img.mode in ("RGBA", "LA") or (img.mode == "P" and "transparency" in img.info):
                        background = Image.new("RGB", img.size, (255, 255, 255))
                        background.paste(img, mask=img.split()[3] if img.mode == "RGBA" else None)
                        img = background

                    # First compression attempt
                    img_io = io.BytesIO()
                    img.save(img_io, format="JPEG", quality=quality, optimize=True)
                    image_bytes = img_io.getvalue()

                    # If still too large, use binary search to find optimal quality
                    if len(image_bytes) > target_size:
                        min_quality = 10  # Don't go below this to maintain reasonable quality
                        max_quality = quality
                        attempt = 0

                        while min_quality <= max_quality and attempt < 8:  # Limit attempts
                            attempt += 1
                            quality = (min_quality + max_quality) // 2

                            img_io = io.BytesIO()
                            img.save(img_io, format="JPEG", quality=quality, optimize=True)
                            image_bytes = img_io.getvalue()

                            if len(image_bytes) <= target_size:
                                min_quality = quality + 1  # Try to increase quality
                            else:
                                max_quality = quality - 1  # Need to decrease quality

                        # If still too large, resize the image further
                        if len(image_bytes) > target_size:
                            quality = min(quality, 70)  # Cap quality

                            # Calculate new dimensions to reach target size
                            scale_factor = 0.8  # Start with 80% reduction
                            iterations = 0

                            while len(image_bytes) > target_size and iterations < 5:
                                iterations += 1
                                width = int(width * scale_factor)
                                height = int(height * scale_factor)

                                # Don't let the image get too small
                                if width < 200 or height < 200:
                                    width = max(width, 200)
                                    height = max(height, 200)

                                    # Final attempt with minimum size and low quality
                                    img_resized = img.resize((width, height), Image.LANCZOS)
                                    img_io = io.BytesIO()
                                    img_resized.save(img_io, format="JPEG", quality=60, optimize=True)
                                    image_bytes = img_io.getvalue()
                                    break

                                # Resize and compress
                                img_resized = img.resize((width, height), Image.LANCZOS)
                                img_io = io.BytesIO()
                                img_resized.save(img_io, format="JPEG", quality=quality, optimize=True)
                                image_bytes = img_io.getvalue()

                    # Log final result
                    final_size_kb = len(image_bytes) / 1024
                    self.logger.info(
                        f"Successfully processed image: {width}x{height}, {final_size_kb:.1f}KB from {url}"
                    )
                    return image_bytes

            except asyncio.TimeoutError:
                self.logger.warning(f"Timeout downloading image from {url}")
                return None
            except aiohttp.ClientError as client_error:
                self.logger.warning(f"Client error downloading from {url}: {str(client_error)}")
                return None

        except Exception as e:
            self.logger.warning(f"Error processing image from {url}: {str(e)}")
            return None

    async def rank_images_with_gemini(self, images: List[dict], query: str, max_results: int) -> Optional[List[dict]]:
        """
        Use Gemini 2.0 Flash to rank the most relevant images for a home maintenance conversation.
        Modified to handle API limits better.

        Args:
            images: List of image results from search
            query: The original search query
            max_results: Maximum number of results to return

        Returns:
            Filtered and ranked list of images, or None if there's an exception or if Gemini deems all images inappropriate
        """
        self.logger.info(f"rank_images_with_gemini(): query={query} images: {str(images)}")

        if not images:
            return []

        if not self.gemini_api_key:
            self.logger.warning("Gemini API key not found, returning unfiltered results")
            return images[:max_results]

        try:
            valid_images = []

            async with aiohttp.ClientSession() as shared_session:
                download_tasks = []
                for img in images:
                    if "imageUrl" not in img or not img["imageUrl"]:
                        self.logger.debug(f"Skipping image without URL: {img}")
                        continue

                    image_url = img["imageUrl"]

                    parsed_url = urlparse(image_url)
                    if not parsed_url.scheme or not parsed_url.netloc:
                        self.logger.debug(f"Skipping invalid URL: {image_url}")
                        continue

                    extension = parsed_url.path.split(".")[-1].lower() if "." in parsed_url.path else ""
                    valid_extensions = ["jpg", "jpeg", "png", "gif", "webp", "bmp"]
                    if extension and extension not in valid_extensions:
                        self.logger.debug(f"Skipping non-image extension: {extension} in URL {image_url}")
                        continue

                    # Pass the shared session to the download method
                    download_tasks.append(self.download_and_process_image_with_session(shared_session, image_url))
                    valid_images.append(img)

                    # Limit the number of downloads to avoid excessive processing
                    if len(valid_images) >= max(10, max_results * 2):
                        break

                self.logger.info(f"Attempting to download {len(download_tasks)} images")

                # Wait for all downloads to complete with the shared session
                image_bytes_list = await asyncio.gather(*download_tasks, return_exceptions=True)

            # Count successful downloads
            successful_downloads = sum(1 for img_bytes in image_bytes_list if img_bytes is not None)
            self.logger.info(f"Successfully downloaded {successful_downloads} out of {len(download_tasks)} images")

            # If we have very few images, skip Gemini ranking
            if successful_downloads < 2:
                self.logger.warning(f"Not enough valid images ({successful_downloads}) for Gemini analysis")
                return valid_images[:max_results]

            filtered_valid_images = []
            filtered_image_bytes = []

            # Add only valid downloaded images
            for i, (img_bytes, img) in enumerate(zip(image_bytes_list, valid_images)):
                if img_bytes:
                    filtered_valid_images.append(img)
                    filtered_image_bytes.append(img_bytes)
                    self.logger.debug(f"Added image {i} for potential Gemini analysis: {img.get('title', 'Untitled')}")

            # IMPORTANT: I limit the maximum number of images sent to Gemini in a single request
            # This is likely the cause of 400 errors
            MAX_GEMINI_IMAGES = 15

            if len(filtered_valid_images) > MAX_GEMINI_IMAGES:
                self.logger.info(
                    f"Limiting Gemini request to {MAX_GEMINI_IMAGES} images (from {len(filtered_valid_images)})"
                )
                # Keep only the first N images to avoid exceeding limits
                filtered_valid_images = filtered_valid_images[:MAX_GEMINI_IMAGES]
                filtered_image_bytes = filtered_image_bytes[:MAX_GEMINI_IMAGES]

            # Add the prompt as first element in contents
            contents = []
            prompt = f"""You are a home maintenance expert helping UK homeowners.

                    I need you to select the {min(max_results, len(filtered_valid_images))} most relevant and helpful images from the ones provided to illustrate the topic: "{query}"

                    The images should:
                    1. Be clear and easy to understand
                    2. Directly relate to the query about home maintenance or home appliances
                    3. Be appropriate for UK home context
                    4. Show the issue or solution clearly
                    5. Images from catalogs are ok
                    6. Do not include images with text - especially large fonts are bad.

                    In case there is an image that illustrates the topic properly return ONLY a JSON array of indices representing the best {min(max_results, len(filtered_valid_images))} images, ordered from most to least relevant.
                    Example response: [3, 0, 5] means image at index 3 is best, followed by image 0, then image 5.

                    In case that all attached images are not appropriate return: NO    
            """
            contents.append(prompt)

            # Track total request size to ensure we don't exceed API limits
            total_request_size = 0

            # Add images to the contents
            for i, (img_bytes, img) in enumerate(zip(filtered_image_bytes, filtered_valid_images)):
                # Always use JPEG MIME type regardless of source image
                mime_type = "image/jpeg"
                img_size = len(img_bytes)
                total_request_size += img_size

                image_part = genai.types.Part.from_bytes(
                    data=img_bytes,
                    mime_type="image/jpeg",
                )

                contents.append(image_part)
                self.logger.debug(
                    f"Added image {i} to Gemini request: {img.get('title', 'Untitled')} ({img_size / 1024:.1f} KB)"
                )

            self.logger.info(f"Total request size: {total_request_size / (1024 * 1024):.2f} MB")

            # Generate content with Gemini
            self.logger.info(f"Sending {len(filtered_valid_images)} images to Gemini for ranking")

            try:
                response = await self.genai_client.aio.models.generate_content(
                    model="gemini-2.5-flash",
                    contents=contents,
                    config=types.GenerateContentConfig(temperature=0.2, top_p=0.8, max_output_tokens=300000),
                )

                # Process Gemini's response
                gemini_text = response.text
                self.logger.info(f"Gemini response text: {gemini_text[:500]}...")

                # Check if Gemini responded with "NO"
                if "NO" in gemini_text.strip().upper():
                    self.logger.info("Gemini indicated all images are inappropriate")
                    return None

                json_matches = re.findall(r"\[(?:\d+,\s*)*\d+\]", gemini_text)

                if json_matches:
                    # Parse the first match
                    indices = json.loads(json_matches[0])
                else:
                    number_matches = re.findall(r"\b(\d+)\b", gemini_text)
                    indices = [int(num) for num in number_matches[:max_results]]

                # Validate indices
                valid_indices = [i for i in indices if i < len(filtered_valid_images)]

                # If no valid indices found, use first images
                if not valid_indices:
                    self.logger.warning("No valid indices found in Gemini response, using first images")
                    return None

                # Return ranked images
                ranked_images = [filtered_valid_images[i] for i in valid_indices]

                # If we didn't get enough images, add more from the valid set
                remaining_count = max_results - len(ranked_images)
                if remaining_count > 0:
                    used_indices = set(valid_indices)
                    additional_indices = [i for i in range(len(filtered_valid_images)) if i not in used_indices]
                    ranked_images.extend([filtered_valid_images[i] for i in additional_indices[:remaining_count]])

                self.logger.info(f"Successfully ranked {len(ranked_images)} images with Gemini")
                return ranked_images[:max_results]

            except Exception as e:
                self.logger.exception(f"Gemini API error: {str(e)}")
                self.logger.exception(f"Number of images sent: {len(filtered_valid_images)}")
                self.logger.exception(f"Total request size: {total_request_size} bytes")

                # Return None instead of falling back to unranked images
                self.logger.warning("Returning None due to Gemini API error")
                return None

        except Exception as e:
            self.logger.exception(f"Error in image ranking process: {str(e)}")
            # Return None instead of falling back to unfiltered images
            return None

    async def handle_search_request(self, query: str, max_images=3) -> Tuple[str, List[ImageUrl]]:
        """
        Process a single image search query, get images, rank them with Gemini, and return the best ones.

        Args:
            query: The search query
            max_images: Maximum number of images to return

        Returns:
            Tuple containing:
                - Formatted string with image search results
                - List of ImageUrl objects
        """
        try:
            # Get initial search results (more than needed for Gemini filtering)
            initial_images = await self.search_images(query, 20)

            if not initial_images:
                return f"No image results found for '{query}'", []

            # Rank images with Gemini
            images = await self.rank_images_with_gemini(initial_images, query, max_images)

            image_urls = []

            # Extract image URLs and create ImageUrl objects
            if images:
                for img in images:
                    if "imageUrl" in img and img["imageUrl"]:
                        image_obj = ImageUrl(
                            imageUrl=img["imageUrl"],
                            description=query,  # Use the search query as description
                            source=img.get("source"),  # Get source if available
                        )
                        image_urls.append(image_obj)

            results = "No images selected by Gemini"
            # Format results into a readable string
            if images:
                results = [f"- {img.get('title', 'Untitled')}: {img.get('imageUrl', 'No URL')}" for img in images]

            result_text = f"Image search results for '{query}':\n" + "\n".join(results)
            return result_text, image_urls

        except Exception as e:
            self.logger.exception(f"Error in handle_search_request: {str(e)}")
            return f"Error performing image search: {str(e)}", []
