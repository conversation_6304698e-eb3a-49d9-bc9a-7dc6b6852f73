import logging
import os
from datetime import date
from typing import List

import langsmith as ls
from langchain_anthropic import <PERSON>t<PERSON>nthropic
from langchain.output_parsers import PydanticOutputParser
from pydantic import BaseModel

from src.agents.utils.ChatFormatter import Cha<PERSON><PERSON><PERSON>atter
from src.ai_schemas import ChatMessage, FindTodo


logger = logging.getLogger("todos_agent")


class FindTodoListWrapper(BaseModel):
    todos: list[FindTodo]


class TodosAgent:
    """Agent for extracting todos from given chats"""

    def __init__(self, agent_prompt: str):
        assert os.environ["ANTHROPIC_API_KEY"], "ANTHROPIC_API_KEY environment variable is required"

        self.extract_todos_llm = ChatAnthropic(
            model="claude-3-7-sonnet-20250219",
            temperature=0.1,
            max_tokens=2000,
        )
        self.system_prompt = agent_prompt

    @ls.traceable(name="extract_todos", run_type="chain")
    async def extract_todos(self, messages: list[ChatMessage]) -> List[FindTodo]:
        conversation_formated = ChatFormatter.format_conversation_from_list_of_messages(messages)
        full_prompt = f"{self.system_prompt}\n " f"Today is: {date.today()}\n\n" f"{conversation_formated}"
        logger.info(f"Extract todos prompt: {full_prompt}")
        chain = self.extract_todos_llm | PydanticOutputParser(pydantic_object=FindTodoListWrapper)
        result = await chain.ainvoke(full_prompt)
        logger.info(f"Extracted todos: {result.todos}")
        return result.todos
