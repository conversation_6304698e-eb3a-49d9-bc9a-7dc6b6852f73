graph TD
    Doc[Document Catalog] --> LLM1[LLM 1<br>Document Summarizer]
    LLM1 --> Summaries[Document Summaries]
    
    Summaries --> Embeddings[OpenAI Embeddings]
    Embeddings --> QdrantDB[(Qdrant Vector DB<br>RAG Index)]
    
    UserQ[User Question] --> RetrieverC[Retriever Component<br>Get 5 Closest Summaries]
    QdrantDB --> RetrieverC
    
    RetrieverC --> SelectionAgent[Selection Agent<br>Filter Relevant Documents]
    
    SelectionAgent --> AnsweringAgent[Answering Agent]
    Doc --> AnsweringAgent
    UserQ --> AnsweringAgent
    
    AnsweringAgent --> Answer[Final Answer]
    
    classDef component fill:#f9f,stroke:#333,stroke-width:2px
    classDef storage fill:#bbf,stroke:#333,stroke-width:2px
    classDef input fill:#bfb,stroke:#333,stroke-width:2px
    classDef output fill:#ffb,stroke:#333,stroke-width:2px
    
    class LLM1,RetrieverC,SelectionAgent,AnsweringAgent component
    class Doc,QdrantDB storage
    class UserQ input
    class Answer,Summaries output
