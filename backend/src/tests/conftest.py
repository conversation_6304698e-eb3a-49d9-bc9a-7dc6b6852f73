import uuid
from os import environ

from src import test_settings
from src.database import DatabaseSessionManager

environ["CRYSTAL_ROOF_API_KEY"] = "DEMO"
environ["IDEALPOSTCODES_API_KEY"] = "TEST"
environ["CLERK_PEM_PUBLIC_KEY"] = "TEST"
environ["CLERK_API_KEY"] = "TEST"
environ["CLERK_WEBHOOK_SECRET"] = "whsec_TEST"
environ["CLERK_PERMITTED_ORIGINS"] = "http://localhost:8000"
environ["AI_ENGINE_API_KEY"] = "TEST"
environ["ADMIN_API_KEY"] = "TEST_ADMIN_API_KEY"
environ["AI_ENGINE_API_BASE_URL"] = "http://localhost:8000"
environ["DOCUMENTS_S3_BUCKET_NAME"] = "test-bucket"
environ["SENDGRID_API_KEY"] = "TEST"
environ["SENDGRID_DEFAULT_FROM_EMAIL"] = "<EMAIL>"
environ["STAGE"] = "test"
environ["HUBSPOT_API_KEY"] = "hubspot-test"
environ["BACKEND_API_BASE_URL"] = "http://localhost:8000"
environ["GUEST_USER_TOKEN_SIGNING_KEY"] = "testSigningKey"
environ["BASE_URL"] = "http://localhost:8000"

SQLALCHEMY_DATABASE_URL = f"postgresql+psycopg://{test_settings.DATABASE_URL}"

from src.ai_dao import QdrantDAO
from typing import Any, Callable, AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from faker import Faker
from fastapi.testclient import TestClient
from sqlalchemy import text
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
    AsyncConnection,
)

from src.dependencies import get_sendgrid_service, get_qdrant_dao, get_db_session
from src.db_models import BaseModel
from src.db_models.chat import Chat
from src.db_models.message import Message, MessageType, SenderType
from src.db_models.job import Job
from src.db_models.project import Project
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.integrations.idealpostcodes import ResolvedAddress
from src.main import app
from src.services.source_links import SourceLinksService
from src.services.coordinates import CoordinateTransformer
from src.services.properties import PropertyService
from src.services.notifications import NotificationService

fake = Faker()


@pytest.fixture(scope="session", autouse=True)
def set_required_env_vars_for_imports():
    variables_to_set = {
        "ANTHROPIC_API_KEY": "dummy_anthropic_key_for_import",
        "GEMINI_API_KEY": "dummy_gemini_key_for_import",
        "PSE_API_KEY": "dummy_pse_key_for_import",
        "PSE_CX": "dummy_pse_cx_for_import",
        "IS_AWS": "true",
        "LANGSMITH_API_KEY": "dummy_langsmith_key_for_import",
        "TOGETHER_AI_API_KEY": "dummy_together_key_for_import",
        "AI_ENGINE_API_KEY": "dummy_ai_engine_key_for_import",
        "OPEN_AI_API_KEY": "dummy_ai_engine_key_for_import",
        "QDRANT_DB_URL": "dummy_ai_engine_key_for_import",
        "QDRANT_API_KEY": "dummy_ai_engine_key_for_import",
        "QDRANT_COLLECTION_NAME": "dummy_collection",
    }

    original_values = {}
    for key, value in variables_to_set.items():
        original_values[key] = environ.get(key)
        environ[key] = value
        # print(f"  Set {key}") # Optional: reduce verbosity

    print("Environment variables set for import.")


def override_dependency(dependency: Callable[..., Any], mocked_response: Any) -> None:
    app.dependency_overrides[dependency] = lambda: mocked_response


@pytest_asyncio.fixture(scope="session")
async def engine():
    """Create a template database once for all tests"""
    # Extract base URL for admin connections
    base_url = SQLALCHEMY_DATABASE_URL.rsplit("/", 1)[0]
    template_db_name = f"template_{uuid.uuid4().hex[:8]}"
    template_db_url = f"{base_url}/{template_db_name}"

    # Create template database with AUTOCOMMIT mode
    async with create_async_engine(f"{base_url}/postgres", isolation_level="AUTOCOMMIT").connect() as conn:
        await conn.execute(text(f"DROP DATABASE IF EXISTS {template_db_name}"))
        await conn.execute(text(f"CREATE DATABASE {template_db_name}"))

    # Setup schema in template database
    template_engine = create_async_engine(template_db_url)
    async with template_engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)
    await template_engine.dispose()

    yield template_db_name, base_url

    # Cleanup template database
    async with create_async_engine(f"{base_url}/postgres", isolation_level="AUTOCOMMIT").connect() as conn:
        await conn.execute(text(f"DROP DATABASE IF EXISTS {template_db_name}"))


@pytest_asyncio.fixture
async def create(engine) -> AsyncGenerator[AsyncConnection, None]:
    """Create a test database from template for each test"""
    template_db_name, base_url = engine
    test_db_name = f"test_{uuid.uuid4().hex[:8]}"
    test_db_url = f"{base_url}/{test_db_name}"

    # Create test database from template with AUTOCOMMIT
    async with create_async_engine(f"{base_url}/postgres", isolation_level="AUTOCOMMIT").connect() as conn:
        await conn.execute(text(f"CREATE DATABASE {test_db_name} TEMPLATE {template_db_name}"))

    # Connect to the test database
    test_engine = create_async_engine(test_db_url)
    async with test_engine.connect() as connection:
        # Store the test database URL for the client fixture
        connection.info["test_db_url"] = test_db_url
        yield connection

    # Close all connections before dropping
    await test_engine.dispose()

    # Drop test database with AUTOCOMMIT
    async with create_async_engine(f"{base_url}/postgres", isolation_level="AUTOCOMMIT").connect() as conn:
        await conn.execute(
            text(
                f"""
            SELECT pg_terminate_backend(pg_stat_activity.pid)
            FROM pg_stat_activity
            WHERE pg_stat_activity.datname = '{test_db_name}'
            AND pid <> pg_backend_pid()
        """
            )
        )

        # Now drop the test database
        await conn.execute(text(f"DROP DATABASE IF EXISTS {test_db_name}"))


@pytest_asyncio.fixture
async def async_db_session(create) -> AsyncGenerator[AsyncSession, None]:
    """Create a session using the connection"""
    async_session = AsyncSession(
        bind=create,
        expire_on_commit=False,
    )

    yield async_session
    await async_session.close()


@pytest.fixture
def client(create) -> Generator[TestClient, Any, None]:
    """Create a test client that uses the test database"""
    # Get the test database URL from the connection
    test_db_url = create.info["test_db_url"]

    # Create a test database session manager
    test_session_manager = DatabaseSessionManager(test_db_url)

    # Store the original dependency function
    original_get_db = app.dependency_overrides.get(get_db_session, None)

    # Override the database session dependency
    async def override_get_db_session():
        async with test_session_manager.session() as session:
            yield session

    app.dependency_overrides[get_db_session] = override_get_db_session
    app.dependency_overrides[get_sendgrid_service] = lambda: AsyncMock()
    app.dependency_overrides[get_qdrant_dao] = lambda: AsyncMock()

    with TestClient(app) as _client:
        yield _client

    # Restore original dependency
    if original_get_db:
        app.dependency_overrides[get_db_session] = original_get_db
    else:
        del app.dependency_overrides[get_db_session]
    app.dependency_overrides = {}


@pytest_asyncio.fixture
async def async_db_session(create) -> AsyncGenerator[AsyncSession, None]:
    """Create a session using the connection"""
    async_session = AsyncSession(
        bind=create,
        expire_on_commit=False,
    )

    yield async_session
    await async_session.close()


@pytest_asyncio.fixture
async def property_service(async_db_session: AsyncSession, source_link_service: SourceLinksService):
    coordinate_transformer = CoordinateTransformer()
    land_registry_api_mock = AsyncMock()
    land_registry_api_mock.get_property_type.return_value = "flat-maisonette"
    idealpostcodes_api_mock = AsyncMock()
    idealpostcodes_api_mock.resolve_address.return_value = (
        ResolvedAddress(
            id="paf_23833521",
            line_1="22 Eardley Crescent",
            line_2="",
            line_3="",
            post_town="London",
            postcode="SW5 9JZ",
            county="London",
            country="England",
            uprn="217025320",
            udprn=23833521,
            thoroughfare="Eardley Crescent",
            building_number="22",
            building_name="",
            sub_building_name="",
            latitude=51.4885502,
            longitude=-0.1953659,
            umprn=23833522,
        ),
        {
            "postcode": "SW5 9JZ",
            "postcode_inward": "9JZ",
            "postcode_outward": "SW5",
            "post_town": "London",
            "dependant_locality": "",
            "double_dependant_locality": "",
            "thoroughfare": "Eardley Crescent",
            "dependant_thoroughfare": "",
            "building_number": "22",
            "building_name": "",
            "sub_building_name": "",
            "po_box": "",
            "department_name": "",
            "organisation_name": "",
            "udprn": 23833521,
            "postcode_type": "S",
            "su_organisation_indicator": "",
            "delivery_point_suffix": "1J",
            "line_1": "22 Eardley Crescent",
            "line_2": "",
            "line_3": "",
            "premise": "22",
            "longitude": -0.1953659,
            "latitude": 51.4885502,
            "eastings": 525390,
            "northings": 178166,
            "country": "England",
            "traditional_county": "Greater London",
            "administrative_county": "",
            "postal_county": "London",
            "county": "London",
            "district": "Kensington and Chelsea",
            "ward": "Earl's Court",
            "uprn": "217025320",
            "id": "paf_23833521",
            "country_iso": "GBR",
            "country_iso_2": "GB",
            "county_code": "",
            "language": "en",
            "umprn": "23833522",
            "dataset": "paf",
        },
    )
    return PropertyService(
        async_db_session,
        coordinate_transformer,
        idealpostcodes_api=idealpostcodes_api_mock,
        land_registry_api=land_registry_api_mock,
        source_link_service=source_link_service,
    )


@pytest.fixture
async def notification_service(async_db_session: AsyncSession):
    return NotificationService(async_db_session)


@pytest.fixture
async def source_link_service(async_db_session: AsyncSession, notification_service: NotificationService):
    return SourceLinksService(async_db_session, notification_service)


@pytest.fixture
async def user(async_db_session: AsyncSession):
    user = User(
        email=fake.email(),
        clerkId=str(fake.uuid4()),
        firstName="User",
        lastName="One",
    )
    async_db_session.add(user)
    await async_db_session.commit()
    await async_db_session.refresh(user)
    return user


@pytest.fixture
async def chat(user: User, async_db_session: AsyncSession):
    chat = Chat(title=fake.word(), userId=user.id)
    async_db_session.add(chat)
    await async_db_session.commit()
    await async_db_session.refresh(chat)

    message = Message(
        content="Hey Alfie!",
        type=MessageType.TEXT,
        senderType=SenderType.USER,
        chatId=chat.id,
        userId=user.id,
    )
    message_ai = Message(
        content="How can I help you?",
        type=MessageType.TEXT,
        senderType=SenderType.SYSTEM,
        chatId=chat.id,
        userId=user.id,
    )
    async_db_session.add(message)
    async_db_session.add(message_ai)
    await async_db_session.commit()
    return chat


@pytest.fixture
async def project(async_db_session: AsyncSession, user: User, chat: Chat):
    project = Project(
        id=1,
        headline="Dummy Project",
        subTitle="Dummy Subtitle",
        details="Some details about the dummy project",
        urgency="High",
        chatId=chat.id,
        userId=user.id,
    )
    async_db_session.add(project)
    await async_db_session.commit()
    return project


@pytest.fixture
async def job(async_db_session: AsyncSession, user: User, chat: Chat, project: Project):
    job = Job(
        id=1,
        headline="Test Headline 1",
        subTitle="Test Subtitle 1",
        details="Test details 1",
        urgency="Medium",
        availability="Weekdays",
        status="created",
        projectId=project.id,
        chatId=chat.id,
        userId=user.id,
        hubspotId="old_hubspot_id",
    )
    async_db_session.add(job)
    await async_db_session.commit()
    await async_db_session.refresh(job)
    return job


@pytest.fixture
async def user_created_todo(async_db_session: AsyncSession, user: User):
    todo = Todo(
        name="Test Todo User Created",
        description="This is a test todo user created",
        userId=user.id,
        type=TodoType.userCreated,
    )
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.fixture
async def system_created_todo(async_db_session: AsyncSession, user: User):
    todo = Todo(
        name="Test Todo System Created",
        description="This is a test todo system created",
        userId=user.id,
        type=TodoType.systemCreated,
    )
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.fixture
async def system_created_user_accepted_todo(async_db_session: AsyncSession, user: User):
    todo = Todo(
        name="Test Todo System Created User Accepted",
        description="This is a test todo system created user accepted",
        userId=user.id,
        type=TodoType.systemCreatedUserAccepted,
    )
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.fixture
async def system_created_user_rejected_todo(async_db_session: AsyncSession, user: User):
    todo = Todo(
        name="Test Todo System Created User Rejected",
        description="This is a test todo system created user rejected",
        userId=user.id,
        type=TodoType.systemCreatedUserRejected,
    )
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.fixture
def mock_qdrant_dao():
    mock = MagicMock(spec=QdrantDAO)
    return mock
