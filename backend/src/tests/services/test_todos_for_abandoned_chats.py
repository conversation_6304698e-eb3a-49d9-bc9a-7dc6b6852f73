import pytest
from unittest.mock import AsyncMock, MagicMock

from src.services.todos_for_abandoned_chats import TodosForAbandonedChatsService


@pytest.mark.asyncio
async def test_create_todos_for_abandoned_chats_calls_services():
    mock_db = MagicMock()
    mock_chat_service = AsyncMock()
    mock_todo_service = AsyncMock()

    fake_chats = [MagicMock(), MagicMock()]
    mock_chat_service.get_abandoned_chats.return_value = fake_chats

    service = TodosForAbandonedChatsService(
        db=mock_db,
        chat_service=mock_chat_service,
        todo_service=mock_todo_service,
    )

    await service.create_todos_for_abandoned_chats()

    mock_chat_service.get_abandoned_chats.assert_awaited_once_with(limit=None)
    assert mock_todo_service.create_todos_for_chat.await_count == len(fake_chats)
    for chat in fake_chats:
        mock_todo_service.create_todos_for_chat.assert_any_await(chat)


@pytest.mark.asyncio
async def test_create_todos_for_abandoned_chats_with_limit():
    mock_db = MagicMock()
    mock_chat_service = AsyncMock()
    mock_todo_service = AsyncMock()

    fake_chats = [MagicMock()] * 5
    mock_chat_service.get_abandoned_chats.return_value = fake_chats

    service = TodosForAbandonedChatsService(
        db=mock_db,
        chat_service=mock_chat_service,
        todo_service=mock_todo_service,
        limit=5,
    )

    await service.create_todos_for_abandoned_chats()

    mock_chat_service.get_abandoned_chats.assert_awaited_once_with(limit=5)
    assert mock_todo_service.create_todos_for_chat.await_count == 5


@pytest.mark.asyncio
async def test_create_todos_for_abandoned_chats_empty():
    mock_db = MagicMock()
    mock_chat_service = AsyncMock()
    mock_todo_service = AsyncMock()

    mock_chat_service.get_abandoned_chats.return_value = []
    service = TodosForAbandonedChatsService(
        db=mock_db,
        chat_service=mock_chat_service,
        todo_service=mock_todo_service,
    )

    await service.create_todos_for_abandoned_chats()

    mock_chat_service.get_abandoned_chats.assert_awaited_once()
    mock_todo_service.create_todos_for_chat.assert_not_awaited()
