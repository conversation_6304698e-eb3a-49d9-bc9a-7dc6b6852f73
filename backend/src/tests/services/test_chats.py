from datetime import datetime, timedelta
from unittest.mock import Async<PERSON>ock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.chat import Chat
from src.db_models.job import Job, JobStatus
from src.db_models.message import Message, MessageType, SenderType
from src.db_models.user import User
from src.services.chats import ChatService


@pytest.fixture
async def abandoned_chat_without_job(user: User, async_db_session: AsyncSession):
    chat = Chat(
        title="Abandoned",
        userId=user.id,
        created_at=datetime.utcnow() - timedelta(hours=25),
    )
    async_db_session.add(chat)
    await async_db_session.commit()
    await async_db_session.refresh(chat)

    message = Message(
        content="content",
        type=MessageType.TEXT,
        senderType=SenderType.USER,
        chatId=chat.id,
        userId=user.id,
        timestamp=datetime.utcnow() - timedelta(hours=25),
    )
    async_db_session.add(message)
    await async_db_session.commit()
    return chat


@pytest.fixture
async def abandoned_chat_with_job(user: User, job: Job, async_db_session: AsyncSession):
    chat = Chat(
        title="Abandoned",
        userId=user.id,
        created_at=datetime.utcnow() - timedelta(hours=25),
    )
    async_db_session.add(chat)
    await async_db_session.commit()
    await async_db_session.refresh(chat)

    message = Message(
        content="content",
        type=MessageType.TEXT,
        senderType=SenderType.USER,
        chatId=chat.id,
        userId=user.id,
        timestamp=datetime.utcnow() - timedelta(hours=25),
    )
    async_db_session.add(message)
    job.chatId = chat.id
    job.status = JobStatus.USER_ACCEPTED
    async_db_session.add(job)
    await async_db_session.commit()
    return chat


@pytest.mark.asyncio
async def test_get_abandoned_chats(
    async_db_session,
    abandoned_chat_without_job: Chat,
    chat: Chat,
    abandoned_chat_with_job: Chat,
):
    service = ChatService(async_db_session, AsyncMock(), abandoned_chat_after_minutes=60)
    result = await service.get_abandoned_chats(limit=5)
    assert len(result) == 1
    assert abandoned_chat_without_job in result


@pytest.mark.asyncio
async def test_mark_as_processed_as_abandoned_sets_flag_and_commits(async_db_session, chat: Chat):
    service = ChatService(async_db_session, AsyncMock(), abandoned_chat_after_minutes=60)
    await service.mark_as_processed_as_abandoned(chat)

    await async_db_session.refresh(chat)
    assert chat.was_processed_as_abandoned is True
