import datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.chat import Chat
from src.db_models.document import DocumentStatusType, Document
from src.db_models.source_link import SourceType
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.integrations.aiengine import AIEngineAP<PERSON>
from src.schemas import FindTodo, LinkedEntity, LinkedEntityType, TodoCreate, TodoUpdate, TodoAiCreate, TodoAiUpdate
from src.services.entity_links import EntityLinksService
from src.services.source_links import SourceLinksService
from src.services.notifications import NotificationService
from src.services.todos import TodoService, TodoDoesNotExist
from src.tests.matchers import model_dump


@pytest.fixture
def notification_service(async_db_session: AsyncSession):
    return NotificationService(async_db_session)


@pytest.fixture
def source_link_service(async_db_session: AsyncSession, notification_service: NotificationService):
    return SourceLinksService(async_db_session, notification_service)


@pytest.fixture
def ai_engine_api(async_db_session: AsyncSession):
    return MagicMock(spec=AIEngineAPI)


@pytest.fixture
def entity_link_service(async_db_session: AsyncSession):
    return EntityLinksService(db=async_db_session)


@pytest.fixture
async def system_created_system_expired_todo(async_db_session: AsyncSession, user: User):
    todo = Todo(
        name="Test Todo System Created System Expired",
        description="This is a test todo system created system expired",
        userId=user.id,
        type=TodoType.systemCreatedSystemExpired,
    )
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.fixture
async def another_user(async_db_session: AsyncSession):
    user = User(
        email="<EMAIL>",
        clerkId="clerk_user_id_2",
        firstName="User",
        lastName="Two",
    )
    async_db_session.add(user)
    await async_db_session.commit()
    await async_db_session.refresh(user)
    return user


@pytest.fixture
async def document(async_db_session: AsyncSession, user: User):
    document = Document(
        id=1,
        type="test",
        source="system",
        s3Key="test-key",
        s3Bucket="test-bucket",
        fileExtension="pdf",
        sizeInKiloBytes=100,
        originalFileName="test.pdf",
        status=DocumentStatusType.processingCompleted,
        userId=user.id,
    )
    async_db_session.add(document)
    await async_db_session.commit()
    return document


@pytest.fixture
def todo_service(
    async_db_session: AsyncSession,
    source_link_service: SourceLinksService,
    ai_engine_api: AIEngineAPI,
    entity_link_service: EntityLinksService,
):
    return TodoService(
        async_db_session, source_link_service, ai_engine_api, entity_link_service, expiration_after_minutes=60
    )


@pytest.mark.asyncio
async def test_create_todo_for_user(async_db_session: AsyncSession, todo_service: TodoService, user: User):
    todo_data = TodoCreate(
        name="Some todo",
        description="What needs to be done?",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user)

    assert todo is not None
    assert todo.name == "Some todo"
    assert todo.description == "What needs to be done?"
    assert todo.dueDate == datetime.datetime(2025, 1, 15)

    query = todo_service.get_todos_for_user_query(user)
    result = await async_db_session.execute(query)
    todos = result.scalars().all()

    assert len(todos) == 1
    assert todos[0].id == todo.id
    assert todos[0].name == todo.name
    assert todos[0].type == TodoType.userCreated


@pytest.mark.asyncio
async def test_create_todo_for_ai(async_db_session: AsyncSession, todo_service: TodoService, user: User, document):
    todo_data = TodoAiCreate(
        todo=TodoCreate(
            name="Some todo",
            description="What needs to be done?",
            dueDate=datetime.datetime(2025, 1, 15),
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    todo = await todo_service.create_todo_by_ai(todo_data, user)

    assert todo is not None
    assert todo.name == "Some todo"
    assert todo.description == "What needs to be done?"
    assert todo.dueDate == datetime.datetime(2025, 1, 15)

    query = todo_service.get_todos_for_user_query(user)
    result = await async_db_session.execute(query)
    todos = result.scalars().all()

    assert len(todos) == 1
    assert todos[0].id == todo.id
    assert todos[0].name == todo.name
    assert todos[0].type == TodoType.systemCreated

    source_links = await todo_service._source_link_service.get_source_links(todos[0])
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "document"}},
    ]

    entity_links = await todo_service._entity_link_service.get_links(
        LinkedEntity(id=todo.id, entityType=LinkedEntityType.todos), user.id
    )
    assert len(entity_links) == 0


@pytest.mark.asyncio
async def test_create_todo_for_ai_with_linked_chat(
    async_db_session: AsyncSession, todo_service: TodoService, user: User, chat: Chat
):
    todo_data = TodoAiCreate(
        todo=TodoCreate(
            name="Some todo",
            description="What needs to be done?",
            dueDate=datetime.datetime(2025, 1, 15),
        ),
        srcType=SourceType.chat,
        srcId=chat.id,
    )

    todo = await todo_service.create_todo_by_ai(todo_data, user)

    assert todo is not None
    assert todo.name == "Some todo"
    assert todo.description == "What needs to be done?"
    assert todo.dueDate == datetime.datetime(2025, 1, 15)

    query = todo_service.get_todos_for_user_query(user)
    result = await async_db_session.execute(query)
    todos = result.scalars().all()

    assert len(todos) == 1
    assert todos[0].id == todo.id
    assert todos[0].name == todo.name
    assert todos[0].type == TodoType.systemCreated

    entity_links = await todo_service._entity_link_service.get_links(
        LinkedEntity(id=todo.id, entityType=LinkedEntityType.todos), user.id
    )
    assert len(entity_links) == 1
    assert entity_links[0].entityType == LinkedEntityType.chats
    assert entity_links[0].id == chat.id


@pytest.mark.asyncio
async def test_update_todo_by_user(async_db_session: AsyncSession, todo_service: TodoService, user: User):
    todo_data = TodoCreate(
        name="Some todo",
        description="What needs to be done?",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user)
    await async_db_session.commit()

    update_data = TodoUpdate(
        name="Updated todo",
        description="Updated description",
        dueDate=datetime.datetime(2025, 2, 15),
    )

    updated_todo = await todo_service.update_todo_by_user(todo.id, update_data, user)

    assert updated_todo is not None
    assert updated_todo.name == "Updated todo"
    assert updated_todo.description == "Updated description"
    assert updated_todo.dueDate == datetime.datetime(2025, 2, 15)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert todo.type == TodoType.userCreated

    source_links = await todo_service._source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {
            "destinationField": "description",
            "source": {"id": 1, "srcType": "userInput"},
        },
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]


@pytest.mark.asyncio
async def test_create_todo_by_ai_update_by_user(
    async_db_session: AsyncSession, todo_service: TodoService, user: User, document
):
    todo_data = TodoAiCreate(
        todo=TodoCreate(
            name="Some todo",
            description="What needs to be done?",
            dueDate=datetime.datetime(2025, 1, 15),
        ),
        srcType=SourceType.document,
        srcId=document.id,
    )

    todo = await todo_service.create_todo_by_ai(todo_data, user)

    update_data = TodoUpdate(
        name="Updated todo",
    )

    updated_todo = await todo_service.update_todo_by_user(todo.id, update_data, user)

    assert updated_todo is not None
    assert updated_todo.name == "Updated todo"
    assert updated_todo.description == "What needs to be done?"
    assert updated_todo.dueDate == datetime.datetime(2025, 1, 15)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert todo.type == TodoType.systemCreated

    source_links = await todo_service._source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]


@pytest.mark.asyncio
async def test_update_todo_by_different_user_raises_exception(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user: User,
    another_user: User,
):
    todo_data = TodoCreate(
        name="Some todo",
        description="What needs to be done?",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user)
    await async_db_session.commit()

    update_data = TodoUpdate(
        name="Updated todo",
        description="Updated description",
        dueDate=datetime.datetime(2025, 2, 15),
    )

    with pytest.raises(TodoDoesNotExist, match="The ToDo does not exist."):
        await todo_service.update_todo_by_user(todo.id, update_data, another_user)


@pytest.mark.asyncio
async def test_delete_todo_by_user_success(async_db_session: AsyncSession, todo_service: TodoService, user: User):
    todo_data = TodoCreate(
        name="Todo to delete",
        description="This will be deleted",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user)
    await async_db_session.commit()

    # Verify todo exists before deletion and has correct type
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result is not None
    assert result.type == TodoType.userCreated

    # Delete the todo
    await todo_service.delete_todo_by_user(todo.id, user)
    await async_db_session.commit()

    # Verify todo no longer exists in database
    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result is None


@pytest.mark.asyncio
async def test_delete_todo_by_different_user_raises_exception(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user: User,
    another_user: User,
):
    todo_data = TodoCreate(
        name="Todo to delete",
        description="This should not be deleted by another_user",
        dueDate=datetime.datetime(2025, 1, 15),
    )

    todo = await todo_service.create_todo_by_user(todo_data, user)
    await async_db_session.commit()

    with pytest.raises(TodoDoesNotExist, match="The ToDo does not exist."):
        await todo_service.delete_todo_by_user(todo.id, another_user)

    result = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    assert result is not None
    assert result.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_update_todo_by_ai_cannot_update_fields_set_by_user(
    async_db_session: AsyncSession, todo_service: TodoService, user: User, document
):
    source_link_service = todo_service._source_link_service
    todo_data = TodoCreate(
        name="Some todo",
        dueDate=datetime.datetime(2025, 1, 15),
    )
    todo = await todo_service.create_todo_by_user(todo_data, user)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]
    assert todo.type == TodoType.userCreated

    update_data = TodoAiUpdate(
        todo=TodoUpdate(name="Updated todo", description="Updated description"),
        srcType=SourceType.document,
        srcId=document.id,
    )

    updated_todo = await todo_service.update_todo_by_ai(todo.id, update_data)

    assert updated_todo is not None
    assert updated_todo.name == "Some todo"  # note the field set initially by the user hasn't been updated
    assert updated_todo.description == "Updated description"
    assert updated_todo.dueDate == datetime.datetime(2025, 1, 15)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {"destinationField": "description", "source": {"id": 1, "srcType": "document"}},
        {"destinationField": "dueDate", "source": {"id": 1, "srcType": "userInput"}},
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]

    # Verify that type field remains unchanged after AI update
    assert todo.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_update_todo_by_ai_with_chat_source(
    async_db_session: AsyncSession, todo_service: TodoService, user: User
):
    source_link_service = todo_service._source_link_service
    todo_data = TodoCreate(
        name="Some todo",
    )
    todo = await todo_service.create_todo_by_user(todo_data, user)

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [{"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}}]
    assert todo.type == TodoType.userCreated

    chat = Chat(id=1, title="Planning Chat", userId=user.id)
    async_db_session.add(chat)
    await async_db_session.commit()

    update_data = TodoAiUpdate(
        todo=TodoUpdate(
            description="Updated description via chat",
        ),
        srcType=SourceType.chat,
        srcId=chat.id,
    )

    updated_todo = await todo_service.update_todo_by_ai(todo.id, update_data)

    assert updated_todo is not None
    assert updated_todo.name == "Some todo"
    assert updated_todo.description == "Updated description via chat"

    todo = await async_db_session.scalar(select(Todo).where(Todo.id == todo.id))
    source_links = await source_link_service.get_source_links(todo)
    assert model_dump(source_links) == [
        {
            "destinationField": "description",
            "source": {"id": 1, "srcType": "chat", "title": "Planning Chat"},
        },
        {"destinationField": "name", "source": {"id": 1, "srcType": "userInput"}},
    ]

    # Verify that type field remains unchanged after AI update
    assert todo.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_accept_todo_by_user_success(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user: User,
    system_created_todo: Todo,
):
    result = await async_db_session.scalar(select(Todo).where(Todo.id == system_created_todo.id))
    assert result.type == TodoType.systemCreated

    await todo_service.accept_todo_by_user(system_created_todo.id, user)

    # Verify the todo type has been changed to systemCreatedUserAccepted
    result = await async_db_session.scalar(select(Todo).where(Todo.id == system_created_todo.id))
    assert result.type == TodoType.systemCreatedUserAccepted

    # Verify that source links have been updated to include user acceptance
    source_links = await todo_service._source_link_service.get_source_links(result)
    type_source_links = [sl for sl in source_links if sl.destinationField == "type"]
    assert len(type_source_links) > 0
    assert any(sl.source.srcType == "userInput" and sl.source.id == user.id for sl in type_source_links)


@pytest.mark.asyncio
async def test_accept_todo_by_user_non_system_created_raises_exception(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    user: User,
):
    result = await async_db_session.scalar(select(Todo).where(Todo.id == user_created_todo.id))
    assert result.type == TodoType.userCreated

    with pytest.raises(TodoDoesNotExist):
        await todo_service.accept_todo_by_user(user_created_todo.id, user)


@pytest.mark.asyncio
async def test_accept_todo_by_different_user_raises_exception(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    user: User,
):
    with pytest.raises(TodoDoesNotExist):
        await todo_service.accept_todo_by_user(user_created_todo.id, user)

    result = await async_db_session.scalar(select(Todo).where(Todo.id == user_created_todo.id))
    assert result.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_reject_todo_by_user_success(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    system_created_todo: Todo,
    user: User,
):
    await todo_service.reject_todo_by_user(system_created_todo.id, user)

    result = await async_db_session.scalar(select(Todo).where(Todo.id == system_created_todo.id))
    assert result.type == TodoType.systemCreatedUserRejected

    source_links = await todo_service._source_link_service.get_source_links(result)
    type_source_links = [sl for sl in source_links if sl.destinationField == "type"]
    assert len(type_source_links) > 0
    assert any(sl.source.srcType == "userInput" and sl.source.id == user.id for sl in type_source_links)


@pytest.mark.asyncio
async def test_reject_todo_by_user_non_system_created_raises_exception(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    user: User,
):
    with pytest.raises(TodoDoesNotExist):
        await todo_service.reject_todo_by_user(user_created_todo.id, user)


@pytest.mark.asyncio
async def test_reject_todo_by_different_user_raises_exception(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    user: User,
):
    with pytest.raises(TodoDoesNotExist):
        await todo_service.reject_todo_by_user(user_created_todo.id, user)

    result = await async_db_session.scalar(select(Todo).where(Todo.id == user_created_todo.id))
    assert result.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_get_todo_success(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    user: User,
):
    retrieved_todo = await todo_service.get_todo(user_created_todo.id, user)

    assert retrieved_todo is not None
    assert retrieved_todo.id == user_created_todo.id
    assert retrieved_todo.name == "Test Todo User Created"
    assert retrieved_todo.description == "This is a test todo user created"
    assert retrieved_todo.dueDate is None
    assert retrieved_todo.type == TodoType.userCreated

    assert hasattr(retrieved_todo, "sources")
    assert retrieved_todo.sources is not None
    assert model_dump(retrieved_todo.sources) == []


@pytest.mark.asyncio
async def test_get_todo_different_user_raises_exception(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    another_user: User,
):
    with pytest.raises(TodoDoesNotExist):
        await todo_service.get_todo(user_created_todo.id, another_user)


@pytest.mark.asyncio
async def test_get_todo_nonexistent_todo_raises_exception(
    async_db_session: AsyncSession, todo_service: TodoService, user: User
):
    with pytest.raises(TodoDoesNotExist):
        await todo_service.get_todo(999999, user)


@pytest.mark.asyncio
async def test_get_todo_with_ai_created_todo(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    system_created_todo: Todo,
    user: User,
):
    retrieved_todo = await todo_service.get_todo(system_created_todo.id, user)
    assert retrieved_todo is not None
    assert retrieved_todo.id == system_created_todo.id
    assert retrieved_todo.name == "Test Todo System Created"
    assert retrieved_todo.description == "This is a test todo system created"
    assert retrieved_todo.dueDate is None
    assert retrieved_todo.type == TodoType.systemCreated

    assert hasattr(retrieved_todo, "sources")
    assert retrieved_todo.sources is not None
    assert model_dump(retrieved_todo.sources) == []


@pytest.mark.asyncio
async def test_create_todos_for_chat_calls_ai_engine_and_creates_todos(
    async_db_session: AsyncSession, todo_service: TodoService, chat
):
    fake_todos = [
        FindTodo(title="T1", description="D1", dueDate=None),
        FindTodo(title="T2", description="D2", dueDate=datetime.datetime(2025, 7, 1)),
    ]
    todo_service._ai_engine_service.find_todos = AsyncMock(return_value=fake_todos)
    assert (await async_db_session.execute(select(func.count()).select_from(Todo))).scalar_one() == 0
    await todo_service.create_todos_for_chat(chat)

    assert (await async_db_session.execute(select(func.count()).select_from(Todo))).scalar_one() == 2
    result = await async_db_session.execute(select(Todo).order_by(Todo.created_at))
    todo1, todo2 = result.scalars().all()
    assert todo1.name == "T1"
    assert todo1.description == "D1"
    assert todo1.dueDate is None

    assert todo2.name == "T2"
    assert todo2.description == "D2"
    assert todo2.dueDate == datetime.datetime(2025, 7, 1)


@pytest.mark.asyncio
async def test_get_todos_for_user_query(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    user: User,
):
    assert [user_created_todo] == (
        await async_db_session.execute(todo_service.get_todos_for_user_query(user))
    ).scalars().all()


@pytest.mark.asyncio
async def test_get_suggested_todos_query(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    system_created_todo: Todo,
    system_created_user_accepted_todo: Todo,
    system_created_user_rejected_todo: Todo,
    user: User,
):
    suggested_todos = (await async_db_session.execute(todo_service.get_suggested_todos_query(user))).scalars().all()
    assert len(suggested_todos) == 1
    assert system_created_todo in suggested_todos


@pytest.mark.asyncio
async def test_get_accepted_todos_query(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user_created_todo: Todo,
    system_created_todo: Todo,
    system_created_user_accepted_todo: Todo,
    system_created_user_rejected_todo: Todo,
    system_created_system_expired_todo: Todo,
    user: User,
):
    accepted_todos = (await async_db_session.execute(todo_service.get_accepted_todos_query(user))).scalars().all()
    assert len(accepted_todos) == 2
    assert user_created_todo in accepted_todos
    assert system_created_user_accepted_todo in accepted_todos
    assert system_created_todo not in accepted_todos
    assert system_created_user_rejected_todo not in accepted_todos
    assert system_created_system_expired_todo not in accepted_todos


@pytest.mark.asyncio
async def test_expire_todos_expires_old_system_created_todos(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user: User,
):
    # Create a system created todo that is older than the expiration threshold (60 minutes)
    old_todo = Todo(
        name="Old System Todo",
        description="This should be expired",
        type=TodoType.systemCreated,
        userId=user.id,
        created_at=datetime.datetime.utcnow() - datetime.timedelta(minutes=90),  # 90 minutes ago
    )
    async_db_session.add(old_todo)

    # Create a recent system created todo that should not be expired
    recent_todo = Todo(
        name="Recent System Todo",
        description="This should not be expired",
        type=TodoType.systemCreated,
        userId=user.id,
        created_at=datetime.datetime.utcnow() - datetime.timedelta(minutes=30),  # 30 minutes ago
    )
    async_db_session.add(recent_todo)

    # Create a user created todo that should not be affected
    user_todo = Todo(
        name="User Todo",
        description="This should not be affected",
        type=TodoType.userCreated,
        userId=user.id,
        created_at=datetime.datetime.utcnow() - datetime.timedelta(minutes=90),  # 90 minutes ago
    )
    async_db_session.add(user_todo)

    await async_db_session.commit()

    # Run expire_todos
    expired_count = await todo_service.expire_todos()

    # Should have expired exactly 1 todo
    assert expired_count == 1

    # Refresh the todos from database
    await async_db_session.refresh(old_todo)
    await async_db_session.refresh(recent_todo)
    await async_db_session.refresh(user_todo)

    # Check that the old system created todo was expired
    assert old_todo.type == TodoType.systemCreatedSystemExpired

    # Check that the recent system created todo was not expired
    assert recent_todo.type == TodoType.systemCreated

    # Check that the user created todo was not affected
    assert user_todo.type == TodoType.userCreated


@pytest.mark.asyncio
async def test_expire_todos_returns_zero_when_no_todos_to_expire(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user: User,
):
    # Create only recent todos that should not be expired
    recent_todo = Todo(
        name="Recent System Todo",
        description="This should not be expired",
        type=TodoType.systemCreated,
        userId=user.id,
        created_at=datetime.datetime.utcnow() - datetime.timedelta(minutes=30),  # 30 minutes ago
    )
    async_db_session.add(recent_todo)
    await async_db_session.commit()

    # Run expire_todos
    expired_count = await todo_service.expire_todos()

    # Should have expired 0 todos
    assert expired_count == 0

    # Check that the todo was not expired
    await async_db_session.refresh(recent_todo)
    assert recent_todo.type == TodoType.systemCreated


@pytest.mark.asyncio
async def test_expire_todos_only_affects_system_created_status(
    async_db_session: AsyncSession,
    todo_service: TodoService,
    user: User,
):
    # Create todos with different statuses, all old enough to be expired
    old_time = datetime.datetime.utcnow() - datetime.timedelta(minutes=90)

    system_created_todo = Todo(
        name="System Created Todo", type=TodoType.systemCreated, userId=user.id, created_at=old_time
    )

    user_created_todo = Todo(name="User Created Todo", type=TodoType.userCreated, userId=user.id, created_at=old_time)

    accepted_todo = Todo(
        name="Accepted Todo", type=TodoType.systemCreatedUserAccepted, userId=user.id, created_at=old_time
    )

    rejected_todo = Todo(
        name="Rejected Todo", type=TodoType.systemCreatedUserRejected, userId=user.id, created_at=old_time
    )

    async_db_session.add_all([system_created_todo, user_created_todo, accepted_todo, rejected_todo])
    await async_db_session.commit()

    # Run expire_todos
    expired_count = await todo_service.expire_todos()

    # Should have expired exactly 1 todo (only the systemCreated one)
    assert expired_count == 1

    # Refresh all todos
    await async_db_session.refresh(system_created_todo)
    await async_db_session.refresh(user_created_todo)
    await async_db_session.refresh(accepted_todo)
    await async_db_session.refresh(rejected_todo)

    # Check statuses
    assert system_created_todo.type == TodoType.systemCreatedSystemExpired
    assert user_created_todo.type == TodoType.userCreated
    assert accepted_todo.type == TodoType.systemCreatedUserAccepted
    assert rejected_todo.type == TodoType.systemCreatedUserRejected
