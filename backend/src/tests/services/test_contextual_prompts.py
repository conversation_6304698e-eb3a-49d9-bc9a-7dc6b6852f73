import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.contextual_prompt import ContextualPrompt, ContextualPromptType
from src.db_models.entity_link import EntityLink
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.schemas import LinkedEntity, LinkedEntityType
from src.services.contextual_prompts import ContextualPromptService, ContextualPromptDoesNotExist
from src.services.entity_links import EntityLinksService


@pytest.fixture
def entity_link_service(async_db_session: AsyncSession):
    return EntityLinksService(db=async_db_session)


@pytest.fixture
def contextual_prompt_service(async_db_session: AsyncSession, entity_link_service: EntityLinksService):
    return ContextualPromptService(async_db_session, entity_link_service)


@pytest.fixture
async def todo(async_db_session: AsyncSession, user: User):
    todo = Todo(
        name="Test Todo",
        description="Test Description",
        userId=user.id,
        type=TodoType.userCreated,
    )
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.mark.asyncio
async def test_create_contextual_prompts(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    prompts_text = ["Prompt 1", "Prompt 2", "Prompt 3"]
    prompt_type = ContextualPromptType.todoPrompt

    created_prompts = await contextual_prompt_service.create_contextual_prompts(
        prompts=prompts_text,
        todo_id=todo.id,
        prompt_type=prompt_type,
        user_id=user.id,
    )

    assert len(created_prompts) == 3
    for i, prompt in enumerate(created_prompts):
        assert prompt.prompt == prompts_text[i]
        assert prompt.type == prompt_type

    # Verify prompts are in database
    db_prompts = await async_db_session.scalars(select(ContextualPrompt))
    db_prompts_list = list(db_prompts)
    assert len(db_prompts_list) == 3

    # Verify entity links are created
    links = await async_db_session.scalars(
        select(EntityLink).where(
            EntityLink.typeB == LinkedEntityType.todos.value,
            EntityLink.idB == todo.id,
        )
    )
    links_list = list(links)
    assert len(links_list) == 3
    for link in links_list:
        assert link.typeA == LinkedEntityType.contextual_prompts.value


@pytest.mark.asyncio
async def test_create_contextual_prompts_todo_not_exist(
    contextual_prompt_service: ContextualPromptService,
):
    prompts_text = ["Prompt 1"]
    prompt_type = ContextualPromptType.todoPrompt
    non_existent_todo_id = 99999

    with pytest.raises(ContextualPromptDoesNotExist) as exc_info:
        await contextual_prompt_service.create_contextual_prompts(
            prompts=prompts_text,
            todo_id=non_existent_todo_id,
            prompt_type=prompt_type,
            user_id=1,
        )
    assert f"Todo with id {non_existent_todo_id} does not exist" in str(exc_info.value)


@pytest.mark.asyncio
async def test_get_contextual_prompts_for_todo(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    # Create some prompts
    prompts_text = ["Prompt A", "Prompt B"]
    prompt_type = ContextualPromptType.mainChatPrompt

    await contextual_prompt_service.create_contextual_prompts(
        prompts=prompts_text,
        todo_id=todo.id,
        prompt_type=prompt_type,
        user_id=user.id,
    )

    # Get prompts for todo
    retrieved_prompts = await contextual_prompt_service.get_contextual_prompts_for_todo(todo.id, user.id)

    assert len(retrieved_prompts) == 2
    retrieved_prompts_text = [p.prompt for p in retrieved_prompts]
    assert "Prompt A" in retrieved_prompts_text
    assert "Prompt B" in retrieved_prompts_text


@pytest.mark.asyncio
async def test_get_contextual_prompts_for_todo_no_prompts(
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    # Get prompts for todo with no prompts
    retrieved_prompts = await contextual_prompt_service.get_contextual_prompts_for_todo(todo.id, user.id)
    assert retrieved_prompts == []


@pytest.mark.asyncio
async def test_delete_contextual_prompts_for_todo(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    # Create some prompts
    prompts_text = ["Delete me 1", "Delete me 2", "Delete me 3"]
    prompt_type = ContextualPromptType.todoPrompt

    created_prompts = await contextual_prompt_service.create_contextual_prompts(
        prompts=prompts_text,
        todo_id=todo.id,
        prompt_type=prompt_type,
        user_id=user.id,
    )

    # Verify prompts exist
    assert len(created_prompts) == 3

    # Delete all prompts for the todo
    deleted_count = await contextual_prompt_service.delete_contextual_prompts_for_todo(todo.id, user.id)
    assert deleted_count == 3

    # Verify prompts are deleted from database
    remaining_prompts = await async_db_session.scalars(
        select(ContextualPrompt).where(ContextualPrompt.id.in_([p.id for p in created_prompts]))
    )
    assert list(remaining_prompts) == []

    # Verify entity links are deleted
    remaining_links = await async_db_session.scalars(
        select(EntityLink).where(
            EntityLink.typeA == LinkedEntityType.todos.value,
            EntityLink.idA == todo.id,
            EntityLink.typeB == LinkedEntityType.contextual_prompts.value,
        )
    )
    assert list(remaining_links) == []


@pytest.mark.asyncio
async def test_delete_contextual_prompts_for_todo_no_prompts(
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    # Delete prompts for todo with no prompts
    deleted_count = await contextual_prompt_service.delete_contextual_prompts_for_todo(todo.id, user.id)
    assert deleted_count == 0


@pytest.mark.asyncio
async def test_contextual_prompts_isolation_between_todos(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    user: User,
):
    # Create two todos
    todo1 = Todo(
        name="Todo 1",
        description="Description 1",
        userId=user.id,
        type=TodoType.userCreated,
    )
    todo2 = Todo(
        name="Todo 2",
        description="Description 2",
        userId=user.id,
        type=TodoType.userCreated,
    )
    async_db_session.add(todo1)
    async_db_session.add(todo2)
    await async_db_session.commit()
    await async_db_session.refresh(todo1)
    await async_db_session.refresh(todo2)

    # Create prompts for each todo
    await contextual_prompt_service.create_contextual_prompts(
        prompts=["Todo 1 Prompt"],
        todo_id=todo1.id,
        prompt_type=ContextualPromptType.todoPrompt,
        user_id=user.id,
    )
    await contextual_prompt_service.create_contextual_prompts(
        prompts=["Todo 2 Prompt"],
        todo_id=todo2.id,
        prompt_type=ContextualPromptType.mainChatPrompt,
        user_id=user.id,
    )

    # Get prompts for each todo
    todo1_prompts = await contextual_prompt_service.get_contextual_prompts_for_todo(todo1.id, user.id)
    todo2_prompts = await contextual_prompt_service.get_contextual_prompts_for_todo(todo2.id, user.id)

    assert len(todo1_prompts) == 1
    assert todo1_prompts[0].prompt == "Todo 1 Prompt"
    assert todo1_prompts[0].type == ContextualPromptType.todoPrompt

    assert len(todo2_prompts) == 1
    assert todo2_prompts[0].prompt == "Todo 2 Prompt"
    assert todo2_prompts[0].type == ContextualPromptType.mainChatPrompt

    # Delete prompts for todo1
    deleted_count = await contextual_prompt_service.delete_contextual_prompts_for_todo(todo1.id, user.id)
    assert deleted_count == 1

    # Verify todo2 prompts are not affected
    todo2_prompts_after = await contextual_prompt_service.get_contextual_prompts_for_todo(todo2.id, user.id)
    assert len(todo2_prompts_after) == 1
    assert todo2_prompts_after[0].prompt == "Todo 2 Prompt"


@pytest.mark.asyncio
async def test_user_isolation_create_contextual_prompts(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    user: User,
):
    """Test that users cannot create prompts for todos they don't own"""
    # Create another user
    other_user = User(email="<EMAIL>", clerkId="other_clerk_id")
    async_db_session.add(other_user)
    await async_db_session.commit()
    await async_db_session.refresh(other_user)

    # Create a todo owned by the other user
    other_todo = Todo(
        name="Other User's Todo",
        description="This belongs to another user",
        userId=other_user.id,
        type=TodoType.userCreated,
    )
    async_db_session.add(other_todo)
    await async_db_session.commit()
    await async_db_session.refresh(other_todo)

    # Try to create prompts for the other user's todo
    prompts_text = ["Unauthorized prompt"]
    prompt_type = ContextualPromptType.todoPrompt

    with pytest.raises(ContextualPromptDoesNotExist) as exc_info:
        await contextual_prompt_service.create_contextual_prompts(
            prompts=prompts_text,
            todo_id=other_todo.id,
            prompt_type=prompt_type,
            user_id=user.id,
        )
    assert f"Todo with id {other_todo.id} does not exist or does not belong to user {user.id}" in str(exc_info.value)


@pytest.mark.asyncio
async def test_user_isolation_get_contextual_prompts(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    user: User,
):
    """Test that users cannot get prompts for todos they don't own"""
    # Create another user
    other_user = User(email="<EMAIL>", clerkId="other_clerk_id")
    async_db_session.add(other_user)
    await async_db_session.commit()
    await async_db_session.refresh(other_user)

    # Create a todo owned by the other user
    other_todo = Todo(
        name="Other User's Todo",
        description="This belongs to another user",
        userId=other_user.id,
        type=TodoType.userCreated,
    )
    async_db_session.add(other_todo)
    await async_db_session.commit()
    await async_db_session.refresh(other_todo)

    # Create prompts for the other user's todo
    await contextual_prompt_service.create_contextual_prompts(
        prompts=["Other user's prompt"],
        todo_id=other_todo.id,
        prompt_type=ContextualPromptType.todoPrompt,
        user_id=other_user.id,
    )

    # Try to get prompts for the other user's todo
    with pytest.raises(ContextualPromptDoesNotExist) as exc_info:
        await contextual_prompt_service.get_contextual_prompts_for_todo(other_todo.id, user.id)
    assert f"Todo with id {other_todo.id} does not exist or does not belong to user {user.id}" in str(exc_info.value)


@pytest.mark.asyncio
async def test_user_isolation_delete_contextual_prompts(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    user: User,
):
    """Test that users cannot delete prompts for todos they don't own"""
    # Create another user
    other_user = User(email="<EMAIL>", clerkId="other_clerk_id")
    async_db_session.add(other_user)
    await async_db_session.commit()
    await async_db_session.refresh(other_user)

    # Create a todo owned by the other user
    other_todo = Todo(
        name="Other User's Todo",
        description="This belongs to another user",
        userId=other_user.id,
        type=TodoType.userCreated,
    )
    async_db_session.add(other_todo)
    await async_db_session.commit()
    await async_db_session.refresh(other_todo)

    # Create prompts for the other user's todo
    await contextual_prompt_service.create_contextual_prompts(
        prompts=["Other user's prompt"],
        todo_id=other_todo.id,
        prompt_type=ContextualPromptType.todoPrompt,
        user_id=other_user.id,
    )

    # Try to delete prompts for the other user's todo
    with pytest.raises(ContextualPromptDoesNotExist) as exc_info:
        await contextual_prompt_service.delete_contextual_prompts_for_todo(other_todo.id, user.id)
    assert f"Todo with id {other_todo.id} does not exist or does not belong to user {user.id}" in str(exc_info.value)


@pytest.mark.asyncio
async def test_create_contextual_prompts_with_appliance_link(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    """Test creating contextual prompts with appliance linking"""
    from src.db_models.appliance import Appliance
    from src.db_models.property import Property
    from src.schemas import LinkedEntity, LinkedEntityType

    # Create a property first
    test_property = Property()
    async_db_session.add(test_property)
    await async_db_session.commit()
    await async_db_session.refresh(test_property)

    # Create an appliance with the property ID
    appliance = Appliance(type="boiler", brand="Worcester", model="Greenstar 30i")
    appliance.propertyId = test_property.id
    async_db_session.add(appliance)
    await async_db_session.commit()
    await async_db_session.refresh(appliance)

    prompts_text = ["Ready to book boiler service?", "Find Gas Safe engineer?"]
    prompt_type = ContextualPromptType.todoPrompt

    created_prompts = await contextual_prompt_service.create_contextual_prompts_with_appliance_link(
        prompts=prompts_text, todo_id=todo.id, prompt_type=prompt_type, user_id=user.id, appliance_id=appliance.id
    )

    # Verify prompts were created
    assert len(created_prompts) == 2
    for i, prompt in enumerate(created_prompts):
        assert prompt.prompt == prompts_text[i]
        assert prompt.type == prompt_type

    # Verify prompts are in database
    db_prompts = await async_db_session.scalars(select(ContextualPrompt))
    db_prompts_list = list(db_prompts)
    assert len(db_prompts_list) == 2

    # Verify todo-prompt links are created
    todo_prompt_links = await async_db_session.scalars(
        select(EntityLink).where(
            EntityLink.typeB == LinkedEntityType.todos.value,
            EntityLink.idB == todo.id,
            EntityLink.typeA == LinkedEntityType.contextual_prompts.value,
        )
    )
    assert len(list(todo_prompt_links)) == 2

    # Verify todo-appliance link is created
    todo_appliance_links = await async_db_session.scalars(
        select(EntityLink).where(
            EntityLink.typeA == LinkedEntityType.appliances.value,
            EntityLink.idA == appliance.id,
            EntityLink.typeB == LinkedEntityType.todos.value,
            EntityLink.idB == todo.id,
        )
    )
    assert len(list(todo_appliance_links)) == 1


@pytest.mark.asyncio
async def test_create_contextual_prompts_with_appliance_link_no_appliance_id(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    """Test creating contextual prompts without appliance linking when appliance_id is None"""
    prompts_text = ["General help prompt"]
    prompt_type = ContextualPromptType.todoPrompt

    created_prompts = await contextual_prompt_service.create_contextual_prompts_with_appliance_link(
        prompts=prompts_text,
        todo_id=todo.id,
        prompt_type=prompt_type,
        user_id=user.id,
        appliance_id=None,  # No appliance linking
    )

    # Verify prompts were created
    assert len(created_prompts) == 1
    assert created_prompts[0].prompt == prompts_text[0]

    # Verify no todo-appliance links were created
    todo_appliance_links = await async_db_session.scalars(
        select(EntityLink).where(
            EntityLink.typeB == LinkedEntityType.todos.value,
            EntityLink.idB == todo.id,
            EntityLink.typeA == LinkedEntityType.appliances.value,
        )
    )
    assert len(list(todo_appliance_links)) == 0


@pytest.mark.asyncio
async def test_create_contextual_prompts_with_invalid_appliance_id(
    async_db_session: AsyncSession,
    contextual_prompt_service: ContextualPromptService,
    todo: Todo,
    user: User,
):
    """Test that invalid appliance_id doesn't prevent prompt creation"""
    prompts_text = ["Test prompt"]
    prompt_type = ContextualPromptType.todoPrompt

    # This should not raise an exception even with invalid appliance_id
    created_prompts = await contextual_prompt_service.create_contextual_prompts_with_appliance_link(
        prompts=prompts_text,
        todo_id=todo.id,
        prompt_type=prompt_type,
        user_id=user.id,
        appliance_id=99999,  # Non-existent appliance
    )

    # Verify prompts were still created despite appliance linking failure
    assert len(created_prompts) == 1
    assert created_prompts[0].prompt == prompts_text[0]

    # Verify no todo-appliance links were created due to invalid appliance_id
    todo_appliance_links = await async_db_session.scalars(
        select(EntityLink).where(
            EntityLink.typeB == LinkedEntityType.todos.value,
            EntityLink.idB == todo.id,
            EntityLink.typeA == LinkedEntityType.appliances.value,
        )
    )
    assert len(list(todo_appliance_links)) == 0
