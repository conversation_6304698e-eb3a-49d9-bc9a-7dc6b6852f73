import os
from unittest.mock import MagicMock

import pytest

from src.services.DocumentSummarizationService import DocumentSummarizationService


@pytest.fixture
def service():
    os.environ["ANTHROPIC_API_KEY"] = "test_key"
    os.environ["GEMINI_API_KEY"] = "test_key"
    return DocumentSummarizationService()


@pytest.fixture
def mock_anthropic_messages():
    messages = MagicMock()
    messages.create = MagicMock(return_value=MagicMock(content=[MagicMock(text="Test description")]))
    return messages
