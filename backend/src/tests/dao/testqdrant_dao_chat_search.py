import pytest
import asyncio
import os
import time
from qdrant_client import AsyncQdrant<PERSON>lient, QdrantClient, models
import logging

from src.ai_dao.QdrantDAO import QdrantDAO

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("test_qdrant_dao_chat_search")

_VECTOR_SIZE = 1536

# RUNNING TESTS:
# docker-compose up -d qdrant-test
# pytest src/tests/dao/testqdrant_dao_chat_search.py -v


def _get_base_vector(index: int, size: int = _VECTOR_SIZE) -> list[float]:
    """Creates a base vector with 1.0 at the given index and 0.0 otherwise."""
    vec = [0.0] * size
    if 0 <= index < size:
        vec[index] = 1.0
    elif size > 0:
        vec[0] = 1.0
        logger.warning(
            f"MockOpenAIClient: _get_base_vector called with out-of-bounds index {index} for size {size}. Using index 0."
        )
    return vec


class MockOpenAIClient:
    def __init__(self):
        self.base_embeddings = {
            "prog": _get_base_vector(0),
            "cook": _get_base_vector(1),
            "ml": _get_base_vector(2),
            "travel": _get_base_vector(3),
            "generic_query": _get_base_vector(4),  # For "test query"
            "default_unmatched": _get_base_vector(5),  # For queries like "any query", "zzzzzz"
        }

        # Keys are exact summary strings or query strings
        self.embedding_map = {
            # Queries
            "programming python": self.base_embeddings["prog"],
            "programming": self.base_embeddings["prog"],  # for update test
            "italian recipes cooking": self.base_embeddings["cook"],
            "test query": self.base_embeddings["generic_query"],
            "any query": self.base_embeddings["default_unmatched"],
            "zzzzzzzzzzzzzz": self.base_embeddings["default_unmatched"],
            # Summaries (ensure these match QdrantDAO._create_chat_summary output)
            # Prog summary (3 items - from sample_conversations["programming_chat"])
            "Human: How do I learn Python?\nAssistant's response: Python is a great programming language to start with...\n\n | Human: What are the best Python tutorials?\nAssistant's response: I recommend starting with official Python docs...\n\n | Human: Can you explain functions in Python?\nAssistant's response: Functions in Python are defined using the def keyword...\n\n": self.base_embeddings[
                "prog"
            ],
            # Cook summary (3 items)
            "Human: How do I make Italian pasta?\nAssistant's response: For authentic Italian pasta, start with quality ingredients...\n\n | Human: What's the best tomato sauce recipe?\nAssistant's response: A classic marinara sauce requires San Marzano tomatoes...\n\n | Human: How long should I cook the pasta?\nAssistant's response: Pasta should be cooked al dente, usually 8-12 minutes...\n\n": self.base_embeddings[
                "cook"
            ],
            # ML summary (3 items)
            "Human: What is machine learning?\nAssistant's response: Machine learning is a subset of AI that enables computers to learn...\n\n | Human: Explain neural networks\nAssistant's response: Neural networks are computing systems inspired by biological neural networks...\n\n | Human: How do I start with ML?\nAssistant's response: Start with understanding statistics and linear algebra...\n\n": self.base_embeddings[
                "ml"
            ],
            # Travel summary (3 items)
            "Human: Best places to visit in Europe?\nAssistant's response: Europe offers many amazing destinations like Paris, Rome, Barcelona...\n\n | Human: How to plan a European vacation?\nAssistant's response: Start by deciding your budget and preferred travel style...\n\n | Human: What's the weather like in Europe?\nAssistant's response: European weather varies by season and region...\n\n": self.base_embeddings[
                "travel"
            ],
            # Summary for test_chat_summary_creation
            "Human: Hello, how are you?\nAssistant's response: I'm doing well, thank you!\n\n | Human: Can you help with Python?\nAssistant's response: Of course! What specifically do you need help with?\n\n": self.base_embeddings[
                "prog"
            ],  # Assuming prog-related
            # Summary for initial part of test_update_chat_search_on_history_change (2 items)
            "Human: How do I learn Python?\nAssistant's response: Python is a great programming language to start with...\n\n | Human: What are the best Python tutorials?\nAssistant's response: I recommend starting with official Python docs...\n\n": self.base_embeddings[
                "prog"
            ],
        }

    def get_embedding(self, text: str) -> list[float]:
        # Attempt exact match first (common for precise query/summary mapping)
        if text in self.embedding_map:
            # logger.info(f"MockOpenAIClient: Exact match for text: '{text[:60].replace('\n', ' ')}...'")
            return self.embedding_map[text]

        # Fallback to keyword-based matching for unmapped texts
        text_lower = text.lower()
        if "programming" in text_lower or "python" in text_lower or "code" in text_lower:
            # logger.info(f"MockOpenAIClient: Keyword 'programming' for: '{text[:60].replace('\n', ' ')}...'")
            return self.base_embeddings["prog"]
        elif "cooking" in text_lower or "recipe" in text_lower or "italian" in text_lower:
            # logger.info(f"MockOpenAIClient: Keyword 'cooking' for: '{text[:60].replace('\n', ' ')}...'")
            return self.base_embeddings["cook"]
        elif "machine learning" in text_lower or "ml" in text_lower or "ai" in text_lower:
            # logger.info(f"MockOpenAIClient: Keyword 'ml' for: '{text[:60].replace('\n', ' ')}...'")
            return self.base_embeddings["ml"]
        elif "travel" in text_lower or "europe" in text_lower or "vacation" in text_lower:
            # logger.info(f"MockOpenAIClient: Keyword 'travel' for: '{text[:60].replace('\n', ' ')}...'")
            return self.base_embeddings["travel"]

        logger.warning(
            f"MockOpenAIClient: No specific or keyword embedding for '{text[:100].replace('\n', ' ')}...'. Returning default_unmatched."
        )
        return self.base_embeddings["default_unmatched"]


@pytest.fixture
async def qdrant_dao(event_loop):  # event_loop fixture for async finalizer
    """Setup QdrantDAO with local test instance and mock OpenAI client"""

    os.environ["QDRANT_DB_URL"] = "http://localhost:6333"
    os.environ["QDRANT_API_KEY"] = "test-key"

    client_async = AsyncQdrantClient(url="http://localhost:6333", timeout=30)
    client_sync = QdrantClient(url="http://localhost:6333", timeout=30)
    mock_openai = MockOpenAIClient()

    max_retries = 5
    for i in range(max_retries):
        try:
            collections = client_sync.get_collections()
            logger.info(
                f"Connected to Qdrant successfully. Existing collections: {[c.name for c in collections.collections]}"
            )
            break
        except Exception as e:
            if i == max_retries - 1:
                await client_async.close()
                raise Exception(f"Could not connect to Qdrant after {max_retries} attempts: {e}")
            logger.info(f"Waiting for Qdrant to be ready... attempt {i + 1}")
            time.sleep(1 * (i + 1))

    dummy_dao_for_config = QdrantDAO(qdrant_client_asynch=client_async, openai_client=mock_openai)

    test_collections_configs = {
        dummy_dao_for_config._collection_name: models.VectorParams(
            size=dummy_dao_for_config._vector_size, distance=dummy_dao_for_config._distance
        ),
        dummy_dao_for_config._collection_name_chats: models.VectorParams(size=1, distance=models.Distance.COSINE),
        # size 1 for dummy vector
        dummy_dao_for_config._collection_name_chats_jobs: models.VectorParams(size=1, distance=models.Distance.COSINE),
        # size 1
        dummy_dao_for_config._collection_name_chats_search: models.VectorParams(
            size=dummy_dao_for_config._vector_size, distance=dummy_dao_for_config._distance
        ),
    }

    for collection_name in test_collections_configs.keys():
        try:
            if client_sync.collection_exists(collection_name):
                logger.info(f"Deleting existing collection: {collection_name}")
                client_sync.delete_collection(collection_name)
                time.sleep(0.1)
        except Exception as e:
            logger.warning(f"Error deleting collection {collection_name} during setup: {e}")

    dao = QdrantDAO(qdrant_client_asynch=client_async, openai_client=mock_openai)

    logger.info("Fixture explicitly ensuring all collections exist...")
    for name, vec_config in test_collections_configs.items():
        retries = 3
        for attempt in range(retries):
            try:
                if not client_sync.collection_exists(collection_name=name):
                    logger.info(
                        f"Fixture creating collection: {name} (vectors: size={vec_config.size}, distance={vec_config.distance}) (attempt {attempt + 1})"
                    )
                    client_sync.create_collection(collection_name=name, vectors_config=vec_config)
                    time.sleep(0.2)

                if client_sync.collection_exists(collection_name=name):
                    # logger.info(f"Collection {name} confirmed to exist by fixture.")
                    break
                elif attempt == retries - 1:
                    raise Exception(f"Fixture failed to create or confirm collection {name} after {retries} attempts.")
            except Exception as e:
                if attempt == retries - 1:
                    await client_async.close()
                    raise Exception(f"Critical error creating/confirming collection {name} in fixture: {e}")
                logger.warning(f"Attempt {attempt + 1} to handle collection {name} failed: {e}. Retrying...")
                time.sleep(0.5 * (attempt + 1))

    collections_after_fixture_setup = client_sync.get_collections()
    collection_names_final = [c.name for c in collections_after_fixture_setup.collections]
    logger.info(f"Collections confirmed by fixture: {collection_names_final}")
    for name in test_collections_configs.keys():
        assert name in collection_names_final, f"Fixture failed to ensure collection '{name}' exists."

    yield dao

    logger.info("Cleaning up test collections...")
    try:
        for collection_name in test_collections_configs.keys():
            if client_sync.collection_exists(collection_name):
                # logger.info(f"Deleting collection: {collection_name}")
                client_sync.delete_collection(collection_name)
    except Exception as e:
        logger.warning(f"Cleanup error: {e}")
    finally:
        await client_async.close()


@pytest.fixture
def sample_conversations():
    """Sample conversation data for testing"""
    return {
        "programming_chat": [
            ("How do I learn Python?", "Python is a great programming language to start with..."),
            ("What are the best Python tutorials?", "I recommend starting with official Python docs..."),
            ("Can you explain functions in Python?", "Functions in Python are defined using the def keyword..."),
        ],
        "cooking_chat": [
            ("How do I make Italian pasta?", "For authentic Italian pasta, start with quality ingredients..."),
            ("What's the best tomato sauce recipe?", "A classic marinara sauce requires San Marzano tomatoes..."),
            ("How long should I cook the pasta?", "Pasta should be cooked al dente, usually 8-12 minutes..."),
        ],
        "ml_chat": [
            ("What is machine learning?", "Machine learning is a subset of AI that enables computers to learn..."),
            (
                "Explain neural networks",
                "Neural networks are computing systems inspired by biological neural networks...",
            ),
            ("How do I start with ML?", "Start with understanding statistics and linear algebra..."),
        ],
        "travel_chat": [
            (
                "Best places to visit in Europe?",
                "Europe offers many amazing destinations like Paris, Rome, Barcelona...",
            ),
            ("How to plan a European vacation?", "Start by deciding your budget and preferred travel style..."),
            ("What's the weather like in Europe?", "European weather varies by season and region..."),
        ],
    }


class TestQdrantDAOChatSearch:

    @pytest.mark.asyncio
    async def test_collections_created(self, qdrant_dao: QdrantDAO):
        client_async = qdrant_dao._qdrant_client_asynch
        required_collections = [
            qdrant_dao._collection_name,
            qdrant_dao._collection_name_chats,
            qdrant_dao._collection_name_chats_jobs,
            qdrant_dao._collection_name_chats_search,
        ]
        for collection_name in required_collections:
            assert await client_async.collection_exists(collection_name), f"Collection {collection_name} should exist"
        collection_info_search = await client_async.get_collection(qdrant_dao._collection_name_chats_search)
        assert collection_info_search.config.params.vectors.size == qdrant_dao._vector_size
        collection_info_chats = await client_async.get_collection(qdrant_dao._collection_name_chats)
        assert collection_info_chats.config.params.vectors.size == 1
        logger.info("V test_collections_created passed")

    @pytest.mark.asyncio
    async def test_upsert_searchable_chat(self, qdrant_dao: QdrantDAO, sample_conversations):
        user_id = "user123"
        chat_id = "chat_programming_001"
        conversation = sample_conversations["programming_chat"]
        expected_conversation_history = [list(item) for item in conversation]
        await qdrant_dao.upsert_searchable_chat(user_id, chat_id, conversation)
        await asyncio.sleep(0.5)
        results = await qdrant_dao.search_chat_history("programming python", user_id, limit=1)
        assert len(results) == 1, f"Expected 1 result, got {len(results)}"
        assert results[0].userId == user_id
        assert results[0].chatId == chat_id
        assert results[0].conversationHistory == expected_conversation_history
        assert "python" in results[0].chatSummary.lower()
        logger.info("V test_upsert_searchable_chat passed")

    @pytest.mark.asyncio
    async def test_search_chat_history_basic(self, qdrant_dao: QdrantDAO, sample_conversations):
        user_id = "user123_basic"
        await qdrant_dao.upsert_searchable_chat(user_id, "chat_prog", sample_conversations["programming_chat"])
        await qdrant_dao.upsert_searchable_chat(user_id, "chat_cook", sample_conversations["cooking_chat"])
        await qdrant_dao.upsert_searchable_chat(user_id, "chat_ml", sample_conversations["ml_chat"])
        await asyncio.sleep(0.5)
        results = await qdrant_dao.search_chat_history("programming python", user_id, limit=3)
        assert len(results) >= 1, "Expected at least one result for 'programming python'"
        assert (
            results[0].chatId == "chat_prog"
        ), f"Programming chat should be most relevant. Got {results[0].chatId} with score {results[0].score}. All results: {[(r.chatId, r.score) for r in results]}"
        assert all(r.userId == user_id for r in results)
        logger.info("V test_search_chat_history_basic passed")

    @pytest.mark.asyncio
    async def test_search_chat_history_relevance_ranking(self, qdrant_dao: QdrantDAO, sample_conversations):
        user_id = "user123_relevance"
        await qdrant_dao.upsert_searchable_chat(user_id, "chat_prog", sample_conversations["programming_chat"])
        await qdrant_dao.upsert_searchable_chat(user_id, "chat_cook", sample_conversations["cooking_chat"])
        await qdrant_dao.upsert_searchable_chat(user_id, "chat_travel", sample_conversations["travel_chat"])
        await asyncio.sleep(0.5)
        results = await qdrant_dao.search_chat_history("italian recipes cooking", user_id, limit=3)
        assert len(results) >= 1
        assert results[0].chatId == "chat_cook"
        for i in range(len(results) - 1):
            assert results[i].score >= results[i + 1].score
        logger.info("V test_search_chat_history_relevance_ranking passed")

    @pytest.mark.asyncio
    async def test_user_isolation_security(self, qdrant_dao: QdrantDAO, sample_conversations):
        user1_id = "user1_iso"
        user2_id = "user2_iso"
        conversation = sample_conversations["programming_chat"]
        await qdrant_dao.upsert_searchable_chat(user1_id, "chat1_user1", conversation)
        await qdrant_dao.upsert_searchable_chat(user2_id, "chat2_user2", conversation)
        await asyncio.sleep(0.5)
        user1_results = await qdrant_dao.search_chat_history("programming", user1_id, limit=10)
        assert len(user1_results) == 1
        assert user1_results[0].userId == user1_id
        user2_results = await qdrant_dao.search_chat_history("programming", user2_id, limit=10)
        assert len(user2_results) == 1
        assert user2_results[0].userId == user2_id
        assert user1_results[0].chatId != user2_results[0].chatId
        logger.info("V test_user_isolation_security passed")

    @pytest.mark.asyncio
    async def test_update_chat_search_on_history_change(self, qdrant_dao: QdrantDAO, sample_conversations):
        user_id = "user_update"
        chat_id = "chat_update_test"
        initial_conversation = sample_conversations["programming_chat"][:2]
        expected_initial_history = [list(item) for item in initial_conversation]
        await qdrant_dao.update_chat_search_on_history_change(user_id, chat_id, initial_conversation)
        await asyncio.sleep(0.5)
        results1 = await qdrant_dao.search_chat_history("programming", user_id, limit=1)  # Query "programming"
        assert len(results1) == 1, "Search after initial upsert failed to find the chat."
        assert (
            len(results1[0].conversationHistory) == 2
        ), f"Expected 2 messages in history, got {len(results1[0].conversationHistory)}. Summary was: {results1[0].chatSummary}"
        assert results1[0].conversationHistory == expected_initial_history

        updated_conversation = sample_conversations["programming_chat"]
        expected_updated_history = [list(item) for item in updated_conversation]
        await qdrant_dao.update_chat_search_on_history_change(user_id, chat_id, updated_conversation)
        await asyncio.sleep(0.5)
        results2 = await qdrant_dao.search_chat_history("programming", user_id, limit=1)  # Query "programming"
        assert len(results2) == 1, "Search after update failed to find the chat."
        assert len(results2[0].conversationHistory) == 3
        assert results2[0].conversationHistory == expected_updated_history
        regular_history = await qdrant_dao.get_chat_history(user_id, chat_id)
        assert len(regular_history) == 3
        assert regular_history == expected_updated_history
        logger.info("V test_update_chat_search_on_history_change passed")

    @pytest.mark.asyncio
    async def test_empty_conversation_handling(self, qdrant_dao: QdrantDAO):
        user_id = "user_empty_conv"
        chat_id = "empty_chat"
        await qdrant_dao.upsert_searchable_chat(user_id, chat_id, [])
        await asyncio.sleep(0.2)
        results = await qdrant_dao.search_chat_history("anything", user_id, limit=5)
        assert len(results) == 0
        logger.info("V test_empty_conversation_handling passed")

    @pytest.mark.asyncio
    async def test_limit_parameter(self, qdrant_dao: QdrantDAO, sample_conversations):
        user_id = "user_limit_test"
        conversations_data = [
            ("chat1_limit", sample_conversations["programming_chat"]),
            ("chat2_limit", sample_conversations["cooking_chat"]),
            ("chat3_limit", sample_conversations["ml_chat"]),
            ("chat4_limit", sample_conversations["travel_chat"]),
        ]
        for chat_id, conv in conversations_data:
            await qdrant_dao.upsert_searchable_chat(user_id, chat_id, conv)
        await asyncio.sleep(0.5)
        results_1 = await qdrant_dao.search_chat_history("test query", user_id, limit=1)
        results_2 = await qdrant_dao.search_chat_history("test query", user_id, limit=2)
        results_all = await qdrant_dao.search_chat_history("test query", user_id, limit=10)
        assert len(results_1) == 1
        assert len(results_2) == 2
        assert len(results_all) == 4
        logger.info("V test_limit_parameter passed")

    @pytest.mark.asyncio
    async def test_chat_summary_creation(self, qdrant_dao: QdrantDAO):
        conversation = [
            ("Hello, how are you?", "I'm doing well, thank you!"),
            ("Can you help with Python?", "Of course! What specifically do you need help with?"),
        ]
        conversation_as_list_of_lists = [list(item) for item in conversation]
        summary = qdrant_dao._create_chat_summary(conversation_as_list_of_lists)
        expected_summary_key = "Human: Hello, how are you?\nAssistant's response: I'm doing well, thank you!\n\n | Human: Can you help with Python?\nAssistant's response: Of course! What specifically do you need help with?\n\n"
        assert summary == expected_summary_key
        empty_summary = qdrant_dao._create_chat_summary([])
        assert empty_summary == ""
        logger.info("V test_chat_summary_creation passed")
