import unittest
from unittest.mock import patch

from src.agents.JobSummaryExtractor import JobSummaryExtractor


class TestJobSummaryExtractor(unittest.TestCase):
    def setUp(self):
        self.extractor = JobSummaryExtractor()

    def test_extract_original_format(self):
        """Test extraction with the original [[jobSummary = {...}]] format"""
        test_input = """Here's a job summary for you:

        [[
        jobSummary = {
            jobHeadline: "e.g. No hot water",
            jobSubTitle: "e.g. Gas Boiler Maxol Micro Turbo Not Working",
            jobDetails: "e.g. Seeking a Gas Safe registered engineer ....",
            jobDate: " e.g. <PERSON><PERSON> (within 48 hours)",
            jobTimeOfDay: "e.g. Tuesdays and Thursdays after 3 PM or weekends"
        }
        ]]

        Let me know if you need anything else!"""

        processed_response, job_summary = self.extractor.extract_job_summary(test_input)

        # Check that the JSON block was removed
        self.assertNotIn("[[", processed_response)
        self.assertNotIn("]]", processed_response)
        self.assertNotIn("jobSummary", processed_response)

        # Check that job summary was correctly extracted
        self.assertIsNotNone(job_summary)
        self.assertEqual(job_summary.jobHeadline, "e.g. No hot water")
        self.assertEqual(job_summary.jobSubTitle, "e.g. Gas Boiler Maxol Micro Turbo Not Working")
        self.assertEqual(job_summary.jobDetails, "e.g. Seeking a Gas Safe registered engineer ....")
        self.assertEqual(job_summary.jobDate, " e.g. Urgent (within 48 hours)")
        self.assertEqual(job_summary.jobTimeOfDay, "e.g. Tuesdays and Thursdays after 3 PM or weekends")
        self.assertEqual(job_summary.messageContainingJobSummary, test_input)
        self.assertIsNone(job_summary.errorDuringParsing)

    def test_extract_code_block_format(self):
        """Test extraction with the ```json\njobSummary = {...}\n``` format"""
        test_input = """Here's the job summary:

        ```json
        jobSummary = {
            "jobHeadline": "e.g. No hot water",
            "jobSubTitle": "e.g. Gas Boiler Maxol Micro Turbo Not Working",
            "jobDetails": "e.g. Seeking a Gas Safe registered engineer ....",
            "jobDate": " e.g. Urgent (within 48 hours)",
            "jobTimeOfDay": "e.g. Tuesdays and Thursdays after 3 PM or weekends"
        }
        ```

        Let me know if you need more information."""

        processed_response, job_summary = self.extractor.extract_job_summary(test_input)

        # Check that the JSON block was removed
        self.assertNotIn("```json", processed_response)
        self.assertNotIn("```", processed_response)
        self.assertNotIn("jobSummary", processed_response)

        # Check that job summary was correctly extracted
        self.assertIsNotNone(job_summary)
        self.assertEqual(job_summary.jobHeadline, "e.g. No hot water")
        self.assertEqual(job_summary.jobSubTitle, "e.g. Gas Boiler Maxol Micro Turbo Not Working")
        self.assertEqual(job_summary.jobDetails, "e.g. Seeking a Gas Safe registered engineer ....")
        self.assertEqual(job_summary.jobDate, " e.g. Urgent (within 48 hours)")
        self.assertEqual(job_summary.jobTimeOfDay, "e.g. Tuesdays and Thursdays after 3 PM or weekends")
        self.assertEqual(job_summary.messageContainingJobSummary, test_input)
        self.assertIsNone(job_summary.errorDuringParsing)

    def test_extract_plain_code_block_format(self):
        """Test extraction with the ```\njobSummary = {...}\n``` format (no 'json' identifier)"""
        test_input = """Check out this job:

        ```
        jobSummary = {
            jobHeadline: "e.g. No hot water",
            jobSubTitle: "e.g. Gas Boiler Maxol Micro Turbo Not Working",
            jobDetails: "e.g. Seeking a Gas Safe registered engineer ....",
            jobDate: " e.g. Urgent (within 48 hours)",
            jobTimeOfDay: "e.g. Tuesdays and Thursdays after 3 PM or weekends"
        }
        ```

        Is this what you're looking for?"""

        processed_response, job_summary = self.extractor.extract_job_summary(test_input)

        # Check that the JSON block was removed
        self.assertNotIn("```", processed_response)
        self.assertNotIn("jobSummary", processed_response)

        # Check that job summary was correctly extracted
        self.assertIsNotNone(job_summary)
        self.assertEqual(job_summary.jobHeadline, "e.g. No hot water")
        self.assertEqual(job_summary.jobSubTitle, "e.g. Gas Boiler Maxol Micro Turbo Not Working")
        self.assertEqual(job_summary.jobDetails, "e.g. Seeking a Gas Safe registered engineer ....")
        self.assertEqual(job_summary.jobDate, " e.g. Urgent (within 48 hours)")
        self.assertEqual(job_summary.jobTimeOfDay, "e.g. Tuesdays and Thursdays after 3 PM or weekends")
