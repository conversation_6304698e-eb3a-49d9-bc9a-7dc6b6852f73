import pytest
import os
from datetime import datetime
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import date

from src.agents.todos_agent import TodosAgent, FindTodoListWrapper
from src.ai_schemas import ChatMessage, FindTodo


@pytest.fixture
def sample_messages():
    return [
        ChatMessage(id=1, senderType="user", content="Broken pipe", timestamp=datetime.utcnow(), type="text"),
        ChatMessage(id=2, senderType="system", content="Ok not cool.", timestamp=datetime.utcnow(), type="text"),
    ]


@pytest.fixture
def sample_prompt():
    return "Extract todos from this conversation."


@pytest.fixture
def sample_result():
    return FindTodoListWrapper(todos=[FindTodo(title="Fix pipe", description=None, dueDate=None)])


@pytest.mark.asyncio
@patch("src.agents.todos_agent.ChatFormatter.format_conversation_from_list_of_messages")
@patch("src.agents.todos_agent.ChatAnthropic")
@patch("src.agents.todos_agent.PydanticOutputParser")
async def test_extract_todos(
    mock_output_parser, mock_chat_anthropic, mock_formatter, sample_messages, sample_prompt, sample_result
):
    os.environ["ANTHROPIC_API_KEY"] = "fake_key"

    mock_formatter.return_value = "formatted convo"

    mock_chain = AsyncMock()
    mock_chain.ainvoke.return_value = sample_result

    mock_llm = MagicMock()
    mock_chat_anthropic.return_value = mock_llm
    mock_output_parser.return_value = MagicMock()
    mock_llm.__or__.return_value = mock_chain

    agent = TodosAgent(agent_prompt=sample_prompt)
    todos = await agent.extract_todos(sample_messages)

    expected_prompt = f"{sample_prompt}\n Today is: {date.today()}\n\nformatted convo"
    mock_formatter.assert_called_once_with(sample_messages)
    mock_chain.ainvoke.assert_awaited_once_with(expected_prompt)

    assert todos == sample_result.todos
