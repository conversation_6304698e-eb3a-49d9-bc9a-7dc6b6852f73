from unittest.mock import Async<PERSON>ock

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.integrations.clerk import Clerk<PERSON>ser
from src.schemas import ConvertGuestUserToRegisteredRequest, UserDetails
from src.services.properties import PropertyService
from src.services.users import UserService


@pytest_asyncio.fixture
async def user_service(
    async_db_session: AsyncSession,
) -> UserService:
    return UserService(async_db_session, AsyncMock(), AsyncMock(), AsyncMock(), AsyncMock())


@pytest_asyncio.fixture
async def test_user(async_db_session: AsyncSession) -> User:
    user = User(
        id=1,
        clerkId="clerk_user_id_3",
        email="<EMAIL>",
        isEmailVerified=False,
        firstName="Original",
        lastName="User",
        phoneNumber="0000000000",
        isPhoneNumberVerified=False,
    )
    async_db_session.add(user)
    await async_db_session.flush()
    await async_db_session.refresh(user)
    return user


@pytest.fixture
async def empty_property_with_user(async_db_session: AsyncSession, property_service: PropertyService, test_user):
    empty_property = await property_service.create_property_for_user(test_user)
    return empty_property, test_user


@pytest.mark.asyncio
async def test_delete_user(
    async_db_session: AsyncSession,
    user_service: UserService,
):
    user = User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id",
        firstName="Test",
        lastName="User",
    )
    async_db_session.add(user)
    await async_db_session.flush()

    user_from_db = await async_db_session.get(User, user.id)
    assert user_from_db is not None

    await user_service.delete_user(user)

    deleted_user = await user_service.get_user_by_clerk_id(user.clerkId)
    assert deleted_user is None


@pytest.mark.asyncio
async def test_create_user_from_clerk_user(
    async_db_session: AsyncSession,
):
    clerk_user = ClerkUser(
        clerk_id="clerk_user_id_2",
        primary_email="<EMAIL>",
        is_primary_email_verified=True,
        first_name="New",
        last_name="User",
        primary_phone_number=None,
        is_primary_phone_number_verified=None,
        user_id=None,
    )

    hubspot_sync_service = AsyncMock()
    user_service = UserService(async_db_session, AsyncMock(), AsyncMock(), hubspot_sync_service, AsyncMock())

    created_user = await user_service.upsert_user_from_clerk_user(clerk_user)
    assert created_user is not None
    assert created_user.clerkId == clerk_user.clerk_id
    assert created_user.email == clerk_user.primary_email
    assert created_user.firstName == clerk_user.first_name
    assert created_user.lastName == clerk_user.last_name
    assert created_user.isGuest == False

    user_from_db = await async_db_session.get(User, created_user.id)
    assert user_from_db is not None
    assert user_from_db.clerkId == clerk_user.clerk_id
    assert user_from_db.email == clerk_user.primary_email
    assert user_from_db.firstName == clerk_user.first_name
    assert user_from_db.lastName == clerk_user.last_name
    assert user_from_db.canUserAcceptJobs == False

    hubspot_sync_service.sync_user.assert_called_once_with(created_user.id)


@pytest.mark.asyncio
async def test_update_user_from_clerk_user(async_db_session: AsyncSession, empty_property_with_user):
    empty_property, test_user = empty_property_with_user

    updated_clerk_user = ClerkUser(
        clerk_id=test_user.clerkId,
        primary_email="<EMAIL>",
        is_primary_email_verified=True,
        first_name="Updated",
        last_name="User",
        primary_phone_number="1234567890",
        is_primary_phone_number_verified=True,
        user_id=None,
    )

    hubspot_sync_service = AsyncMock()
    user_service = UserService(async_db_session, AsyncMock(), AsyncMock(), hubspot_sync_service, AsyncMock())

    updated_user = await user_service.upsert_user_from_clerk_user(updated_clerk_user)

    assert updated_user is not None
    assert updated_user.email == updated_clerk_user.primary_email
    assert updated_user.isEmailVerified == updated_clerk_user.is_primary_email_verified
    assert updated_user.firstName == updated_clerk_user.first_name
    assert updated_user.lastName == updated_clerk_user.last_name
    assert updated_user.phoneNumber == updated_clerk_user.primary_phone_number
    assert updated_user.isPhoneNumberVerified == updated_clerk_user.is_primary_phone_number_verified

    user_from_db = await async_db_session.get(User, updated_user.id)
    assert user_from_db.canUserAcceptJobs == True
    assert UserDetails.model_validate(user_from_db).canUserAcceptJobs == True

    hubspot_sync_service.sync_user.assert_called_once_with(updated_user.id)


@pytest.mark.asyncio
async def test_create_guest_user(async_db_session: AsyncSession, user_service: UserService):
    guest_user = await user_service.create_guest_user()
    assert guest_user is not None
    assert guest_user.id is not None
    assert guest_user.isGuest == True
    assert guest_user.clerkId is None
    assert guest_user.email is None
    assert guest_user.firstName is None
    assert guest_user.lastName is None
    assert guest_user.phoneNumber is None
    assert guest_user.isPhoneNumberVerified is None


@pytest.mark.asyncio
async def test_convert_guest_user_to_authenticated(
    async_db_session: AsyncSession, user_service: UserService, monkeypatch
):
    guest_user = await user_service.create_guest_user()
    assert guest_user.isGuest is True
    assert guest_user.clerkId is None

    email = "<EMAIL>"
    first_name = "Converted"
    last_name = "User"

    user_service._clerk_api.create_user = AsyncMock(
        return_value=ClerkUser(
            clerk_id="clerk_user_id_converted",
            primary_email=email,
            is_primary_email_verified=True,
            first_name=first_name,
            last_name=last_name,
            primary_phone_number=None,
            is_primary_phone_number_verified=None,
            user_id=None,
        )
    )

    converted_user = await user_service.convert_guest_user_to_authenticated(
        user=guest_user,
        user_details=ConvertGuestUserToRegisteredRequest(
            email=email,
            firstName=first_name,
            lastName=last_name,
        ),
    )

    assert converted_user is not None
    assert converted_user.isGuest is False
    assert converted_user.clerkId == "clerk_user_id_converted"

    with pytest.raises(ValueError):
        await user_service.convert_guest_user_to_authenticated(
            user=guest_user,
            user_details=ConvertGuestUserToRegisteredRequest(
                email=email,
                firstName=first_name,
                lastName=last_name,
            ),
        )
