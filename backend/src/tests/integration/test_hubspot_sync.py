from datetime import datetime, timedelta
from unittest.mock import AsyncMock

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.chat import Chat
from src.db_models.job import Job
from src.db_models.project import Project
from src.db_models.user import User
from src.integrations.hubspot import JobWithDocumentsUrls
from src.schemas import B2BDemoRequest
from src.services.hubspot_sync import HubSpotSyncService


@pytest.fixture
def already_synced_user():
    return User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id_1",
        firstName="Already Synced",
        lastName="User",
        hubspotId="old_hubspot_id",
    )


@pytest.fixture
def new_user():
    return User(
        id=2,
        email="<EMAIL>",
        clerkId="clerk_user_id_2",
        firstName="New",
        lastName="User",
    )


@pytest.fixture
def deleted_user():
    """User that has been deleted (should not be synced)"""
    return User(
        id=6,
        email="<EMAIL>",
        clerkId="clerk_user_id_6",
        firstName="Deleted",
        lastName="User",
        hubspotId=None,
        deletedAt=datetime.now() - timedelta(days=1),
    )


@pytest.fixture
def deleted_user_with_hubspot_id():
    """Deleted user that already has hubspotId (should not be synced)"""
    return User(
        id=7,
        email="<EMAIL>",
        clerkId="clerk_user_id_7",
        firstName="Deleted Synced",
        lastName="User",
        hubspotId="deleted_hubspot_id",
        deletedAt=datetime.now() - timedelta(days=1),
    )


@pytest.fixture
def recently_synced_user():
    """User synced within the expiration window (should not be synced again)"""
    return User(
        id=3,
        email="<EMAIL>",
        clerkId="clerk_user_id_3",
        firstName="Recently",
        lastName="Synced",
        hubspotId="recent_hubspot_id",
        hubspotSyncAt=datetime.now() - timedelta(seconds=300),  # 5 minutes ago
    )


@pytest.fixture
def never_synced_user():
    """User that has never been synced (hubspotSyncAt is None) but has hubspotId (should NOT be synced)"""
    return User(
        id=5,
        email="<EMAIL>",
        clerkId="clerk_user_id_5",
        firstName="Never",
        lastName="Synced",
        hubspotId="never_hubspot_id",
        hubspotSyncAt=None,
    )


@pytest.fixture
def dummy_user():
    return User(
        id=1,
        email="<EMAIL>",
        clerkId="dummy_clerk_id",
        firstName="Test",
        lastName="User",
        hubspotId="dummy_hubspot_id",
    )


@pytest.fixture
def user_without_hubspot_id():
    """User without hubspotId - jobs should not be synced"""
    return User(
        id=8,
        email="<EMAIL>",
        clerkId="no_hubspot_clerk_id",
        firstName="No",
        lastName="HubSpot",
        hubspotId=None,
    )


@pytest.fixture
def specific_user():
    return User(
        id=1,
        email="<EMAIL>",
        clerkId="specific_clerk_id",
        firstName="Specific",
        lastName="User",
    )


@pytest.fixture
def another_user():
    return User(
        id=2,
        email="<EMAIL>",
        clerkId="another_clerk_id",
        firstName="Another",
        lastName="User",
    )


@pytest.fixture
def dummy_project():
    return Project(
        id=1,
        headline="Dummy Project",
        subTitle="Dummy Subtitle",
        details="Some details about the dummy project",
        urgency="High",
        chatId=1,
        userId=1,
    )


@pytest.fixture
def dummy_chat():
    return Chat(id=1, title="Dummy", userId=1)


@pytest.fixture
def already_synced_job():
    return Job(
        id=1,
        headline="Test Headline 1",
        subTitle="Test Subtitle 1",
        details="Test details 1",
        urgency="Medium",
        availability="Weekdays",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=1,
        hubspotId="old_hubspot_id",
        hubspotSyncAt=datetime.now() - timedelta(seconds=300),  # 5 minutes ago
    )


@pytest.fixture
def new_job():
    return Job(
        id=2,
        headline="Test Headline 2",
        subTitle="Test Subtitle 2",
        details="Test details 2",
        urgency="High",
        availability="Weekends",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=1,
    )


@pytest.fixture
def job_with_deleted_user():
    """Job belonging to a deleted user (should not be synced)"""
    return Job(
        id=6,
        headline="Deleted User Job",
        subTitle="Deleted User Subtitle",
        details="Job from deleted user",
        urgency="High",
        availability="Weekends",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=7,  # deleted_user_with_hubspot_id
    )


@pytest.fixture
def job_with_user_no_hubspot():
    """Job belonging to user without hubspotId (should not be synced)"""
    return Job(
        id=7,
        headline="No HubSpot Job",
        subTitle="No HubSpot Subtitle",
        details="Job from user without hubspotId",
        urgency="Medium",
        availability="Anytime",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=8,  # user_without_hubspot_id
    )


@pytest.fixture
def recently_synced_job():
    """Job synced within the expiration window (should not be synced again)"""
    return Job(
        id=3,
        headline="Recent Job",
        subTitle="Recent Subtitle",
        details="Recent details",
        urgency="Low",
        availability="Weekdays",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=1,
        hubspotId="recent_hubspot_id",
        hubspotSyncAt=datetime.now() - timedelta(seconds=300),  # 5 minutes ago
    )


@pytest.fixture
def expired_sync_job():
    """Job synced outside the expiration window (should be synced again)"""
    return Job(
        id=4,
        headline="Expired Job",
        subTitle="Expired Subtitle",
        details="Expired details",
        urgency="High",
        availability="Weekends",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=1,
        hubspotId="expired_hubspot_id",
        hubspotSyncAt=datetime.now() - timedelta(seconds=1200),  # 20 minutes ago
    )


@pytest.fixture
def never_synced_job():
    """Job that has never been synced (hubspotSyncAt is None)"""
    return Job(
        id=5,
        headline="Never Synced Job",
        subTitle="Never Synced Subtitle",
        details="Never synced details",
        urgency="Medium",
        availability="Anytime",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=1,
        hubspotId="never_hubspot_id",
        hubspotSyncAt=None,
    )


@pytest.fixture
def test_job():
    return Job(
        id=1,
        headline="Test Headline",
        subTitle="Test Subtitle",
        details="Test details",
        urgency="Low",
        availability="Weekdays",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=1,
    )


@pytest.fixture
def job_with_wrong_status():
    """Job with status other than user_accepted (should not be synced)"""
    return Job(
        id=9,
        headline="Wrong Status Job",
        subTitle="Wrong Status Subtitle",
        details="Job with wrong status",
        urgency="High",
        availability="Weekends",
        status="created",  # Not user_accepted
        projectId=1,
        chatId=1,
        userId=1,
    )


@pytest.fixture
def demo_request():
    return B2BDemoRequest(companyName="B2B Demo", businessEmail="<EMAIL>", numberOfPropertiesManaged="5")


@pytest.fixture
def hubspot_api_mock():
    mock = AsyncMock()
    mock.get_batch_size_limit = lambda: 100
    return mock


PRESIGNED_URL_EXPIRATION_IN_SECONDS = 900


@pytest.fixture
def document_service_mock():
    mock = AsyncMock()
    mock.get_presigned_urls_of_chat_documents.return_value = [
        "http://example.com/doc1.pdf",
        "http://example.com/doc2.pdf",
    ]
    mock.get_presigned_url_expiration_in_seconds = lambda: PRESIGNED_URL_EXPIRATION_IN_SECONDS
    return mock


@pytest.fixture
def hubspot_service(async_db_session, hubspot_api_mock, document_service_mock):
    return HubSpotSyncService(
        async_db_session, hubspot_api=hubspot_api_mock, document_service=document_service_mock, retry_delay=0.1
    )


@pytest.mark.asyncio
async def test_sync_users_failure(async_db_session: AsyncSession, new_user, hubspot_service, hubspot_api_mock):
    async_db_session.add(new_user)
    await async_db_session.commit()

    hubspot_api_mock.upsert_contacts.side_effect = Exception("Simulated sync failure")

    await hubspot_service.sync_users()
    assert hubspot_api_mock.upsert_contacts.call_count == 3


@pytest.mark.asyncio
async def test_sync_jobs_success(
    async_db_session: AsyncSession,
    dummy_user,
    dummy_project,
    dummy_chat,
    already_synced_job,
    new_job,
    hubspot_service,
    hubspot_api_mock,
    document_service_mock,
):
    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    async_db_session.add_all([already_synced_job, new_job])
    await async_db_session.commit()

    def fill_dummy_hubspot_id(jobs_with_docs: list[JobWithDocumentsUrls]) -> list[Job]:
        for job_with_docs in jobs_with_docs:
            job_with_docs.job.hubspotId = "dummy_new_hubspot_id"
        return [job_with_docs.job for job_with_docs in jobs_with_docs]

    hubspot_api_mock.upsert_job_tickets.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_jobs()

    job1 = await async_db_session.scalar(select(Job).where(Job.id == 1))
    assert job1.hubspotId == "old_hubspot_id"

    job2 = await async_db_session.scalar(select(Job).where(Job.id == 2))
    assert job2.hubspotId == "dummy_new_hubspot_id"

    assert hubspot_api_mock.upsert_job_tickets.call_count == 1
    assert document_service_mock.get_presigned_urls_of_chat_documents.call_count == 1


@pytest.mark.asyncio
async def test_sync_jobs_failure(
    async_db_session: AsyncSession, dummy_user, dummy_project, dummy_chat, test_job, hubspot_service, hubspot_api_mock
):
    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    async_db_session.add(test_job)
    await async_db_session.commit()

    hubspot_api_mock.upsert_job_tickets.side_effect = Exception("Simulated sync failure")

    await hubspot_service.sync_jobs()

    assert hubspot_api_mock.upsert_job_tickets.call_count == 3


@pytest.mark.asyncio
async def test_sync_b2b_demo_request_failure(
    async_db_session: AsyncSession, demo_request, hubspot_service, hubspot_api_mock
):
    hubspot_api_mock.create_b2b_demo_request_ticket.side_effect = Exception("Simulated sync failure")

    await hubspot_service.sync_b2b_demo_request(demo_request)

    hubspot_api_mock.create_b2b_demo_request_ticket.assert_called_with(demo_request)
    assert hubspot_api_mock.create_b2b_demo_request_ticket.call_count == 3


@pytest.mark.asyncio
async def test_sync_b2b_demo_request_success(
    async_db_session: AsyncSession, demo_request, hubspot_service, hubspot_api_mock
):
    await hubspot_service.sync_b2b_demo_request(demo_request)

    hubspot_api_mock.create_b2b_demo_request_ticket.assert_called_with(demo_request)
    assert hubspot_api_mock.create_b2b_demo_request_ticket.call_count == 1


@pytest.mark.asyncio
async def test_force_sync_user_success(
    async_db_session: AsyncSession, specific_user, hubspot_service, hubspot_api_mock
):
    async_db_session.add(specific_user)
    await async_db_session.commit()

    def fill_dummy_hubspot_id(users: list[User]) -> list[User]:
        for user in users:
            user.hubspotId = "dummy_new_hubspot_id"
        return users

    hubspot_api_mock.upsert_contacts.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_user(specific_user.id)

    updated_user = await async_db_session.scalar(select(User).where(User.id == 1))
    assert updated_user.hubspotId == "dummy_new_hubspot_id"

    assert hubspot_api_mock.upsert_contacts.call_count == 1


@pytest.mark.asyncio
async def test_sync_user_success_with_other_users_synced_same_hubspot_id(
    async_db_session: AsyncSession, specific_user, another_user, hubspot_service, hubspot_api_mock
):
    async_db_session.add(specific_user)
    async_db_session.add(another_user)
    await async_db_session.commit()

    def fill_dummy_hubspot_id(users: list[User]) -> list[User]:
        for user in users:
            user.hubspotId = f"dummy_hubspot_id"
        return users

    hubspot_api_mock.upsert_contacts.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_users()

    updated_user = await async_db_session.scalar(select(User).where(User.id == 1))
    assert updated_user.hubspotId == "dummy_hubspot_id"

    updated_user = await async_db_session.scalar(select(User).where(User.id == 2))
    assert updated_user.hubspotId == "dummy_hubspot_id"

    assert hubspot_api_mock.upsert_contacts.call_count == 1


@pytest.mark.asyncio
async def test_force_sync_user_failure(
    async_db_session: AsyncSession, specific_user, hubspot_service, hubspot_api_mock
):
    async_db_session.add(specific_user)
    await async_db_session.commit()

    hubspot_api_mock.upsert_contacts.side_effect = Exception("Simulated sync failure")

    await hubspot_service.sync_user(specific_user.id)

    hubspot_api_mock.upsert_contacts.assert_called_with([specific_user])
    assert hubspot_api_mock.upsert_contacts.call_count == 3


@pytest.mark.asyncio
async def test_sync_job_success(
    async_db_session: AsyncSession,
    dummy_user,
    dummy_project,
    dummy_chat,
    test_job,
    hubspot_service,
    hubspot_api_mock,
    document_service_mock,
):
    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    async_db_session.add(test_job)
    await async_db_session.commit()

    def fill_dummy_hubspot_id(job_with_docs: JobWithDocumentsUrls) -> Job:
        job_with_docs.job.hubspotId = "dummy_new_hubspot_id"
        return job_with_docs.job

    hubspot_api_mock.upsert_job_ticket.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_job(test_job.id)

    updated_job = await async_db_session.scalar(select(Job).where(Job.id == 1))
    assert updated_job.hubspotId == "dummy_new_hubspot_id"

    assert hubspot_api_mock.upsert_job_ticket.call_count == 1
    assert document_service_mock.get_presigned_urls_of_chat_documents.call_count == 1


@pytest.mark.asyncio
async def test_sync_job_failure(
    async_db_session: AsyncSession, dummy_user, dummy_project, dummy_chat, test_job, hubspot_service, hubspot_api_mock
):
    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    async_db_session.add(test_job)
    await async_db_session.commit()

    hubspot_api_mock.upsert_job_ticket.side_effect = Exception("Simulated sync failure")

    await hubspot_service.sync_job(test_job.id)

    assert hubspot_api_mock.upsert_job_ticket.call_count == 3


@pytest.mark.asyncio
async def test_sync_users_only_syncs_users_without_hubspot_id(
    async_db_session: AsyncSession,
    recently_synced_user,
    never_synced_user,
    new_user,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that only users without hubspotId are synced"""
    async_db_session.add_all([recently_synced_user, never_synced_user, new_user])
    await async_db_session.commit()

    await hubspot_service.sync_users()

    assert hubspot_api_mock.upsert_contacts.call_count == 1

    # Get the users that were passed to sync_users
    call_args = hubspot_api_mock.upsert_contacts.call_args
    synced_users = call_args[0][0]  # First positional argument (list of users)
    synced_user_ids = set(user.id for user in synced_users)

    # Should include only:
    # - new_user (id=2): hubspotId is None and deletedAt is None

    # Should NOT include:
    # - recently_synced_user (id=3): has hubspotId
    # - never_synced_user (id=5): has hubspotId

    expected_ids = {2}  # Only new_user
    assert synced_user_ids == expected_ids, f"Expected {expected_ids}, got {synced_user_ids}"


@pytest.mark.asyncio
async def test_sync_users_excludes_deleted_users(
    async_db_session: AsyncSession,
    new_user,
    deleted_user,
    deleted_user_with_hubspot_id,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that deleted users are excluded from sync regardless of hubspotId status"""
    async_db_session.add_all([new_user, deleted_user, deleted_user_with_hubspot_id])
    await async_db_session.commit()

    await hubspot_service.sync_users()

    assert hubspot_api_mock.upsert_contacts.call_count == 1

    # Get the users that were passed to sync_users
    call_args = hubspot_api_mock.upsert_contacts.call_args
    synced_users = call_args[0][0]  # First positional argument (list of users)
    synced_user_ids = set(user.id for user in synced_users)

    # Should include only:
    # - new_user (id=2): hubspotId is None and deletedAt is None

    # Should NOT include:
    # - deleted_user (id=6): deletedAt is not None (even though hubspotId is None)
    # - deleted_user_with_hubspot_id (id=7): deletedAt is not None

    expected_ids = {2}  # Only new_user
    assert synced_user_ids == expected_ids, f"Expected {expected_ids}, got {synced_user_ids}"


@pytest.mark.asyncio
async def test_sync_jobs_cutoff_time_logic(
    async_db_session: AsyncSession,
    dummy_user,
    dummy_project,
    dummy_chat,
    recently_synced_job,
    expired_sync_job,
    never_synced_job,
    new_job,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that jobs are synced based on cutoff time logic"""
    # Add required entities first
    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    # Add all jobs to the database
    async_db_session.add_all([recently_synced_job, expired_sync_job, never_synced_job, new_job])
    await async_db_session.commit()

    def fill_dummy_hubspot_id(jobs_with_docs: list[JobWithDocumentsUrls]) -> list[Job]:
        for job_with_docs in jobs_with_docs:
            job_with_docs.job.hubspotId = f"new_hubspot_id_{job_with_docs.job.id}"
        return [job_with_docs.job for job_with_docs in jobs_with_docs]

    hubspot_api_mock.upsert_job_tickets.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_jobs()

    # Should sync:
    # - new_job (id=2): hubspotId is None and status is user_accepted
    # - expired_sync_job (id=4): sync is older than expiration and status is user_accepted
    # - never_synced_job (id=5): hubspotSyncAt is None and status is user_accepted

    # Should NOT sync:
    # - recently_synced_job (id=3): sync is within expiration window

    # We expect 3 calls to sync_job
    assert hubspot_api_mock.upsert_job_tickets.call_count == 1

    # Verify the jobs that were synced
    synced_job_ids = set(
        (job_with_docs.job.id for job_with_docs in hubspot_api_mock.upsert_job_tickets.call_args_list[0][0][0])
    )
    expected_ids = {2, 4, 5}  # new_job, expired_sync_job, never_synced_job
    assert synced_job_ids == expected_ids, f"Expected {expected_ids}, got {synced_job_ids}"


@pytest.mark.asyncio
async def test_sync_jobs_excludes_deleted_users(
    async_db_session: AsyncSession,
    dummy_user,
    deleted_user_with_hubspot_id,
    user_without_hubspot_id,
    dummy_project,
    dummy_chat,
    new_job,
    job_with_deleted_user,
    job_with_user_no_hubspot,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that jobs belonging to deleted users or users without hubspotId are excluded"""
    async_db_session.add_all(
        [dummy_user, deleted_user_with_hubspot_id, user_without_hubspot_id, dummy_project, dummy_chat]
    )
    await async_db_session.commit()

    async_db_session.add_all([new_job, job_with_deleted_user, job_with_user_no_hubspot])
    await async_db_session.commit()

    def fill_dummy_hubspot_id(jobs_with_docs: list[JobWithDocumentsUrls]) -> list[Job]:
        for job_with_docs in jobs_with_docs:
            job_with_docs.job.hubspotId = f"new_hubspot_id_{job_with_docs.job.id}"
        return [job_with_docs.job for job_with_docs in jobs_with_docs]

    hubspot_api_mock.upsert_job_tickets.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_jobs()

    # Should sync only:
    # - new_job (id=2): belongs to dummy_user who has hubspotId and is not deleted, and has user_accepted status

    # Should NOT sync:
    # - job_with_deleted_user (id=6): belongs to deleted user
    # - job_with_user_no_hubspot (id=7): belongs to user without hubspotId

    assert hubspot_api_mock.upsert_job_tickets.call_count == 1

    # Verify the job that was synced
    call_args = hubspot_api_mock.upsert_job_tickets.call_args
    synced_job_with_docs = call_args[0][0][0]
    assert synced_job_with_docs.job.id == 2  # new_job


@pytest.mark.asyncio
async def test_sync_jobs_only_user_accepted_status(
    async_db_session: AsyncSession,
    dummy_user,
    dummy_project,
    dummy_chat,
    new_job,
    job_with_wrong_status,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that only jobs with user_accepted status are synced"""
    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    async_db_session.add_all([new_job, job_with_wrong_status])
    await async_db_session.commit()

    def fill_dummy_hubspot_id(jobs_with_docs: list[JobWithDocumentsUrls]) -> list[Job]:
        for job_with_docs in jobs_with_docs:
            job_with_docs.job.hubspotId = f"new_hubspot_id_{job_with_docs.job.id}"
        return [job_with_docs.job for job_with_docs in jobs_with_docs]

    hubspot_api_mock.upsert_job_tickets.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_jobs()

    # Should sync only:
    # - new_job (id=2): has user_accepted status

    # Should NOT sync:
    # - job_with_wrong_status (id=9): has 'created' status instead of 'user_accepted'

    assert hubspot_api_mock.upsert_job_tickets.call_count == 1

    # Verify the job that was synced
    call_args = hubspot_api_mock.upsert_job_tickets.call_args
    synced_job_with_docs = call_args[0][0][0]
    assert synced_job_with_docs.job.id == 2  # new_job
    assert synced_job_with_docs.job.status == "user_accepted"


@pytest.mark.asyncio
async def test_sync_users_respects_batch_limit(
    async_db_session: AsyncSession,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that batch size limit is respected when selecting users for sync"""
    # Create more users than the batch limit
    users_to_create = []
    for i in range(150):  # More than the 100 batch limit
        user = User(
            id=i + 100,
            email=f"test{i}@example.com",
            clerkId=f"clerk_user_id_{i}",
            firstName=f"Test{i}",
            lastName="User",
            hubspotId=None,  # All need syncing
            deletedAt=None,  # All are not deleted
        )
        users_to_create.append(user)

    async_db_session.add_all(users_to_create)
    await async_db_session.commit()

    await hubspot_service.sync_users()

    assert hubspot_api_mock.upsert_contacts.call_count == 1

    # Get the users that were passed to sync_users
    call_args = hubspot_api_mock.upsert_contacts.call_args
    synced_users = call_args[0][0]

    # Should only sync up to batch limit (100)
    assert len(synced_users) <= 100, f"Batch size exceeded: {len(synced_users)}"


@pytest.mark.asyncio
async def test_sync_users_only_users_with_hubspot_id(
    async_db_session: AsyncSession,
    recently_synced_user,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that no users are synced when all users have hubspotId"""
    async_db_session.add(recently_synced_user)
    await async_db_session.commit()

    await hubspot_service.sync_users()

    hubspot_api_mock.upsert_contacts.assert_called_with([])


@pytest.mark.asyncio
async def test_sync_jobs_boundary_condition(
    async_db_session: AsyncSession,
    dummy_user,
    dummy_project,
    dummy_chat,
    hubspot_service,
    hubspot_api_mock,
    document_service_mock,
):
    """Test jobs synced exactly at the expiration boundary"""
    expiration_seconds = document_service_mock.get_presigned_url_expiration_in_seconds()

    # Job synced exactly at the expiration time
    boundary_job = Job(
        id=6,
        headline="Boundary Job",
        subTitle="Boundary Subtitle",
        details="Boundary details",
        urgency="Medium",
        availability="Anytime",
        status="user_accepted",
        projectId=1,
        chatId=1,
        userId=1,
        hubspotId="boundary_hubspot_id",
        hubspotSyncAt=datetime.now() - timedelta(seconds=expiration_seconds),
    )

    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    async_db_session.add(boundary_job)
    await async_db_session.commit()

    def fill_dummy_hubspot_id(jobs_with_docs: list[JobWithDocumentsUrls]) -> list[Job]:
        for job_with_docs in jobs_with_docs:
            job_with_docs.job.hubspotId = "dummy_new_hubspot_id"
        return [job_with_docs.job for job_with_docs in jobs_with_docs]

    hubspot_api_mock.upsert_job_tickets.side_effect = fill_dummy_hubspot_id

    await hubspot_service.sync_jobs()

    # The boundary job should be included (sync time equals cutoff and status is user_accepted)
    assert hubspot_api_mock.upsert_job_tickets.call_count == 1

    # Verify it was the boundary job that was synced
    call_args = hubspot_api_mock.upsert_job_tickets.call_args
    synced_jobs_with_docs = call_args[0][0]
    assert len(synced_jobs_with_docs) == 1
    assert synced_jobs_with_docs[0].job.id == 6


@pytest.mark.asyncio
async def test_sync_jobs_only_recently_synced(
    async_db_session: AsyncSession,
    dummy_user,
    dummy_project,
    dummy_chat,
    recently_synced_job,
    hubspot_service,
    hubspot_api_mock,
):
    """Test that no jobs are synced when all are recently synced"""
    async_db_session.add_all([dummy_user, dummy_project, dummy_chat])
    await async_db_session.commit()

    async_db_session.add(recently_synced_job)
    await async_db_session.commit()

    await hubspot_service.sync_jobs()

    # No jobs should be synced
    assert hubspot_api_mock.upsert_job_ticket.call_count == 0
