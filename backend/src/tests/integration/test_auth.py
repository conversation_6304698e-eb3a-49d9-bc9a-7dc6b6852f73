import datetime
import os

import jwt
import pytest
from fastapi import FastAPI

app = FastAPI()


@pytest.mark.filterwarnings("ignore:authenticate_request method is applicable")
def test_invalid_token(client, monkeypatch):
    def mock_decode(*args, **kwargs):
        raise jwt.InvalidTokenError

    monkeypatch.setattr("jwt.decode", mock_decode)
    os.environ["CLERK_PEM_PUBLIC_KEY"] = "test_key"

    response = client.get("/user/", headers={"Authorization": "Bearer invalid_token"})
    assert response.status_code == 401
    assert response.json() == {"detail": "Could not validate credentials"}


def test_create_guest_user(client, create):

    response = client.post("/user/guest/")

    assert response.status_code == 200
    data = response.json()
    assert "userId" in data
    assert "token" in data
    assert isinstance(data["userId"], int)
    assert isinstance(data["token"], str)

    token = data["token"]
    decoded_token = jwt.decode(token, os.environ["GUEST_USER_TOKEN_SIGNING_KEY"], algorithms=["HS256"])
    assert decoded_token["sub"] == str(data["userId"])
    assert datetime.datetime.fromtimestamp(decoded_token["iat"], tz=datetime.timezone.utc) < datetime.datetime.now(
        tz=datetime.timezone.utc
    )
    assert datetime.datetime.fromtimestamp(decoded_token["exp"], tz=datetime.timezone.utc) > datetime.datetime.now(
        tz=datetime.timezone.utc
    )
    assert decoded_token["iss"] == os.environ["BASE_URL"]
    assert decoded_token["azp"] == os.environ["BASE_URL"]

    response = client.get("/user/", headers={"Authorization": f"Bearer {token}"})
    assert response.status_code == 200
    user_details = response.json()
    assert user_details["id"] == data["userId"]
    # Simulate token expiration by setting 'exp' to 14 days ago
    expired_iat = int((datetime.datetime.now(tz=datetime.timezone.utc) - datetime.timedelta(days=15)).timestamp())
    expired_exp = int((datetime.datetime.now(tz=datetime.timezone.utc) - datetime.timedelta(days=1)).timestamp())
    expired_token_payload = decoded_token.copy()
    expired_token_payload["iat"] = expired_iat
    expired_token_payload["exp"] = expired_exp

    expired_token = jwt.encode(
        expired_token_payload,
        os.environ["GUEST_USER_TOKEN_SIGNING_KEY"],
        algorithm="HS256",
    )

    response = client.get("/user/", headers={"Authorization": f"Bearer {expired_token}"})
    assert response.status_code == 401
    assert response.json() == {"detail": "Could not validate credentials"}
