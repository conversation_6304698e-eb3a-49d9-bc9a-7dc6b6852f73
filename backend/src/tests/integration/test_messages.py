import re
from unittest.mock import AsyncMock

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.document import Document, DocumentStatusType
from src.db_models.job import Job
from src.db_models.message import Message as DbMessage
from src.db_models.relationships import UsersProperties
from src.db_models.user import User
from src.integrations.aiengine import AIEngineAPI, AIMessageResponse, ResponseData, Message
from src.schemas import Message as MessageSchema
from src.schemas import SendMessageRequest, SendMessage, SendMessageMetadata, Attachment
from src.services.jobs import JobService, JobDoesNotExist, OperationNotAllowed
from src.services.messages import MessageService
from src.services.properties import PropertyService
from src.tests.matchers import model_dump


@pytest.mark.asyncio(loop_scope="session")
class TestMessages:
    @pytest.fixture
    def api(self, mocker):
        api = AIEngineAPI(api_key="test", base_url="https://aiengine.localhost")
        mock_response = AIMessageResponse(
            status="success",
            data=ResponseData(
                chatId=1,
                response=Message(content="This is an example response", type="text"),
                additionalData={
                    "category": "general",
                    "confidence": 0.95,
                    "suggested_actions": [{"type": "button", "label": "Click me", "action": "click"}],
                    "imageUrls": None,
                    "jobSummary": {
                        "jobHeadline": "Job headline",
                        "jobSubTitle": "Job subtitle",
                        "jobDetails": "Job details",
                        "jobDate": "2022-01-01",
                        "jobTimeOfDay": "morning",
                    },
                },
            ),
        )
        mocker.patch.object(api, "generate_response", AsyncMock(return_value=mock_response))
        return api

    @pytest.fixture
    def streaming_api(self, mocker):
        api = AIEngineAPI(api_key="test", base_url="https://aiengine.localhost")

        # Create a mock for the streaming response
        async def mock_stream_response(*args, **kwargs):
            yield 'data: {"type": "content", "data": "This is the first part of "}\n\n'
            yield 'data: {"type": "content", "data": "the streaming response."}\n\n'

            # Yield final data with job summary
            yield 'data: {"type": "final_data", "data": {"jobSummary": {"jobHeadline": "Streaming job headline", "jobSubTitle": "Streaming job subtitle", "jobDetails": "Streaming job details", "jobDate": "2022-01-02", "jobTimeOfDay": "afternoon"}, "imageUrls": null, "imageClickableUrls": null}}\n\n'

            # Yield end event
            yield "event: end\ndata: Stream finished\n\n"

        mocker.patch.object(api, "generate_response_stream", mock_stream_response)
        return api

    @pytest.mark.asyncio
    async def test_send_user_message_and_get_system_response(
        self, async_db_session: AsyncSession, api: AIEngineAPI, property_service: PropertyService
    ):
        user = User(
            id=1,
            email="<EMAIL>",
            clerkId="clerk_user_id",
            firstName="Test",
            lastName="User",
            phoneNumber="+91123456789",
            isPhoneNumberVerified=True,
        )
        async_db_session.add(user)
        await async_db_session.flush()
        await property_service.create_property_for_user(user)

        user2 = User(
            id=2,
            email="<EMAIL>",
            clerkId="clerk_user_id2",
            firstName="Test2",
            lastName="User2",
            phoneNumber="+91123456789",
        )

        async_db_session.add(user2)
        await async_db_session.flush()

        message_request = SendMessageRequest(
            chatId=None,
            message=SendMessage(
                content="Hello, world!Hello, world!Hello, world!Hello, world!Hello, world!Hello, world!Hello, world!Hello, world!Hello, world!Hello, world!",
                type="text",
                additionalData=SendMessageMetadata(device="browser"),
            ),
        )

        mock_property_service = AsyncMock()
        mock_property_service.get_first_property_for_user.return_value = None
        message_service = MessageService(async_db_session, mock_property_service, api)
        hubspot_sync_service_mock = AsyncMock()
        job_service = JobService(
            async_db_session,
            mock_property_service,
            AsyncMock(),
            AsyncMock(),
            hubspot_sync_service=hubspot_sync_service_mock,
        )

        response = await message_service.send_user_message_and_get_system_response(message_request, user)

        assert response is not None
        assert response.chatId is not None
        assert response.userMessageId is not None
        assert response.systemMessageId is not None
        assert response.message.content == "This is an example response"
        assert response.message.type == "text"
        assert response.message.additionalData is not None

        chats = (await async_db_session.execute(message_service.list_chats_for_user_query(user))).scalars().all()
        assert len(chats) == 1
        assert chats[0].id == response.chatId
        assert chats[0].title == message_request.message.content[:50]

        last_message_of_the_chat = (
            await async_db_session.execute(
                select(DbMessage).where(DbMessage.chatId == response.chatId).order_by(DbMessage.id.desc())
            )
        ).scalar()
        assert last_message_of_the_chat.additionalData["jobSummary"]["jobId"] == 1

        messages = (
            (await async_db_session.execute(message_service.list_messages_for_chat_query(chats[0].id, user)))
            .scalars()
            .all()
        )
        assert len(messages) == 2

        jobs = (await async_db_session.execute(select(Job).where(Job.userId == user.id))).scalars().all()
        assert len(jobs) == 1

        with pytest.raises(OperationNotAllowed):
            await job_service.accept_job_by_user(jobs[0].id, User(id=999))

        with pytest.raises(JobDoesNotExist):
            await job_service.accept_job_by_user(
                jobs[0].id,
                User(id=999, phoneNumber="123213213", isPhoneNumberVerified=True, usersProperties=[UsersProperties()]),
            )
        job = (await async_db_session.execute(select(Job).where(Job.id == jobs[0].id))).scalar()
        assert job.status == "created"

        await job_service.accept_job_by_user(job.id, user)
        await async_db_session.flush()
        await async_db_session.refresh(job)
        assert job.status == "user_accepted"
        hubspot_sync_service_mock.sync_job.assert_called_once()

        with pytest.raises(OperationNotAllowed):
            await job_service.accept_job_by_user(job.id, user)

        user2 = User(
            id=2,
            email="<EMAIL>",
            clerkId="clerk_user_id2",
            firstName="Test2",
            lastName="User2",
        )
        async_db_session.add(user)
        await async_db_session.flush()

        messages = (
            (await async_db_session.execute(message_service.list_messages_for_chat_query(chats[0].id, user2)))
            .scalars()
            .all()
        )
        assert len(messages) == 0

        document = Document(
            id=1,
            userId=user.id,
            source="user",
            originalFileName="test.pdf",
            fileExtension="pdf",
            sizeInKiloBytes=2,
            browserMimeType="application/pdf",
            s3Key=f"user={user.id}/test.pdf",
            s3Bucket="test-bucket",
            status=DocumentStatusType.processingCompleted,
        )

        documentNotUsers = Document(
            id=2,
            userId=user2.id,
            source="user",
            originalFileName="test.pdf",
            fileExtension="pdf",
            sizeInKiloBytes=2,
            browserMimeType="application/pdf",
            s3Key=f"user={user2.id}/test.pdf",
            s3Bucket="test-bucket",
            status=DocumentStatusType.processingCompleted,
        )
        async_db_session.add(document)
        async_db_session.add(documentNotUsers)
        await async_db_session.flush()

        message_request = SendMessageRequest(
            chatId=None,
            message=SendMessage(
                content="Hello, world!", type="text", additionalData=SendMessageMetadata(device="browser")
            ),
            attachments=[Attachment(documentId=document.id), Attachment(documentId=documentNotUsers.id)],
        )
        api.generate_response = AsyncMock(
            return_value=AIMessageResponse(
                status="success",
                data=ResponseData(
                    chatId=1,
                    response=Message(content="This is an example response", type="text"),
                    additionalData={
                        "category": "general",
                        "confidence": 0.95,
                        "suggested_actions": [{"type": "button", "label": "Click me", "action": "click"}],
                        "imageUrls": None,
                        "jobSummary": {
                            "jobId": 1,
                            "jobHeadline": "Job headline updated",
                            "jobSubTitle": "Job subtitle",
                            "jobDetails": "Job details",
                            "jobDate": "2022-01-01",
                            "jobTimeOfDay": "morning",
                        },
                    },
                ),
            )
        )
        response = await message_service.send_user_message_and_get_system_response(message_request, user)

        job = (await async_db_session.execute(select(Job).where(Job.id == 1))).scalar()
        assert job.headline == "Job headline updated"

        second_to_last_message = (
            await async_db_session.execute(select(DbMessage).where(DbMessage.id == response.userMessageId))
        ).scalar()
        assert second_to_last_message.documents is not None
        assert second_to_last_message.documents[0].id == document.id
        assert document.id in [doc.id for doc in second_to_last_message.documents]
        assert documentNotUsers.id not in [doc.id for doc in second_to_last_message.documents]

        chats = (await async_db_session.execute(message_service.list_chats_for_user_query(user))).scalars().all()
        assert chats[0].id == response.chatId  # the newest first

        firstly_created_chat_id = chats[1].id
        message_request.chatId = firstly_created_chat_id
        await message_service.send_user_message_and_get_system_response(message_request, user)
        chats = (await async_db_session.execute(message_service.list_chats_for_user_query(user))).scalars().all()
        assert chats[0].id == firstly_created_chat_id

    @pytest.mark.asyncio
    async def test_send_user_message_and_get_system_response_stream(
        self, async_db_session: AsyncSession, streaming_api: AIEngineAPI
    ):
        user = User(
            id=3,
            email="<EMAIL>",
            clerkId="clerk_user_id3",
            firstName="Test3",
            lastName="User3",
            phoneNumber="+91987654321",
            isPhoneNumberVerified=True,
        )
        async_db_session.add(user)
        await async_db_session.flush()

        message_request = SendMessageRequest(
            chatId=None,
            message=SendMessage(
                content="Test streaming message",
                type="text",
                additionalData=SendMessageMetadata(device="browser"),
            ),
        )

        mock_property_service = AsyncMock()
        mock_property_service.get_first_property_for_user.return_value = None
        message_service = MessageService(async_db_session, mock_property_service, streaming_api)

        collected_chunks = []
        async for chunk in message_service.send_user_message_and_get_system_response_stream(message_request, user):
            collected_chunks.append(chunk)

        assert len(collected_chunks) == 5

        assert 'data: {"type": "content", "data": "This is the first part of "}\n' in collected_chunks
        assert 'data: {"type": "content", "data": "the streaming response."}\n' in collected_chunks

        assert any("jobSummary" in chunk for chunk in collected_chunks)
        assert any(re.search(r'"jobId": \d', chunk) for chunk in collected_chunks)

        assert "event: end\n" in collected_chunks

        chats = (await async_db_session.execute(message_service.list_chats_for_user_query(user))).scalars().all()
        assert len(chats) == 1
        assert chats[0].title == message_request.message.content[:50]

        user_messages = (
            (
                await async_db_session.execute(
                    select(DbMessage).where(DbMessage.chatId == chats[0].id, DbMessage.senderType == "user")
                )
            )
            .scalars()
            .all()
        )
        assert len(user_messages) == 1
        assert user_messages[0].content == message_request.message.content

        system_messages = (
            (
                await async_db_session.execute(
                    select(DbMessage).where(DbMessage.chatId == chats[0].id, DbMessage.senderType == "system")
                )
            )
            .scalars()
            .all()
        )
        assert len(system_messages) == 1
        assert system_messages[0].content == "This is the first part of the streaming response."

        jobs = (await async_db_session.execute(select(Job).where(Job.userId == user.id))).scalars().all()
        assert len(jobs) == 1
        assert jobs[0].headline == "Streaming job headline"
        assert jobs[0].subTitle == "Streaming job subtitle"
        assert jobs[0].details == "Streaming job details"
        assert jobs[0].urgency == "2022-01-02"
        assert jobs[0].availability == "afternoon"
        assert jobs[0].status == "created"

        assert "jobSummary" in system_messages[0].additionalData
        assert system_messages[0].additionalData["jobSummary"].get("jobId") == jobs[0].id

    @pytest.mark.asyncio
    async def test_streaming_with_existing_chat(self, async_db_session: AsyncSession, streaming_api: AIEngineAPI):
        user = User(
            id=4,
            email="<EMAIL>",
            clerkId="clerk_user_id4",
            firstName="Test4",
            lastName="User4",
        )
        async_db_session.add(user)
        await async_db_session.flush()

        mock_property_service = AsyncMock()
        mock_property_service.get_first_property_for_user.return_value = None
        message_service = MessageService(async_db_session, mock_property_service, streaming_api)

        # Create initial chat
        initial_message = SendMessageRequest(
            chatId=None,
            message=SendMessage(
                content="Initial message to create chat",
                type="text",
                additionalData=SendMessageMetadata(device="browser"),
            ),
        )

        async for _ in message_service.send_user_message_and_get_system_response_stream(initial_message, user):
            pass

        chats = (await async_db_session.execute(message_service.list_chats_for_user_query(user))).scalars().all()
        assert len(chats) == 1
        chat_id = chats[0].id

        second_message = SendMessageRequest(
            chatId=chat_id,
            message=SendMessage(
                content="Second message in existing chat",
                type="text",
                additionalData=SendMessageMetadata(device="browser"),
            ),
        )

        second_chunks = []
        async for chunk in message_service.send_user_message_and_get_system_response_stream(second_message, user):
            second_chunks.append(chunk)

        assert len(second_chunks) == 5

        updated_chats = (
            (await async_db_session.execute(message_service.list_chats_for_user_query(user))).scalars().all()
        )
        assert len(updated_chats) == 1
        assert updated_chats[0].id == chat_id

        all_messages = (
            (await async_db_session.execute(select(DbMessage).where(DbMessage.chatId == chat_id))).scalars().all()
        )
        assert len(all_messages) == 4

        user_message_count = sum(1 for msg in all_messages if msg.senderType == "user")
        system_message_count = sum(1 for msg in all_messages if msg.senderType == "system")
        assert user_message_count == 2
        assert system_message_count == 2

    @pytest.mark.asyncio
    async def test_streaming_with_attachments(self, async_db_session: AsyncSession, streaming_api: AIEngineAPI):
        user = User(
            id=5,
            email="<EMAIL>",
            clerkId="clerk_user_id5",
            firstName="Test5",
            lastName="User5",
        )
        async_db_session.add(user)

        document = Document(
            id=5,
            userId=user.id,
            source="user",
            originalFileName="test_stream.pdf",
            fileExtension="pdf",
            sizeInKiloBytes=2,
            browserMimeType="application/pdf",
            s3Key=f"user={user.id}/test_stream.pdf",
            s3Bucket="test-bucket",
            status=DocumentStatusType.processingCompleted,
        )
        async_db_session.add(document)
        await async_db_session.flush()

        mock_property_service = AsyncMock()
        mock_property_service.get_first_property_for_user.return_value = None
        message_service = MessageService(async_db_session, mock_property_service, streaming_api)

        message_with_attachment = SendMessageRequest(
            chatId=None,
            message=SendMessage(
                content="Message with attachment",
                type="text",
                additionalData=SendMessageMetadata(device="browser"),
            ),
            attachments=[Attachment(documentId=document.id)],
        )

        chunks = []
        async for chunk in message_service.send_user_message_and_get_system_response_stream(
            message_with_attachment, user
        ):
            chunks.append(chunk)

        assert chunks == [
            'data: {"type": "stream_send_message_response", "data": {"chatId": 1, "userMessageId": 1, "systemMessageId": 2}}\n',
            'data: {"type": "content", "data": "This is the first part of "}\n',
            'data: {"type": "content", "data": "the streaming response."}\n',
            'data: {"type": "final_data", "data": {"jobSummary": {"jobHeadline": "Streaming job headline", "jobSubTitle": "Streaming job subtitle", "jobDetails": "Streaming job details", "jobDate": "2022-01-02", "jobTimeOfDay": "afternoon", "jobId": 1}, "imageUrls": null, "imageClickableUrls": null}}\n',
            "event: end\n",
        ]

        user_messages = (await async_db_session.scalars(message_service.list_messages_for_chat_query(1, user))).all()
        actual_messages = model_dump(user_messages, MessageSchema)

        assert len(actual_messages) == 2

        system_message = actual_messages[0]
        assert system_message["content"] == "This is the first part of the streaming response."
        assert system_message["senderType"] == "system"
        assert system_message["type"] == "text"
        assert system_message["id"] == 2
        assert system_message["attachments"] == []
        assert system_message["additionalData"]["jobSummary"]["jobHeadline"] == "Streaming job headline"
        assert system_message["additionalData"]["jobSummary"]["jobId"] == 1

        user_message = actual_messages[1]
        assert user_message["content"] == "Message with attachment"
        assert user_message["senderType"] == "user"
        assert user_message["type"] == "text"
        assert user_message["id"] == 1
        assert len(user_message["attachments"]) == 1
        assert user_message["attachments"][0]["originalFileName"] == "test_stream.pdf"
        assert user_message["attachments"][0]["id"] == 5
        assert user_message["additionalData"]["device"] == "browser"
