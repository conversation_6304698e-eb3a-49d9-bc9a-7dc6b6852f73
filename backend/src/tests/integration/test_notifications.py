import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.notification import Notification
from src.db_models.user import User
from src.schemas import NotificationCreate, NotificationInfo
from src.services.notifications import NotificationService, NotificationDoesNotExist


@pytest.fixture
def notification_service(async_db_session: AsyncSession):
    return NotificationService(async_db_session)


@pytest.fixture
async def user1(async_db_session: AsyncSession):
    user = User(
        id=1,
        email="<EMAIL>",
        clerkId="clerk_user_id_1",
        firstName="User",
        lastName="One",
    )
    async_db_session.add(user)
    return user


@pytest.fixture
async def user2(async_db_session: AsyncSession):
    user = User(
        id=2,
        email="<EMAIL>",
        clerkId="clerk_user_id_2",
        firstName="User",
        lastName="Two",
    )
    async_db_session.add(user)
    return user


@pytest.mark.asyncio
async def test_create_notification(async_db_session: AsyncSession, notification_service: NotificationService, user1):
    notification_data = NotificationCreate(
        context="info",
        payload={"type": "irrelevant_document", "fileName": "test.pdf"},
    )

    notification = await notification_service.create_notification(notification_data, user1.id)

    assert notification is not None
    assert notification.context == "info"
    assert notification.payload == {"type": "irrelevant_document", "fileName": "test.pdf"}
    assert notification.userId == user1.id

    query = notification_service.get_notifications_for_user_query(user1)
    result = await async_db_session.execute(query)
    notifications = result.scalars().all()

    assert len(notifications) == 1
    assert notifications[0].id == notification.id
    assert notifications[0].payload == notification.payload


@pytest.mark.asyncio
async def test_get_notifications_by_context(
    async_db_session: AsyncSession, notification_service: NotificationService, user1
):
    # Create notifications with different contexts
    notification1 = await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test1.pdf"},
        ),
        user1.id,
    )

    notification2 = await notification_service.create_notification(
        NotificationCreate(
            context="warning",
            payload={"type": "document_error", "fileName": "test2.pdf", "someOtherValue": "error"},
        ),
        user1.id,
    )

    notification3 = await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test3.pdf"},
        ),
        user1.id,
    )

    await async_db_session.commit()

    # Get notifications by context "info"
    query = notification_service.get_notifications_for_user_by_context_query(user1, "info")
    result = await async_db_session.execute(query)
    info_notifications = result.scalars().all()

    # Verify only info notifications are returned
    assert len(info_notifications) == 2
    assert all(n.context == "info" for n in info_notifications)
    assert {n.id for n in info_notifications} == {notification1.id, notification3.id}

    # Get notifications by context "warning"
    query = notification_service.get_notifications_for_user_by_context_query(user1, "warning")
    result = await async_db_session.execute(query)
    warning_notifications = result.scalars().all()

    # Verify only warning notifications are returned
    assert len(warning_notifications) == 1
    assert warning_notifications[0].context == "warning"
    assert warning_notifications[0].id == notification2.id


@pytest.mark.asyncio
async def test_delete_all_notifications(
    async_db_session: AsyncSession, notification_service: NotificationService, user1
):
    # Create notifications with different contexts
    await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test1.pdf"},
        ),
        user1.id,
    )

    await notification_service.create_notification(
        NotificationCreate(
            context="warning",
            payload={"type": "document_error", "fileName": "test2.pdf", "someOtherValue": "error"},
        ),
        user1.id,
    )

    await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test3.pdf"},
        ),
        user1.id,
    )

    await async_db_session.commit()

    # Verify notifications exist before deletion
    query = notification_service.get_notifications_for_user_query(user1)
    result = await async_db_session.execute(query)
    notifications_before = result.scalars().all()
    assert len(notifications_before) == 3

    # Delete all notifications for the user
    await notification_service.delete_all_notifications(user1)
    await async_db_session.commit()

    # Verify no notifications remain
    query = notification_service.get_notifications_for_user_query(user1)
    result = await async_db_session.execute(query)
    remaining_notifications = result.scalars().all()

    assert len(remaining_notifications) == 0


@pytest.mark.asyncio
async def test_delete_notifications_by_context(
    async_db_session: AsyncSession, notification_service: NotificationService, user1
):
    # Create notifications with different contexts
    await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test1.pdf"},
        ),
        user1.id,
    )

    notification2 = await notification_service.create_notification(
        NotificationCreate(
            context="warning",
            payload={"type": "document_error", "fileName": "test2.pdf", "someOtherValue": "error"},
        ),
        user1.id,
    )

    await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test3.pdf"},
        ),
        user1.id,
    )

    await async_db_session.commit()

    # Delete all notifications with context "info"
    await notification_service.delete_notifications_by_context("info", user1)
    await async_db_session.commit()

    # Verify only warning notification remains
    query = notification_service.get_notifications_for_user_query(user1)
    result = await async_db_session.execute(query)
    remaining_notifications = result.scalars().all()

    assert len(remaining_notifications) == 1
    assert remaining_notifications[0].context == "warning"
    assert remaining_notifications[0].id == notification2.id


@pytest.mark.asyncio
async def test_delete_notification_by_id(
    async_db_session: AsyncSession, notification_service: NotificationService, user1
):
    # Create notifications with same context
    notification1 = await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test1.pdf"},
        ),
        user1.id,
    )

    notification2 = await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test2.pdf"},
        ),
        user1.id,
    )

    await async_db_session.commit()

    # Delete specific notification by id
    await notification_service.delete_notification_by_id(notification1.id, user1)
    await async_db_session.commit()

    # Verify only notification2 remains
    query = notification_service.get_notifications_for_user_query(user1)
    result = await async_db_session.execute(query)
    remaining_notifications = result.scalars().all()

    assert len(remaining_notifications) == 1
    assert remaining_notifications[0].id == notification2.id


@pytest.mark.asyncio
async def test_delete_notification_by_id_wrong_id_raises_exception(
    async_db_session: AsyncSession, notification_service: NotificationService, user1
):
    await notification_service.create_notification(
        NotificationCreate(
            context="info",
            payload={"type": "irrelevant_document", "fileName": "test.pdf"},
        ),
        user1.id,
    )

    await async_db_session.commit()

    # Try to delete notification with wrong id - should raise NotificationDoesNotExist
    with pytest.raises(NotificationDoesNotExist, match="The notification does not exist."):
        await notification_service.delete_notification_by_id(934563457, user1)
