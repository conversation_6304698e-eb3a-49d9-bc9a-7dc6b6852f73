import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.appliance import Appliance
from src.db_models.chat import Chat
from src.db_models.entity_link import LinkedEntityType
from src.db_models.job import Job
from src.db_models.todo import Todo, TodoType
from src.db_models.user import User
from src.schemas import LinkedEntity
from src.services.entity_links import EntityLinksService


@pytest.fixture
def entity_link_service(async_db_session: AsyncSession):
    return EntityLinksService(async_db_session)


@pytest.fixture
async def user(async_db_session: AsyncSession):
    user = User(
        clerkId="test_clerk_id",
        email="<EMAIL>",
        firstName="Test",
        lastName="User",
    )
    async_db_session.add(user)
    await async_db_session.commit()
    await async_db_session.refresh(user)
    return user


@pytest.fixture
async def chat(async_db_session: AsyncSession, user: User):
    chat = Chat(title="Dummy", userId=user.id)
    async_db_session.add(chat)
    await async_db_session.commit()
    await async_db_session.refresh(chat)
    return chat


@pytest.fixture
async def todo(async_db_session: AsyncSession, user: User):
    todo = Todo(name="Test Todo", type=TodoType.userCreated, userId=user.id)
    async_db_session.add(todo)
    await async_db_session.commit()
    await async_db_session.refresh(todo)
    return todo


@pytest.fixture
async def property(async_db_session: AsyncSession):
    from src.db_models.property import Property

    prop = Property(
        sizeInSqft=1000,
        onFloorLevel=1,
        hasBalconyTerrace=False,
        hasGarden=False,
        hasSwimmingPool=False,
        numberOfBedrooms=2,
        numberOfBathrooms=1,
        numberOfFloors=1,
        yearsOfOwnership=5,
    )
    async_db_session.add(prop)
    await async_db_session.commit()
    await async_db_session.refresh(prop)
    return prop


@pytest.fixture
async def appliance(async_db_session: AsyncSession, property):
    appliance = Appliance(
        type="refrigerator",
        brand="Test Brand",
        model="Test Model",
        propertyId=property.id,
    )
    async_db_session.add(appliance)
    await async_db_session.commit()
    await async_db_session.refresh(appliance)
    return appliance


@pytest.fixture
async def document(async_db_session: AsyncSession, user: User):
    from src.db_models.document import Document, DocumentStatusType, DocumentCategoryType

    document = Document(
        type="test",
        source="user",
        s3Key="test-key-123",
        s3Bucket="test-bucket",
        fileExtension="pdf",
        sizeInKiloBytes=1024,
        originalFileName="test-document.pdf",
        browserMimeType="application/pdf",
        uploadContext="test",
        status=DocumentStatusType.processingCompleted,
        category=DocumentCategoryType.other,
        label="Test Document",
        userId=user.id,
    )
    async_db_session.add(document)
    await async_db_session.commit()
    await async_db_session.refresh(document)
    return document


@pytest.fixture
async def insurance(async_db_session: AsyncSession, user: User, property):
    from src.db_models.insurance import Insurance, InsuranceType

    insurance = Insurance(
        type=InsuranceType.buildingsAndContents,
        policyProvider="Test Insurance Co",
        policyNumber="POL123456",
        userId=user.id,
        propertyId=property.id,
    )
    async_db_session.add(insurance)
    await async_db_session.commit()
    await async_db_session.refresh(insurance)
    return insurance


@pytest.fixture
async def legal(async_db_session: AsyncSession, user: User, property):
    from src.db_models.legal import Legal, LegalType

    legal = Legal(
        type=LegalType.tenancyAgreement,
        aiShortSummary="Test legal document summary",
        userId=user.id,
        propertyId=property.id,
    )
    async_db_session.add(legal)
    await async_db_session.commit()
    await async_db_session.refresh(legal)
    return legal


@pytest.mark.asyncio
async def test_link_retrieval_bidirectional_user_job(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    job: Job,
):
    """Test that links can be retrieved from both entities"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create link
    await entity_link_service.link(user_entity, job_entity, user.id)
    await async_db_session.commit()

    # Test that the link can be retrieved by user_entity
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.jobs
    assert user_links[0].id == job.id
    # Check JobLinkedEntityInfo fields
    assert user_links[0].entity.headline == "Test Headline 1"

    # Test that the link can also be retrieved by job_entity
    job_links = await entity_link_service.get_links(job_entity, user.id)
    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.users
    assert job_links[0].id == user.id
    assert job_links[0].entity.firstName == "Test"
    assert job_links[0].entity.lastName == "User"


@pytest.mark.asyncio
async def test_link_job_to_appliance(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    job: Job,
    appliance: Appliance,
    user: User,
):
    """Test linking a job to an appliance"""
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    appliance_entity = LinkedEntity(entityType=LinkedEntityType.appliances, id=appliance.id)

    # Create link
    await entity_link_service.link(job_entity, appliance_entity, user.id)
    await async_db_session.commit()

    # Test bidirectional retrieval
    job_links = await entity_link_service.get_links(job_entity, user.id)

    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.appliances
    assert job_links[0].id == appliance.id
    # Check ApplianceLinkedEntityInfo fields
    assert job_links[0].entity.type == "refrigerator"
    assert job_links[0].entity.brand == "Test Brand"
    assert job_links[0].entity.model == "Test Model"

    appliance_links = await entity_link_service.get_links(appliance_entity, user.id)
    assert len(appliance_links) == 1
    assert appliance_links[0].entityType == LinkedEntityType.jobs
    assert appliance_links[0].id == job.id
    assert appliance_links[0].entity.headline == "Test Headline 1"


@pytest.mark.asyncio
async def test_link_user_to_document(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    document,
):
    """Test linking a user to a document"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    document_entity = LinkedEntity(entityType=LinkedEntityType.documents, id=document.id)

    # Create link
    await entity_link_service.link(user_entity, document_entity, user.id)
    await async_db_session.commit()

    # Test bidirectional retrieval
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.documents
    assert user_links[0].id == document.id
    # Check DocumentLinkedEntityInfo fields
    assert user_links[0].entity.originalFileName == "test-document.pdf"
    assert user_links[0].entity.sizeInKiloBytes == 1024
    assert user_links[0].entity.label == "Test Document"

    document_links = await entity_link_service.get_links(document_entity, user.id)
    assert len(document_links) == 1
    assert document_links[0].entityType == LinkedEntityType.users
    assert document_links[0].id == user.id
    assert document_links[0].entity.firstName == "Test"
    assert document_links[0].entity.lastName == "User"


@pytest.mark.asyncio
async def test_link_user_to_insurance(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    insurance,
):
    """Test linking a user to an insurance"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    insurance_entity = LinkedEntity(entityType=LinkedEntityType.insurances, id=insurance.id)

    # Create link
    await entity_link_service.link(user_entity, insurance_entity, user.id)
    await async_db_session.commit()

    # Test bidirectional retrieval
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.insurances
    assert user_links[0].id == insurance.id
    # Check InsuranceLinkedEntityInfo fields - removed policyProvider and policyNumber

    insurance_links = await entity_link_service.get_links(insurance_entity, user.id)
    assert len(insurance_links) == 1
    assert insurance_links[0].entityType == LinkedEntityType.users
    assert insurance_links[0].id == user.id
    assert insurance_links[0].entity.firstName == "Test"
    assert insurance_links[0].entity.lastName == "User"


@pytest.mark.asyncio
async def test_link_user_to_legal(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    legal,
):
    """Test linking a user to a legal document"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    legal_entity = LinkedEntity(entityType=LinkedEntityType.legals, id=legal.id)

    # Create link
    await entity_link_service.link(user_entity, legal_entity, user.id)
    await async_db_session.commit()

    # Test bidirectional retrieval
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.legals
    assert user_links[0].id == legal.id
    # Check LegalLinkedEntityInfo fields - removed type and aiShortSummary

    legal_links = await entity_link_service.get_links(legal_entity, user.id)
    assert len(legal_links) == 1
    assert legal_links[0].entityType == LinkedEntityType.users
    assert legal_links[0].id == user.id
    assert legal_links[0].entity.firstName == "Test"
    assert legal_links[0].entity.lastName == "User"


@pytest.mark.asyncio
async def test_link_nonexistent_entities(
    entity_link_service: EntityLinksService, async_db_session: AsyncSession, user: User
):
    nonexistent_user = LinkedEntity(entityType=LinkedEntityType.users, id=99999)
    nonexistent_job = LinkedEntity(entityType=LinkedEntityType.jobs, id=99999)

    with pytest.raises(ValueError, match="User with id 99999 not found"):
        await entity_link_service.link(nonexistent_user, nonexistent_job, user.id)

    with pytest.raises(ValueError, match="Job with id 99999 not found"):
        await entity_link_service.link(nonexistent_job, nonexistent_user, user.id)

    links = await entity_link_service.get_links(nonexistent_user, user.id)
    assert len(links) == 0

    links = await entity_link_service.get_links(nonexistent_job, user.id)
    assert len(links) == 0


@pytest.mark.asyncio
async def test_unlink_entities(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    job: Job,
):
    """Test unlinking entities"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create link first
    await entity_link_service.link(user_entity, job_entity, user.id)
    await async_db_session.commit()

    # Unlink
    await entity_link_service.unlink(user_entity, job_entity, user.id)
    await async_db_session.commit()

    # Verify link is removed
    links = await entity_link_service.get_links(user_entity, user.id)
    assert len(links) == 0


@pytest.mark.asyncio
async def test_unlink_entities_reverse_order(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    todo: Todo,
    chat: Chat,
    user: User,
):
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)

    await entity_link_service.link(todo_entity, chat_entity, user.id)
    await async_db_session.commit()

    # verify the link exists for chat and for todo (and assert the values)
    todo_links = await entity_link_service.get_links(todo_entity, user.id)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.chats
    assert todo_links[0].id == chat.id
    # Check ChatLinkedEntityInfo fields
    assert todo_links[0].entity.title == "Dummy"

    chat_links = await entity_link_service.get_links(chat_entity, user.id)
    assert len(chat_links) == 1
    assert chat_links[0].entityType == LinkedEntityType.todos
    assert chat_links[0].id == todo.id
    # Check TodoLinkedEntityInfo fields
    assert chat_links[0].entity.name == "Test Todo"

    # Unlink
    await entity_link_service.unlink(chat_entity, todo_entity, user.id)
    await async_db_session.commit()

    links = await entity_link_service.get_links(todo_entity, user.id)
    assert len(links) == 0

    links = await entity_link_service.get_links(chat_entity, user.id)
    assert len(links) == 0


@pytest.mark.asyncio
async def test_unlink_bidirectional(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    job: Job,
):
    """Test that unlink works bidirectionally"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create link
    await entity_link_service.link(user_entity, job_entity, user.id)
    await async_db_session.commit()

    # Unlink in reverse order
    await entity_link_service.unlink(job_entity, user_entity, user.id)
    await async_db_session.commit()

    # Verify link is removed
    user_links = await entity_link_service.get_links(user_entity, user.id)
    job_links = await entity_link_service.get_links(job_entity, user.id)

    assert len(user_links) == 0
    assert len(job_links) == 0


@pytest.mark.asyncio
async def test_unlink_all(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    job: Job,
    appliance: Appliance,
):
    """Test unlinking all connections for an entity"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    appliance_entity = LinkedEntity(entityType=LinkedEntityType.appliances, id=appliance.id)

    # Create multiple links
    await entity_link_service.link(user_entity, job_entity, user.id)
    await entity_link_service.link(user_entity, appliance_entity, user.id)
    await async_db_session.commit()

    # Verify multiple links exist and check their fields
    links = await entity_link_service.get_links(user_entity, user.id)
    assert len(links) == 2

    # Sort links by entity type for consistent testing
    links.sort(key=lambda x: x.entityType.value)

    # First link should be appliance
    assert links[0].entityType == LinkedEntityType.appliances
    assert links[0].id == appliance.id
    assert links[0].entity.type == "refrigerator"
    assert links[0].entity.brand == "Test Brand"
    assert links[0].entity.model == "Test Model"

    # Second link should be job
    assert links[1].entityType == LinkedEntityType.jobs
    assert links[1].id == job.id
    assert links[1].entity.headline == "Test Headline 1"

    # Unlink all
    await entity_link_service.unlink_all(user_entity, user.id)
    await async_db_session.commit()

    # Verify all links are removed
    links = await entity_link_service.get_links(user_entity, user.id)
    assert len(links) == 0

    # Verify other entities also have no links to the user
    job_links = await entity_link_service.get_links(job_entity, user.id)
    appliance_links = await entity_link_service.get_links(appliance_entity, user.id)

    assert len(job_links) == 0
    assert len(appliance_links) == 0


@pytest.mark.asyncio
async def test_duplicate_links_ignored(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    job: Job,
):
    """Test that duplicate links are ignored"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)

    # Create same link multiple times
    await entity_link_service.link(user_entity, job_entity, user.id)
    await entity_link_service.link(user_entity, job_entity, user.id)
    await entity_link_service.link(job_entity, user_entity, user.id)
    await async_db_session.commit()

    # Should only have one link
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.jobs
    assert user_links[0].id == job.id
    assert user_links[0].entity.headline == "Test Headline 1"

    job_links = await entity_link_service.get_links(job_entity, user.id)
    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.users
    assert job_links[0].id == user.id
    assert job_links[0].entity.firstName == "Test"
    assert job_links[0].entity.lastName == "User"


@pytest.mark.asyncio
async def test_link_user_to_todo(entity_link_service, user, todo):
    """Test linking a user to a todo"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)

    # Link user to todo
    await entity_link_service.link(user_entity, todo_entity, user.id)

    # Verify link exists in both directions
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.todos
    assert user_links[0].id == todo.id

    todo_links = await entity_link_service.get_links(todo_entity, user.id)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.users
    assert todo_links[0].id == user.id


@pytest.mark.asyncio
async def test_link_project_to_chat(entity_link_service, project, chat, user):
    """Test linking a project to a chat"""
    # Create LinkedEntity objects
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)

    # Link project to chat
    await entity_link_service.link(project_entity, chat_entity, user.id)

    # Verify bidirectional linking
    project_links = await entity_link_service.get_links(project_entity, user.id)
    assert len(project_links) == 1
    assert project_links[0].entityType == LinkedEntityType.chats
    assert project_links[0].id == chat.id

    chat_links = await entity_link_service.get_links(chat_entity, user.id)
    assert len(chat_links) == 1
    assert chat_links[0].entityType == LinkedEntityType.projects
    assert chat_links[0].id == project.id


@pytest.mark.asyncio
async def test_link_todo_to_project(entity_link_service, todo, project, user):
    """Test linking a todo to a project"""
    # Create LinkedEntity objects
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)

    # Link todo to project
    await entity_link_service.link(todo_entity, project_entity, user.id)

    # Verify bidirectional linking
    todo_links = await entity_link_service.get_links(todo_entity, user.id)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.projects
    assert todo_links[0].id == project.id

    project_links = await entity_link_service.get_links(project_entity, user.id)
    assert len(project_links) == 1
    assert project_links[0].entityType == LinkedEntityType.todos
    assert project_links[0].id == todo.id


@pytest.mark.asyncio
async def test_link_chat_to_user(entity_link_service, chat, user):
    """Test linking a chat to a user"""
    # Create LinkedEntity objects
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)

    # Link chat to user
    await entity_link_service.link(chat_entity, user_entity, user.id)

    # Verify bidirectional linking
    chat_links = await entity_link_service.get_links(chat_entity, user.id)
    assert len(chat_links) == 1
    assert chat_links[0].entityType == LinkedEntityType.users
    assert chat_links[0].id == user.id

    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 1
    assert user_links[0].entityType == LinkedEntityType.chats
    assert user_links[0].id == chat.id


@pytest.mark.asyncio
async def test_multiple_links_same_entity(entity_link_service, user, job, project, todo):
    """Test that one entity can be linked to multiple other entities"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)

    # Link user to multiple entities
    await entity_link_service.link(user_entity, job_entity, user.id)
    await entity_link_service.link(user_entity, project_entity, user.id)
    await entity_link_service.link(user_entity, todo_entity, user.id)

    # Verify user has links to all three entities
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 3

    # Check that all expected entity types are linked
    linked_types = {link.entityType for link in user_links}
    assert linked_types == {
        LinkedEntityType.jobs,
        LinkedEntityType.projects,
        LinkedEntityType.todos,
    }

    # Verify each linked entity has a link back to the user
    job_links = await entity_link_service.get_links(job_entity, user.id)
    assert len(job_links) == 1
    assert job_links[0].entityType == LinkedEntityType.users

    project_links = await entity_link_service.get_links(project_entity, user.id)
    assert len(project_links) == 1
    assert project_links[0].entityType == LinkedEntityType.users

    todo_links = await entity_link_service.get_links(todo_entity, user.id)
    assert len(todo_links) == 1
    assert todo_links[0].entityType == LinkedEntityType.users


@pytest.mark.asyncio
async def test_complex_entity_network(entity_link_service, user, job, project, todo, chat, appliance):
    """Test creating a complex network of entity links"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)
    appliance_entity = LinkedEntity(entityType=LinkedEntityType.appliances, id=appliance.id)

    # Create a network: user -> job -> project -> todo -> chat -> appliance
    await entity_link_service.link(user_entity, job_entity, user.id)
    await entity_link_service.link(job_entity, project_entity, user.id)
    await entity_link_service.link(project_entity, todo_entity, user.id)
    await entity_link_service.link(todo_entity, chat_entity, user.id)
    await entity_link_service.link(chat_entity, appliance_entity, user.id)

    # Verify each entity has the expected number of links
    assert len(await entity_link_service.get_links(user_entity, user.id)) == 1
    assert len(await entity_link_service.get_links(job_entity, user.id)) == 2  # linked to user and project
    assert len(await entity_link_service.get_links(project_entity, user.id)) == 2  # linked to job and todo
    assert len(await entity_link_service.get_links(todo_entity, user.id)) == 2  # linked to project and chat
    assert len(await entity_link_service.get_links(chat_entity, user.id)) == 2  # linked to todo and appliance
    assert len(await entity_link_service.get_links(appliance_entity, user.id)) == 1


@pytest.mark.asyncio
async def test_unlink_multiple_entity_types(entity_link_service, user, job, project, todo):
    """Test unlinking works correctly with multiple entity types"""
    # Create LinkedEntity objects
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    project_entity = LinkedEntity(entityType=LinkedEntityType.projects, id=project.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)

    # Create multiple links
    await entity_link_service.link(user_entity, job_entity, user.id)
    await entity_link_service.link(user_entity, project_entity, user.id)
    await entity_link_service.link(user_entity, todo_entity, user.id)

    # Verify all links exist
    assert len(await entity_link_service.get_links(user_entity, user.id)) == 3

    # Unlink one relationship
    await entity_link_service.unlink(user_entity, project_entity, user.id)

    # Verify only the specific link was removed
    user_links = await entity_link_service.get_links(user_entity, user.id)
    assert len(user_links) == 2

    linked_types = {link.entityType for link in user_links}
    assert linked_types == {LinkedEntityType.jobs, LinkedEntityType.todos}

    # Verify the project no longer has a link to the user
    project_links = await entity_link_service.get_links(project_entity, user.id)
    assert len(project_links) == 0


# Router endpoint tests
@pytest.mark.asyncio
async def test_get_entity_links_endpoint_returns_concrete_types(
    entity_link_service: EntityLinksService,
    async_db_session: AsyncSession,
    user: User,
    job: Job,
    appliance,
    document,
    todo: Todo,
    chat: Chat,
    insurance,
    legal,
):
    """Test that the router endpoint returns concrete types for all entity types"""

    # Create links between user and various entity types
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)
    job_entity = LinkedEntity(entityType=LinkedEntityType.jobs, id=job.id)
    appliance_entity = LinkedEntity(entityType=LinkedEntityType.appliances, id=appliance.id)
    document_entity = LinkedEntity(entityType=LinkedEntityType.documents, id=document.id)
    todo_entity = LinkedEntity(entityType=LinkedEntityType.todos, id=todo.id)
    chat_entity = LinkedEntity(entityType=LinkedEntityType.chats, id=chat.id)
    insurance_entity = LinkedEntity(entityType=LinkedEntityType.insurances, id=insurance.id)
    legal_entity = LinkedEntity(entityType=LinkedEntityType.legals, id=legal.id)

    # Create all the links
    await entity_link_service.link(user_entity, job_entity, user.id)
    await entity_link_service.link(user_entity, appliance_entity, user.id)
    await entity_link_service.link(user_entity, document_entity, user.id)
    await entity_link_service.link(user_entity, todo_entity, user.id)
    await entity_link_service.link(user_entity, chat_entity, user.id)
    await entity_link_service.link(user_entity, insurance_entity, user.id)
    await entity_link_service.link(user_entity, legal_entity, user.id)
    await async_db_session.commit()

    # Test the service directly to ensure it returns concrete types
    links = await entity_link_service.get_links(user_entity, user.id)
    assert len(links) == 7

    # Find each entity type and validate its specific fields
    jobs_links = [link for link in links if link.entityType == LinkedEntityType.jobs]
    appliances_links = [link for link in links if link.entityType == LinkedEntityType.appliances]
    documents_links = [link for link in links if link.entityType == LinkedEntityType.documents]
    todos_links = [link for link in links if link.entityType == LinkedEntityType.todos]
    chats_links = [link for link in links if link.entityType == LinkedEntityType.chats]
    insurances_links = [link for link in links if link.entityType == LinkedEntityType.insurances]
    legals_links = [link for link in links if link.entityType == LinkedEntityType.legals]

    # Assert exactly one of each type exists
    assert len(jobs_links) == 1
    assert len(appliances_links) == 1
    assert len(documents_links) == 1
    assert len(todos_links) == 1
    assert len(chats_links) == 1
    assert len(insurances_links) == 1
    assert len(legals_links) == 1

    # Validate jobs entity fields
    jobs_link = jobs_links[0]
    assert hasattr(jobs_link, "entity")
    assert hasattr(jobs_link.entity, "headline")
    assert jobs_link.entity.headline == "Test Headline 1"

    # Validate appliances entity fields
    appliances_link = appliances_links[0]
    assert hasattr(appliances_link, "entity")
    assert hasattr(appliances_link.entity, "type")
    assert appliances_link.entity.type == "refrigerator"

    # Validate documents entity fields
    documents_link = documents_links[0]
    assert hasattr(documents_link, "entity")
    assert hasattr(documents_link.entity, "originalFileName")
    assert documents_link.entity.originalFileName == "test-document.pdf"

    # Validate todos entity fields
    todos_link = todos_links[0]
    assert hasattr(todos_link, "entity")
    assert hasattr(todos_link.entity, "name")
    assert todos_link.entity.name == "Test Todo"

    # Validate chats entity fields
    chats_link = chats_links[0]
    assert hasattr(chats_link, "entity")
    assert hasattr(chats_link.entity, "title")
    assert chats_link.entity.title == "Dummy"


@pytest.mark.asyncio
async def test_get_entity_links_endpoint_empty_result(
    entity_link_service: EntityLinksService,
    user: User,
):
    """Test that the endpoint returns empty list when no links exist"""
    user_entity = LinkedEntity(entityType=LinkedEntityType.users, id=user.id)

    # Test with no links
    links = await entity_link_service.get_links(user_entity, user.id)
    assert len(links) == 0
    assert links == []
