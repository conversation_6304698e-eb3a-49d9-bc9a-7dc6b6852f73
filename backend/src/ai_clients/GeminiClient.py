import asyncio
import logging
import mimetypes
import os
from pathlib import Path

from google import genai
from google.genai import types


class GeminiClient:
    def __init__(self):
        self.logger = logging.getLogger("uvicorn")
        self.gemini_flash_model = "gemini-2.5-flash"

        # Configure Gemini
        self.gemini_api_key = os.environ.get("GOOGLE_API_KEY")
        if not self.gemini_api_key:
            self.logger.warning("GOOGLE_API_KEY not found in environment variables")
        else:
            self.genai_client = genai.Client()

    async def process_file_gemini_flash(
        self, file_path: Path, describing_llm_prompt: str, is_json_response: bool = False
    ) -> str:
        self.logger.info(f"Starting processing for file: {file_path}")
        mime_type, _ = mimetypes.guess_type(file_path)

        if not mime_type:
            error_message = f"Could not determine MIME type for file: {file_path}. Please check the file extension."
            self.logger.error(error_message)
            raise ValueError(error_message)

        self.logger.info(f"Detected MIME type '{mime_type}' for file {file_path}")

        if mime_type == "application/pdf":
            self.logger.info(f"Delegating to PDF processing for: {file_path}")
            return await self.process_pdf_gemini_flash(file_path, describing_llm_prompt, is_json_response)
        elif mime_type.startswith("image/"):
            self.logger.info(f"Delegating to image processing for: {file_path}")
            return await self.process_image_gemini_flash(file_path, mime_type, describing_llm_prompt, is_json_response)
        else:
            error_message = (
                f"Unsupported file type for {file_path}. "
                f"Detected MIME type: {mime_type}. Only PDFs and images are supported."
            )
            self.logger.error(error_message)
            raise ValueError(error_message)

    async def process_pdf_gemini_flash(
        self, pdf_path: Path, describing_llm_prompt: str, is_json_response: bool = False
    ) -> str:
        return await self._process_pdf_gemini(
            pdf_path, describing_llm_prompt, self.gemini_flash_model, is_json_response
        )

    async def process_image_gemini_flash(
        self, image_path: Path, mime_type: str, describing_llm_prompt: str, is_json_response: bool = False
    ) -> str:
        return await self._process_image_gemini(
            image_path, mime_type, describing_llm_prompt, self.gemini_flash_model, is_json_response
        )

    async def process_text_prompt_gemini_flash(self, text_prompt: str, is_json_response: bool = False) -> str:
        """Process a text prompt using Gemini Flash model.

        Args:
            text_prompt (str): The text prompt to process
            is_json_response (bool, optional): Whether to expect JSON response format. Defaults to False.

        Returns:
            str: Response from Gemini model

        Raises:
            Exception: If there's an error generating content with Gemini
        """
        return await self._process_text_gemini(text_prompt, self.gemini_flash_model, is_json_response)

    async def _process_pdf_gemini(
        self, pdf_path: Path, describing_llm_prompt: str, gemini_model: str, is_json_response: bool
    ) -> str:
        """Upload PDF directly to Gemini and get description asynchronously.

        This refactored method removes the need for PDF to image conversion,
        uploading the PDF file bytes directly to the Gemini model for analysis.

        Args:
            pdf_path (Path): Path to the PDF file.
            describing_llm_prompt (str, optional): Custom prompt for PDF analysis.
                                                   Defaults to self.pdf_prompt.

        Returns:
            str: Description of the PDF content provided by Gemini.

        Raises:
            Exception: If there's an error processing or uploading the PDF,
                       or generating content with Gemini.
        """
        self.logger.info(f"Processing PDF directly with Gemini: {pdf_path}")

        try:
            # Read PDF file content into bytes.
            # Use asyncio.to_thread for I/O operations to prevent blocking the event loop.
            pdf_data = await asyncio.to_thread(pdf_path.read_bytes)
            self.logger.info(f"Successfully read PDF file {pdf_path} into bytes.")

            # Prepare the PDF as a Part object for Gemini.
            # This is the core change: direct PDF upload, no image conversion needed.
            pdf_part = genai.types.Part.from_bytes(
                data=pdf_data,
                mime_type="application/pdf",
            )

            # Prepare message content for Gemini.
            # The PDF Part typically comes first, followed by the text prompt for multi-modal input.
            contents = [pdf_part, describing_llm_prompt]

            config_params = {"max_output_tokens": 300000, "top_p": 0.8, "temperature": 0.4}

            if is_json_response:
                config_params["response_mime_type"] = "application/json"

            # Generate content asynchronously using the Gemini model.
            response = await self.genai_client.aio.models.generate_content(
                model=gemini_model, contents=contents, config=types.GenerateContentConfig(**config_params)
            )

            # Extract and return the description from the model's response.
            description = response.text

            self.logger.info(f"Successfully processed PDF with Gemini: {pdf_path}")
            return description

        except Exception as e:
            self.logger.exception(f"Error processing PDF with Gemini {pdf_path}: {str(e)}")
            raise

    async def _process_image_gemini(
        self, image_path: Path, mime_type: str, describing_llm_prompt: str, gemini_model: str, is_json_response: bool
    ) -> str:
        """Process an image file using Google's Gemini Flash 1.5 asynchronously.

        Args:
            image_path (Path): Path to the image file
            describing_llm_prompt (str, optional): Custom prompt for image analysis

        Returns:
            str: Description of the image content

        Raises:
            Exception: If there's an error processing the image
        """
        self.logger.info(f"Processing image with Gemini Flash: {image_path}")

        try:
            # Read the image file asynchronously
            image_data = await asyncio.to_thread(image_path.read_bytes)

            image_part = genai.types.Part.from_bytes(
                data=image_data,
                mime_type=mime_type,
            )

            config_params = {"max_output_tokens": 300000, "top_p": 0.8, "temperature": 0.4}

            if is_json_response:
                config_params["response_mime_type"] = "application/json"

            # Prepare message content for Gemini.
            # Explicitly create a text part for the prompt.
            contents = [describing_llm_prompt, image_part]

            # Generate content asynchronously using the Gemini model.
            response = await self.genai_client.aio.models.generate_content(
                model=gemini_model, contents=contents, config=types.GenerateContentConfig(**config_params)
            )

            # Extract and return the description
            description = response.text

            self.logger.info(f"Successfully processed image with Gemini Flash: {image_path}")
            return description

        except Exception as e:
            self.logger.exception(f"Error processing image with Gemini Flash {image_path}: {str(e)}")
            raise

    async def _process_text_gemini(self, text_prompt: str, gemini_model: str, is_json_response: bool) -> str:
        """Process a text prompt using Google's Gemini model asynchronously.

        Args:
            text_prompt (str): The text prompt to process
            gemini_model (str): The Gemini model to use
            is_json_response (bool): Whether to expect JSON response format

        Returns:
            str: Response from Gemini model

        Raises:
            Exception: If there's an error generating content with Gemini
        """
        self.logger.info(f"Processing text prompt with Gemini model: {gemini_model}")

        try:
            config_params = {"max_output_tokens": 300000, "top_p": 0.8, "temperature": 0.4}

            if is_json_response:
                config_params["response_mime_type"] = "application/json"

            # Generate content asynchronously using the Gemini model
            response = await self.genai_client.aio.models.generate_content(
                model=gemini_model, contents=[text_prompt], config=types.GenerateContentConfig(**config_params)
            )

            # Extract and return the response
            result = response.text

            self.logger.info(f"Successfully processed text prompt with Gemini model: {gemini_model}")
            return result

        except Exception as e:
            self.logger.exception(f"Error processing text prompt with Gemini model {gemini_model}: {str(e)}")
            raise
