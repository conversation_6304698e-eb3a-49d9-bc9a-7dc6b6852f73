import os
from openai import OpenAI
from typing import List


class OpenAIClient:
    def __init__(self):
        self._open_ai_api_key = os.environ["OPEN_AI_API_KEY"]
        self._openai_client = OpenAI(api_key=self._open_ai_api_key)

    def get_embedding(self, content: str) -> List[float]:
        # Rough estimate: 1 token ≈ 4 characters for English text
        max_chars = 8192 * 3  # Very conservative estimate
        if len(content) > max_chars:
            content = content[:max_chars]
        embedding = (
            self._openai_client.embeddings.create(model="text-embedding-3-small", input=content).data[0].embedding
        )
        return embedding
