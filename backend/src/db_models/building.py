from enum import Enum
from typing import Optional

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class NumberOfFlatsType(Enum):
    two = "2"
    threeToTen = "3-10"
    elevenToFourtyNine = "11-49"
    fiftyPlus = "50+"


class Building(BaseModel):
    __tablename__ = "buildings"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str | None]
    numberOfFlats: Mapped[NumberOfFlatsType | None]
    freeholder: Mapped[str | None]
    hasConcierge: Mapped[bool | None]
    hasSwimmingPool: Mapped[bool | None]
    hasMailRoom: Mapped[bool | None]
    hasBicycleStore: Mapped[bool | None]
    hasGym: Mapped[bool | None]
    otherDetails: Mapped[str | None]
    # relationships:
    managingAgentId: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    managingAgent: Mapped[Optional["User"]] = relationship(back_populates="buildingsManaged")
    properties: Mapped[list["Property"]] = relationship(back_populates="building")
