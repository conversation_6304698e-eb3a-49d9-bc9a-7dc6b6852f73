from decimal import Decimal
from typing import Any

from sqlalchemy import Integer, BigInteger, Numeric
from sqlalchemy.orm import relationship, Mapped, mapped_column

from .base import BaseModel
from .relationships import users_addresses


class Address(BaseModel):
    __tablename__ = "addresses"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    streetLine1: Mapped[str]
    streetLine2: Mapped[str | None]
    streetLine3: Mapped[str | None]
    townOrCity: Mapped[str]
    postcode: Mapped[str]
    locality: Mapped[str | None]
    country: Mapped[str]
    houseAccess: Mapped[str | None]
    parkingInstructions: Mapped[str | None]
    uprn: Mapped[int | None] = mapped_column(BigInteger)
    udprn: Mapped[int | None] = mapped_column(BigInteger)
    umprn: Mapped[int | None] = mapped_column(BigInteger)
    latitude: Mapped[Decimal | None] = mapped_column(Numeric(precision=8, scale=6))
    longitude: Mapped[Decimal | None] = mapped_column(Numeric(precision=8, scale=6))
    osPlacesOutput: Mapped[dict[str, Any] | None]
    idealPostcodesOutput: Mapped[dict[str, Any] | None]
    users: Mapped[list["User"]] = relationship("User", secondary=users_addresses, back_populates="addresses")
    property: Mapped["Property"] = relationship(back_populates="address", lazy="selectin")
    bookings: Mapped[list["Booking"]] = relationship(back_populates="address")
