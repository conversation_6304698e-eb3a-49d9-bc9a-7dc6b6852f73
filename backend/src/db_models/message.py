from datetime import datetime
from enum import StrEnum
from typing import Optional, Any

from sqlalchemy import DateTime, ForeignKey, Index, Integer, String, desc
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql import func

from .base import BaseModel


class MessageType(StrEnum):
    TEXT = "text"
    IMAGE = "image"
    DIAGNOSTIC_REPORT = "diagnostic_report"


class SenderType(StrEnum):
    USER = "user"
    SYSTEM = "system"
    CUSTOMER_SUPPORT = "customer_support"


class Message(BaseModel):
    __tablename__ = "messages"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    content: Mapped[str | None]
    type: Mapped[MessageType] = mapped_column(String, nullable=False)
    senderType: Mapped[SenderType] = mapped_column(String, nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)
    additionalData: Mapped[dict[str, Any] | None]
    chatId: Mapped[int] = mapped_column(ForeignKey("chats.id"), index=True)
    chat: Mapped["Chat"] = relationship(back_populates="messages")
    userId: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    user: Mapped[Optional["User"]] = relationship(back_populates="messages")
    documents: Mapped[list["Document"]] = relationship(back_populates="message", lazy="selectin")

    __table_args__ = (Index("ix_messages_chat_id_timestamp", "chatId", desc("timestamp")),)
