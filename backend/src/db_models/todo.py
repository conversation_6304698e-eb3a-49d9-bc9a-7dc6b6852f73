from datetime import datetime
from enum import StrEnum

from sqlalchemy import Integer, ForeignKey, DateTime, String
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel


class TodoType(StrEnum):
    userCreated = "userCreated"
    systemCreated = "systemCreated"
    systemCreatedUserAccepted = "systemCreatedUserAccepted"
    systemCreatedUserRejected = "systemCreatedUserRejected"
    systemCreatedSystemExpired = "systemCreatedSystemExpired"


class ToDoSource(StrEnum):
    user = "user"
    system = "system"


class ToDoStatus(StrEnum):
    created = "created"
    accepted = "accepted"
    rejected = "rejected"
    expired = "expired"


class Todo(BaseModel):
    __tablename__ = "todos"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[TodoType] = mapped_column(String, nullable=False, index=True)
    source: Mapped[ToDoSource | None] = mapped_column(String, nullable=True, index=True)
    status: Mapped[ToDoStatus] = mapped_column(
        String, nullable=True, index=True, server_default=ToDoStatus.created.value
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str | None] = mapped_column(String, nullable=True)
    dueDate: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
    doneDate: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
    deletedDate: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"), index=True)
    user: Mapped["User"] = relationship(back_populates="todos")

    # Analytics
    firstShownAt: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
