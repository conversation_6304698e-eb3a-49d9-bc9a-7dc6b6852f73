from datetime import datetime
from typing import Optional

from sqlalchemy import Integer, ForeignKey, and_
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel
from .document import Document
from .source_link import SourceLink, SourceType, sourced_documents


class Appliance(BaseModel):
    __tablename__ = "appliances"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[str]
    brand: Mapped[str | None]
    model: Mapped[str | None]
    serialNumber: Mapped[str | None]
    warranty: Mapped[str | None]
    dateOfPurchase: Mapped[datetime | None]
    otherDetails: Mapped[str | None]  # description, service history, etc.
    # relationships:
    propertyId: Mapped[int] = mapped_column(ForeignKey("properties.id"))
    property: Mapped["Property"] = relationship(back_populates="appliances")
    invoiceReceiptDocumentId: Mapped[int | None] = mapped_column(ForeignKey("documents.id"))
    invoiceReceiptDocument: Mapped[Optional["Document"]] = relationship(back_populates="appliances")
    sourcedDocuments: Mapped[list["Document"]] = sourced_documents('appliances', id)
