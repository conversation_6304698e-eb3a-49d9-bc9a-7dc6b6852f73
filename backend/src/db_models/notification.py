from typing import Literal

from pydantic import BaseModel as PydanticBaseModel, field_serializer
from sqlalchemy import Integer, ForeignKey, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel
from .document import DocumentCategoryType


class IrrelevantDocumentNotification(PydanticBaseModel):
    type: Literal["irrelevant_document"] = "irrelevant_document"
    fileName: str


class ErrorDocumentNotification(PydanticBaseModel):
    type: Literal["document_error"] = "document_error"
    fileName: str


class DocumentCategorizedNotification(PydanticBaseModel):
    type: Literal["document_categorized"] = "document_categorized"
    fileName: str


class InformationFromDocumentSavedNotification(PydanticBaseModel):
    type: Literal["information_from_document_saved"] = "information_from_document_saved"
    fileName: str
    documentId: int
    category: DocumentCategoryType

    @field_serializer('category')
    def serialize_category(self, category: DocumentCategoryType) -> str:
        return category.value


NotificationPayload = (
        IrrelevantDocumentNotification |
        ErrorDocumentNotification |
        DocumentCategorizedNotification |
        InformationFromDocumentSavedNotification
)


class Notification(BaseModel):
    __tablename__ = "notifications"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    context: Mapped[str | None] = mapped_column(String, nullable=True, index=True)
    payload: Mapped[NotificationPayload] = mapped_column(JSONB, nullable=False)
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"), index=True)
    user: Mapped["User"] = relationship(back_populates="notifications")
