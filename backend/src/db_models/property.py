from enum import Enum

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.ext.associationproxy import AssociationProxy
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel
from .source_link import sourced_documents


class PropertyType(Enum):
    house = "house"
    flat = "flat"
    office = "office"
    retail = "retail"


class PropertySubgroupType(Enum):
    detached = "detached"
    semiDetached = "semiDetached"
    terraced = "terraced"
    NOT_APPLICABLE = "NOT_APPLICABLE"


class PropertyTenureType(Enum):
    freehold = "freehold"
    leasehold = "leasehold"
    shareOfFreehold = "shareOfFreehold"
    NOT_APPLICABLE = "NOT_APPLICABLE"


class PropertyConditionType(Enum):
    excellent = "excellent"
    good = "good"
    fair = "fair"
    poor = "poor"


class PropertyConservationStatusType(Enum):
    gradeI = "gradeI"
    gradeIIstar = "gradeII*"
    gradeII = "gradeII"
    conservationArea = "conservationArea"
    buildingPreservationNotice = "buildingPreservationNotice"
    localHeritageList = "localHeritageList"
    other = "other"


class PropertyEpcRatingType(Enum):
    a = "A"
    b = "B"
    c = "C"
    d = "D"
    e = "E"
    f = "F"
    g = "G"


class Property(BaseModel):
    __tablename__ = "properties"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[PropertyType | None]
    subgroupType: Mapped[PropertySubgroupType | None]
    tenureType: Mapped[PropertyTenureType | None]
    # details:
    sizeInSqft: Mapped[int | None]  # gross internal area
    onFloorLevel: Mapped[int | None]
    hasBalconyTerrace: Mapped[bool | None]
    balconyTerraceDetails: Mapped[str | None]
    hasGarden: Mapped[bool | None]
    gardenDetails: Mapped[str | None]
    hasSwimmingPool: Mapped[bool | None]
    swimmingPoolDetails: Mapped[str | None]
    numberOfBedrooms: Mapped[int | None]
    numberOfBathrooms: Mapped[int | None]
    numberOfFloors: Mapped[int | None]
    lastSoldPriceInGbp: Mapped[int | None]
    condition: Mapped[PropertyConditionType | None]
    yearsOfOwnership: Mapped[int | None]
    architecturalType: Mapped[str | None]
    valuationInGbp: Mapped[int | None]
    conservationStatus: Mapped[PropertyConservationStatusType | None]
    typeOfLock: Mapped[str | None]
    typeOfConstruction: Mapped[str | None]
    proportionOfFlatRoof: Mapped[int | None]  # percentage
    epcRating: Mapped[PropertyEpcRatingType | None]
    # relationships:
    floorPlanDocumentId: Mapped[int | None]
    floorPlanDocument: Mapped["Document"] = relationship(back_populates="property", viewonly=True)
    propertySurveyDocumentId: Mapped[int | None]
    propertySurveyDocument: Mapped["Document"] = relationship(back_populates="property", viewonly=True)
    epcCertificateDocumentId: Mapped[int | None]
    epcCertificateDocument: Mapped["Document"] = relationship(back_populates="property", viewonly=True)
    buildingId: Mapped[int | None] = mapped_column(ForeignKey("buildings.id"))
    building: Mapped["Building"] = relationship(back_populates="properties", lazy="selectin", cascade="all, delete")
    addressId: Mapped[int | None] = mapped_column(ForeignKey("addresses.id"))
    address: Mapped["Address"] = relationship(back_populates="property", lazy="selectin", cascade="all, delete")
    usersProperties: Mapped[list["UsersProperties"]] = relationship(
        back_populates="property", lazy="selectin", cascade="all, delete"
    )
    users: AssociationProxy[list["User"]] = association_proxy("usersProperties", "user")
    documents: Mapped[list["Document"]] = relationship(back_populates="property")
    appliances: Mapped[list["Appliance"]] = relationship(back_populates="property")
    chats: Mapped[list["Chat"]] = relationship(back_populates="property")
    jobs: Mapped[list["Job"]] = relationship(back_populates="property")
    projects: Mapped[list["Project"]] = relationship(back_populates="property")
    bills: Mapped[list["Bill"]] = relationship(back_populates="property")
    insurances: Mapped[list["Insurance"]] = relationship(back_populates="property")
    legals: Mapped[list["Legal"]] = relationship(back_populates="property")
    sourcedDocuments: Mapped[list["Document"]] = sourced_documents('properties', id)
