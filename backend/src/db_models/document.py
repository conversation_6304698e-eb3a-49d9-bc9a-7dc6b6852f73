from enum import Enum

from sqlalchemy import Integer, Foreign<PERSON><PERSON>, <PERSON>, <PERSON>olean
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class DocumentStatusType(Enum):
    saved = "saved"
    processing = "processing"
    processingCompleted = "processingCompleted"
    irrelevant = "irrelevant"
    error = "error"


class DocumentCategoryType(Enum):
    propertyDetails = "propertyDetails"
    insurance = "insurance"
    billsAndSubscriptions = "billsAndSubscriptions"
    legal = "legal"
    appliance = "appliance"
    buildingInformation = "buildingInformation"
    todo = "todo"
    other = "other"


class Document(BaseModel):
    __tablename__ = "documents"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[str | None]  # user photo, warranty etc.
    source: Mapped[str]  # user, system, customer_support
    s3Key: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    s3Bucket: Mapped[str]
    fileExtension: Mapped[str]
    sizeInKiloBytes: Mapped[int]
    originalFileName: Mapped[str]
    aiGeneratedFileName: Mapped[str | None]
    browserMimeType: Mapped[str | None]
    uploadContext: Mapped[str | None]
    status: Mapped[DocumentStatusType]
    category: Mapped[DocumentCategoryType | None]
    label: Mapped[str | None]
    errorMessage: Mapped[str | None]
    # relationships:
    userId: Mapped[int | None] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="documents")
    propertyId: Mapped[int | None] = mapped_column(ForeignKey("properties.id"))
    property: Mapped["Property"] = relationship(back_populates="documents")
    messageId: Mapped[int | None] = mapped_column(ForeignKey("messages.id"))
    message: Mapped["Message"] = relationship(back_populates="documents")
    appliances: Mapped[list["Appliance"]] = relationship(back_populates="invoiceReceiptDocument")
    bills: Mapped[list["Bill"]] = relationship(back_populates="document")
    insurances: Mapped[list["Insurance"]] = relationship(back_populates="document")
    legals: Mapped[list["Legal"]] = relationship(back_populates="document")
