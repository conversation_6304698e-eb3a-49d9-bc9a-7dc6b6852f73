from enum import Enum

from sqlalchemy import Integer, UniqueConstraint, Index, String, and_
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel
from .document import Document


def sourced_documents(dest_table: str, id: Mapped[int]):
    return relationship(
        secondary="source_links",
        primaryjoin=and_(SourceLink.destTable == dest_table, SourceLink.destId == id),
        secondaryjoin=and_(SourceLink.srcType == SourceType.document, SourceLink.srcId == Document.id),
        viewonly=True,
        lazy="selectin"
    )


class SourceType(Enum):
    chat = "chat"
    document = "document"
    userInput = "userInput"


class SourceLink(BaseModel):
    __tablename__ = "source_links"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    destTable: Mapped[str] = mapped_column(String, index=True, nullable=False)
    destId: Mapped[int] = mapped_column(Integer, index=True, nullable=False)
    destField: Mapped[str] = mapped_column(String, index=True, nullable=False)
    srcType: Mapped[SourceType]
    srcId: Mapped[int | None]
    __table_args__ = (UniqueConstraint("destTable", "destId", "destField"), Index("destTable", "destId", "destField"))
