from datetime import datetime
from enum import StrEnum
from typing import Optional

from sqlalchemy import DateTime, Foreign<PERSON>ey, Integer, Enum, String, text
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql import func

from .base import BaseModel
from .relationships import jobs_professionals


class JobStatus(StrEnum):
    INCOMPLETE = "incomplete"
    CREATED = "created"
    USER_ACCEPTED = "user_accepted"
    QUOTING_STARTED = "quoting_started"
    QUOTING_COMPLETED = "quoting_completed"
    MORE_QUOTES_REQUESTED = "more_quotes_requested"
    BOOKED = "booked"
    COMPLETED = "completed"
    DISPUTED = "disputed"
    PAID = "paid"
    CANCELLED = "cancelled"


class Job(BaseModel):
    __tablename__ = "jobs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    reference: Mapped[str] = mapped_column(
        String(10),
        unique=True,
        index=True,
        nullable=False,
        server_default=text("UPPER(LEFT(TRANSLATE(MD5(RANDOM()::TEXT || CLOCK_TIMESTAMP()::TEXT), '0o', ''), 8))"),
    )
    headline: Mapped[str]
    subTitle: Mapped[str]
    details: Mapped[str]
    urgency: Mapped[str]
    availability: Mapped[str | None]  # Later on based on specific calendar dates/ranges
    status: Mapped[JobStatus] = mapped_column(String, default=JobStatus.CREATED, index=True)
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)
    projectId: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    project: Mapped["Project"] = relationship(back_populates="jobs")
    chatId: Mapped[int] = mapped_column(ForeignKey("chats.id"))
    chat: Mapped["Chat"] = relationship(back_populates="jobs")
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="jobs", lazy="selectin")
    propertyId: Mapped[int | None] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="jobs")
    quotes: Mapped[list["Quote"]] = relationship(back_populates="job", lazy="selectin")
    serviceProviderId: Mapped[int | None] = mapped_column(ForeignKey("service_providers.id"))
    serviceProvider: Mapped[Optional["ServiceProvider"]] = relationship(back_populates="jobs")
    professionals: Mapped[list["Professional"]] = relationship(
        "Professional", secondary=jobs_professionals, back_populates="jobs", lazy="selectin"
    )
    hubspotId: Mapped[str | None] = mapped_column(String, unique=True, index=True, nullable=True)
    hubspotSyncAt: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
