from enum import Enum
from typing import Optional

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel


class LegalType(Enum):
    tenancyAgreement = "tenancyAgreement"
    leaseholdAgreement = "leaseholdAgreement"
    sharedOwnershipAgreement = "sharedOwnershipAgreement"
    planningApplicationsAndPermissions = "planningApplicationsAndPermissions"
    partyWallAgreements = "partyWallAgreements"
    wills = "wills"
    deeds = "deeds"
    contracts = "contracts"
    stampDutyReceipt = "stampDutyReceipt"
    landTitle = "landTitle"
    other = "other"


class Legal(BaseModel):
    __tablename__ = "legals"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[LegalType]
    aiShortSummary: Mapped[str]  # AI generated short summary of the document
    aiDetailedSummary: Mapped[str | None]  # AI generated long summary of the document
    generatedPrompts: Mapped[str | None]  # AI generated prompts
    # relationships:
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="legals")
    propertyId: Mapped[int | None] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="legals")
    documentId: Mapped[int | None] = mapped_column(ForeignKey("documents.id"))
    document: Mapped["Document"] = relationship(back_populates="legals")
