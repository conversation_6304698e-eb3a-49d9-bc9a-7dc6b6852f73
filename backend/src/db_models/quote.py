from datetime import datetime
from enum import StrEnum
from typing import Optional

from sqlalchemy import DateTime, Foreign<PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql import func

from .base import BaseModel


class QuoteStatus(StrEnum):
    OPEN = "open"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class Quote(BaseModel):
    __tablename__ = "quotes"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    costLowest: Mapped[int]
    costHighest: Mapped[int]
    availability: Mapped[str | None]  # just string?
    status: Mapped[QuoteStatus] = mapped_column(String, default=QuoteStatus.OPEN)
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)
    jobId: Mapped[int] = mapped_column(ForeignKey("jobs.id"))
    job: Mapped["Job"] = relationship(back_populates="quotes")
    serviceProviderId: Mapped[int] = mapped_column(ForeignKey("service_providers.id"))
    serviceProvider: Mapped["ServiceProvider"] = relationship(back_populates="quotes")
