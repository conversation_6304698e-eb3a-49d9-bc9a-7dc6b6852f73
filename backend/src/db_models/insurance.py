from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class InsuranceType(Enum):
    buildingsOnly = "buildingsOnly"
    contentsOnly = "contentsOnly"
    buildingsAndContents = "buildingsAndContents"
    landlords = "landlords"
    tenants = "tenants"
    other = "other"


class Insurance(BaseModel):
    __tablename__ = "insurances"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[InsuranceType]
    policyProvider: Mapped[str]
    policyNumber: Mapped[str]
    renewalDate: Mapped[datetime | None]
    annualPremium: Mapped[Decimal | None]
    limits: Mapped[str | None]
    excessDeductible: Mapped[str | None]
    whatsCovered: Mapped[str | None]
    exclusions: Mapped[str | None]
    # relationships:
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="insurances")
    propertyId: Mapped[int | None] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="insurances")
    documentId: Mapped[int | None] = mapped_column(ForeignKey("documents.id"))
    document: Mapped["Document"] = relationship(back_populates="insurances")
