from datetime import datetime
from enum import StrEnum

from sqlalchemy import Integer, ForeignKey, String
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel


class ContextualPromptType(StrEnum):
    todoPrompt = "todo"
    mainChatPrompt = "mainChatPrompt"


class ContextualPrompt(BaseModel):
    __tablename__ = "contextual_prompts"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[ContextualPromptType] = mapped_column(String, nullable=False, index=True)
    prompt: Mapped[str] = mapped_column(String, nullable=False)
