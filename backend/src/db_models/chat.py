from enum import StrEnum
from typing import Optional, Any

from sqlalchemy import String, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class ChatStatus(StrEnum):
    ACTIVE = "active"
    CLOSED = "closed"


class Chat(BaseModel):
    __tablename__ = "chats"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str]
    status: Mapped[ChatStatus] = mapped_column(String, default=ChatStatus.ACTIVE)
    category: Mapped[str | None]
    additionalData: Mapped[dict[str, Any] | None]
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="chats")
    propertyId: Mapped[int | None] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="chats")
    messages: Mapped[list["Message"]] = relationship(
        back_populates="chat", cascade="all, delete-orphan", order_by="Message.timestamp"
    )
    projects: Mapped[list["Project"]] = relationship(back_populates="chat")
    jobs: Mapped[list["Job"]] = relationship(back_populates="chat")

    was_processed_as_abandoned: Mapped[bool] = mapped_column(default=False, index=True, nullable=True)
