from datetime import datetime

from sqlalchemy import Integer, String, Boolean, false, DateTime, UniqueConstraint
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel
from .property import Property
from .relationships import users_types, users_addresses, UserPropertyRelationType, UsersProperties


class User(BaseModel):
    __tablename__ = "users"
    __table_args__ = (UniqueConstraint("email", "deletedAt", name="uq_email_deletedAt"),)

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    clerkId: Mapped[str | None] = mapped_column(String, unique=True, index=True, nullable=True)
    hubspotId: Mapped[str | None] = mapped_column(String, unique=False, index=True, nullable=True)
    hubspotSyncAt: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
    deletedAt: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
    # contact details:
    email: Mapped[str | None] = mapped_column(String, index=True, nullable=True)
    isEmailVerified: Mapped[bool] = mapped_column(Boolean, server_default=false())
    isWelcomeEmailSent: Mapped[bool] = mapped_column(Boolean, server_default=false())
    firstName: Mapped[str | None]
    lastName: Mapped[str | None]
    phoneNumber: Mapped[str | None]
    isPhoneNumberVerified: Mapped[bool | None]
    # personalization:
    mainUsage: Mapped[UserPropertyRelationType | None]
    diyProficiency: Mapped[str | None]
    projectManagementAbilityAndWillingness: Mapped[str | None]
    propertyImprovementInterest: Mapped[str | None]
    preferenceForDIYOrProfessionals: Mapped[str | None]
    aestheticPreferences: Mapped[str | None]
    householdPeople: Mapped[str | None]
    householdChildren: Mapped[str | None]
    householdPets: Mapped[str | None]
    vehicle: Mapped[str | None]
    ecoConsciousness: Mapped[str | None]
    budgetConsciousness: Mapped[str | None]
    brandAffinity: Mapped[str | None]
    occupation: Mapped[str | None]
    dailyWorkRoutineGeneralAvailability: Mapped[str | None]
    # relationships:
    types: Mapped[list["UserType"]] = relationship(
        "UserType", secondary=users_types, back_populates="users", lazy="selectin"
    )
    addresses: Mapped[list["Address"]] = relationship("Address", secondary=users_addresses, back_populates="users")
    usersProperties: Mapped[list["UsersProperties"]] = relationship(
        "UsersProperties", back_populates="user", cascade="all,delete", lazy="selectin"
    )
    properties: Mapped[list["Property"]] = relationship(secondary="users_properties", lazy="selectin")
    documents: Mapped[list["Document"]] = relationship(back_populates="user")
    messages: Mapped[list["Message"]] = relationship(back_populates="user")
    chats: Mapped[list["Chat"]] = relationship(back_populates="user")
    projects: Mapped[list["Project"]] = relationship(back_populates="user")
    jobs: Mapped[list["Job"]] = relationship(back_populates="user")
    bills: Mapped[list["Bill"]] = relationship(back_populates="user")
    insurances: Mapped[list["Insurance"]] = relationship(back_populates="user")
    legals: Mapped[list["Legal"]] = relationship(back_populates="user")
    buildingsManaged: Mapped[list["Building"]] = relationship(back_populates="managingAgent")
    todos: Mapped[list["Todo"]] = relationship(back_populates="user")
    notifications: Mapped[list["Notification"]] = relationship(back_populates="user")

    @hybrid_property
    def canUserAcceptJobs(self) -> bool:
        if not self.phoneNumber:
            return False
        if not self.isPhoneNumberVerified:
            return False
        if not self.usersProperties or len(self.usersProperties) == 0:
            return False
        return True

    @hybrid_property
    def isGuest(self) -> bool:
        return self.clerkId is None
