from datetime import datetime
from typing import Optional

from sqlalchemy import DateTime, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql import func

from .base import BaseModel


class Project(BaseModel):
    __tablename__ = "projects"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    headline: Mapped[str]
    subTitle: Mapped[str]
    details: Mapped[str]
    urgency: Mapped[str]
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), nullable=False)
    chatId: Mapped[int] = mapped_column(ForeignKey("chats.id"))
    chat: Mapped["Chat"] = relationship(back_populates="projects")
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="projects")
    propertyId: Mapped[int | None] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="projects")
    jobs: Mapped[list["Job"]] = relationship(back_populates="project")
