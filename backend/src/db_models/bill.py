from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import Integer, ForeignKey
from sqlalchemy.orm import relationship, mapped_column, Mapped

from .base import BaseModel


class BillType(Enum):
    energyGas = "energyGas"
    energyElectricity = "energyElectricity"
    energyGasAndElectricity = "energyGasAndElectricity"
    water = "water"
    securitySystem = "securitySystem"
    councilTax = "councilTax"
    mortgage = "mortgage"
    rent = "rent"
    serviceCharge = "serviceCharge"
    groundRent = "groundRent"
    tv = "tv"
    internet = "internet"
    mobile = "mobile"
    other = "other"


class PaymentMethodType(Enum):
    directDebit = "directDebit"
    debitCreditCard = "debitCreditCard"
    standingOrder = "standingOrder"
    prepayment = "prepayment"
    other = "other"


class Bill(BaseModel):
    __tablename__ = "bills"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    type: Mapped[BillType]
    provider: Mapped[str | None]
    accountNumber: Mapped[str | None]
    contractDates: Mapped[str | None]
    tariff: Mapped[str | None]
    estimatedMonthlyUsage: Mapped[str | None]
    estimatedMonthlyCost: Mapped[Decimal | None]
    paymentMethod: Mapped[PaymentMethodType | None]
    otherDetails: Mapped[str | None]  # subscription description, broadband speed, limits, tv channels etc.
    isRecentBill: Mapped[bool]
    # relationships:
    userId: Mapped[int] = mapped_column(ForeignKey("users.id"))
    user: Mapped["User"] = relationship(back_populates="bills")
    propertyId: Mapped[int | None] = mapped_column(ForeignKey("properties.id"))
    property: Mapped[Optional["Property"]] = relationship(back_populates="bills")
    documentId: Mapped[int | None] = mapped_column(ForeignKey("documents.id"))
    document: Mapped["Document"] = relationship(back_populates="bills")
