from sqlalchemy import Integer
from sqlalchemy.orm import mapped_column, Mapped, relationship

from .base import BaseModel


class ServiceProvider(BaseModel):
    __tablename__ = "service_providers"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str]
    onboardingStage: Mapped[str]
    haRelationship: Mapped[str]
    businessAddress: Mapped[str]
    postCode: Mapped[str]
    companyType: Mapped[str]
    insuranceDetails: Mapped[str]
    qualifications: Mapped[list[str]]
    idCheck: Mapped[str]

    quotes: Mapped[list["Quote"]] = relationship(back_populates="serviceProvider")
    jobs: Mapped[list["Job"]] = relationship(back_populates="serviceProvider")
    professionals: Mapped[list["Professional"]] = relationship(back_populates="serviceProvider")

    # the fields below should be calculated dynamically
    # noOfQuotesForHa: Mapped[int]
    # noOfJobsCompletedWithHa: Mapped[int]
    # haUserFeedbackAverageScore: Mapped[float]
    # haRating: Mapped[float]
    # timeToQuote: Mapped[float]
    # numberOfComplaintsAndDisputes: Mapped[int]
    email: Mapped[str | None]
    phoneNumber: Mapped[str | None]
    nextSteps: Mapped[str | None]
    source: Mapped[str | None]
    mainServiceCategories: Mapped[list[str] | None]
    serviceSubCategories: Mapped[list[str] | None]
    urgentOohService: Mapped[str | None]
    postcodesServiced: Mapped[list[str] | None]
    areasServiced: Mapped[list[str] | None]
    companyName: Mapped[str | None]
    teamSize: Mapped[int | None]
    website: Mapped[str | None]
    availability: Mapped[str | None]
    phoneNumber: Mapped[str | None]
    email: Mapped[str | None]
    mainContactName: Mapped[str | None]
    mainContactEmail: Mapped[str | None]
    mainContactPhoneNumber: Mapped[str | None]
    preferredCommunication: Mapped[str | None]
    googleRating: Mapped[float | None]
    tradingStandardsRating: Mapped[float | None]
    trustpilotScore: Mapped[float | None]
    whichTrustedRating: Mapped[float | None]
    barkRating: Mapped[float | None]
    trustatraderRating: Mapped[float | None]
    mybuilderRating: Mapped[float | None]
    trustmarkRating: Mapped[float | None]
    checkatradeRating: Mapped[float | None]
    checkatradeProfile: Mapped[str | None]
    qualifications: Mapped[list[str] | None]
    workGuarantee: Mapped[str | None]
    paymentTerms: Mapped[str | None]
    fees: Mapped[str | None]
    notes: Mapped[str | None]
    plumbing: Mapped[str | None]
    plumbingFees: Mapped[str | None]
    handyperson: Mapped[str | None]
    handypersonFees: Mapped[str | None]
    gasAndHeatingEngineer: Mapped[str | None]
    gasAndHeatingFees: Mapped[str | None]
    electrician: Mapped[str | None]
    electricianFees: Mapped[str | None]
    gardeningAndLandscaping: Mapped[str | None]
    gardeningFees: Mapped[str | None]
    cleaner: Mapped[str | None]
    cleaningFees: Mapped[str | None]
    propertySurvey: Mapped[str | None]
    surveyFees: Mapped[str | None]
