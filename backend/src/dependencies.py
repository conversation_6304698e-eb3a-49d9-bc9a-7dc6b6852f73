from os import environ

import aioboto3
from fastapi import Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from src.agents.dataextractors.DataExtractorService import DataExtractorService
from src.agents.todos_agent import TodosAgent
from src.agents.ContextualPromptsAgent import ContextualPromptAgent
from src.ai_dao.QdrantDAO import QdrantDAO
from src.ai_dependencies import (
    get_diagnostic_agent_streaming,
    get_data_extractor_service,
    get_todos_agent,
    get_contextual_prompt_agent,
)
from src.singleton import singleton
from src.database import sessionmanager
from src.integrations.aiengine import AIEngineDirect, AbstractAIEngineAPI
from src.integrations.clerk import <PERSON><PERSON><PERSON>
from src.integrations.crystalroof import CrystalRoofAPI
from src.integrations.hubspot import HubSpotAPI
from src.integrations.idealpostcodes import IdealPostcodesAPI
from src.integrations.landregistry import LandRegistryAPI
from src.integrations.sendgrid import SendgridService
from src.services.appliances import ApplianceService
from src.services.chats import ChatService
from src.services.contextual_prompts import ContextualPromptService
from src.services.coordinates import CoordinateTransformer
from src.services.documents import DocumentService
from src.services.entity_links import EntityLinksService
from src.services.hubspot_sync import HubSpotSyncService
from src.services.jobs import JobService
from src.services.messages import MessageService
from src.services.notifications import NotificationService
from src.services.properties import PropertyService
from src.services.source_links import SourceLinksService
from src.services.todos import TodoService
from src.services.todos_for_abandoned_chats import TodosForAbandonedChatsService
from src.services.users import UserService


async def get_db_session():
    async with sessionmanager.session() as session:
        yield session


crystal_roof_api = CrystalRoofAPI(environ["CRYSTAL_ROOF_API_KEY"])


async def get_crystalroof_api():
    return crystal_roof_api


land_registry_api = LandRegistryAPI()


async def get_land_registry_api():
    return land_registry_api


idealpostcodes_api = IdealPostcodesAPI(environ["IDEALPOSTCODES_API_KEY"])


async def get_idealpostcodes_api():
    return idealpostcodes_api


clerk_api = ClerkAPI(
    api_key=environ["CLERK_API_KEY"],
    public_key=environ["CLERK_PEM_PUBLIC_KEY"],
    permitted_origins=(
        environ["CLERK_PERMITTED_ORIGINS"].split(",") if environ.get("CLERK_PERMITTED_ORIGINS") else None
    ),
    permitted_origin_regex=environ.get("CLERK_PERMITTED_ORIGIN_REGEX", None),
)


async def get_clerk_api():
    return clerk_api


sendgrid_service = SendgridService(
    environ["SENDGRID_API_KEY"],
    environ["SENDGRID_DEFAULT_FROM_EMAIL"],
    environ["STAGE"],
)


def get_sendgrid_service():
    return sendgrid_service


hubspot_api = HubSpotAPI(environ["HUBSPOT_API_KEY"])


def get_hubspot_api():
    return hubspot_api


def get_notification_service(
    db: AsyncSession = Depends(get_db_session),
) -> NotificationService:
    return NotificationService(db=db)


def get_source_links_service(
    db: AsyncSession = Depends(get_db_session),
    notification_service: NotificationService = Depends(get_notification_service),
) -> SourceLinksService:
    return SourceLinksService(db=db, notification_service=notification_service)


@singleton()
def get_qdrant_dao():
    return QdrantDAO()


def get_ai_engine_api(
    background_tasks: BackgroundTasks,
    diagnostic_agent_streaming=Depends(get_diagnostic_agent_streaming),
    data_extractor_service: DataExtractorService = Depends(get_data_extractor_service),
    todos_agent: TodosAgent = Depends(get_todos_agent),
    qdrant_dao: QdrantDAO = Depends(get_qdrant_dao),
) -> AbstractAIEngineAPI:
    return AIEngineDirect(
        diagnostic_agent_streaming=diagnostic_agent_streaming,
        background_tasks=background_tasks,
        data_extractor_service=data_extractor_service,
        todos_agent=todos_agent,
        qdrant_dao=qdrant_dao,
    )


def get_property_service(
    db: AsyncSession = Depends(get_db_session),
    my_land_registry_api=Depends(get_land_registry_api),
    my_idealpostcodes_api=Depends(get_idealpostcodes_api),
    source_links_service: SourceLinksService = Depends(get_source_links_service),
) -> PropertyService:
    return PropertyService(
        db=db,
        coordinate_transformer=CoordinateTransformer(),
        land_registry_api=my_land_registry_api,
        idealpostcodes_api=my_idealpostcodes_api,
        source_link_service=source_links_service,
    )


def get_message_service(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_ai_engine: AbstractAIEngineAPI = Depends(get_ai_engine_api),
) -> MessageService:
    return MessageService(db=db, property_service=my_property_service, ai_engine_api=my_ai_engine)


aioboto3_session = aioboto3.Session()


def get_aioboto3_session():
    return aioboto3_session


def get_document_service_without_ai(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_aioboto3_session: aioboto3.Session = Depends(get_aioboto3_session),
    my_notification_service: NotificationService = Depends(get_notification_service),
) -> DocumentService:
    return DocumentService(
        db=db,
        property_service=my_property_service,
        ai_engine=None,
        aioboto3_session=my_aioboto3_session,
        notification_service=my_notification_service,
    )


def get_document_service_with_ai(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_ai_engine: AbstractAIEngineAPI = Depends(get_ai_engine_api),
    my_aioboto3_session: aioboto3.Session = Depends(get_aioboto3_session),
    my_notification_service: NotificationService = Depends(get_notification_service),
) -> DocumentService:
    return DocumentService(
        db=db,
        property_service=my_property_service,
        ai_engine=my_ai_engine,
        aioboto3_session=my_aioboto3_session,
        notification_service=my_notification_service,
    )


def get_hubspot_sync_service(
    db: AsyncSession = Depends(get_db_session),
    document_service: DocumentService = Depends(get_document_service_without_ai),
) -> HubSpotSyncService:
    return HubSpotSyncService(db=db, hubspot_api=hubspot_api, document_service=document_service)


def get_user_service(
    db: AsyncSession = Depends(get_db_session),
    my_clerk_api: ClerkAPI = Depends(get_clerk_api),
    my_sendgrid_service=Depends(get_sendgrid_service),
    hubspot_sync_service: HubSpotSyncService = Depends(get_hubspot_sync_service),
    property_service: PropertyService = Depends(get_property_service),
) -> UserService:
    return UserService(
        db=db,
        clerk_api=my_clerk_api,
        sendgrid_service=my_sendgrid_service,
        hubspot_sync_service=hubspot_sync_service,
        property_service=property_service,
    )


def get_job_service(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_document_service: DocumentService = Depends(get_document_service_without_ai),
    my_sendgrid_service=Depends(get_sendgrid_service),
    hubspot_sync_service: HubSpotSyncService = Depends(get_hubspot_sync_service),
) -> JobService:
    return JobService(
        db=db,
        property_service=my_property_service,
        document_service=my_document_service,
        sendgrid_service=my_sendgrid_service,
        hubspot_sync_service=hubspot_sync_service,
    )


def get_appliance_service(
    db: AsyncSession = Depends(get_db_session),
    my_property_service: PropertyService = Depends(get_property_service),
    my_source_link_service: SourceLinksService = Depends(get_source_links_service),
) -> ApplianceService:
    return ApplianceService(
        db=db,
        property_service=my_property_service,
        source_link_service=my_source_link_service,
    )


def get_entity_link_service(
    db: AsyncSession = Depends(get_db_session),
) -> EntityLinksService:
    return EntityLinksService(db=db)


def get_todo_service(
    db: AsyncSession = Depends(get_db_session),
    my_source_link_service: SourceLinksService = Depends(get_source_links_service),
    ai_engine_service: AbstractAIEngineAPI = Depends(get_ai_engine_api),
    entity_link_service: EntityLinksService = Depends(get_entity_link_service),
    contextual_prompt_agent: ContextualPromptAgent = Depends(get_contextual_prompt_agent),
) -> TodoService:
    return TodoService(
        db=db,
        source_link_service=my_source_link_service,
        ai_engine_service=ai_engine_service,
        entity_link_service=entity_link_service,
        expiration_after_minutes=TODO_EXPIRATION_AFTER_MINUTES,
        contextual_prompt_agent=contextual_prompt_agent,
    )


TODO_ABANDONED_CHAT_AFTER_MINUTES = int(environ.get("TODO_ABANDONED_CHAT_AFTER_MINUTES", 24 * 60))
TODO_PROCESSING_BATCH_SIZE = int(environ.get("TODO_PROCESSING_BATCH_SIZE", 10))
TODO_EXPIRATION_AFTER_MINUTES = int(environ.get("TODO_EXPIRATION_AFTER_MINUTES", 30 * 24 * 60))


def get_chat_service(
    db: AsyncSession = Depends(get_db_session),
    aioboto3_session: aioboto3.Session = Depends(get_aioboto3_session),
) -> ChatService:
    return ChatService(
        db=db, aioboto3_session=aioboto3_session, abandoned_chat_after_minutes=TODO_ABANDONED_CHAT_AFTER_MINUTES
    )


def get_todos_for_abandoned_chats_service(
    db: AsyncSession = Depends(get_db_session),
    chat_service: ChatService = Depends(get_chat_service),
    todo_service: TodoService = Depends(get_todo_service),
) -> TodosForAbandonedChatsService:
    return TodosForAbandonedChatsService(
        db=db, chat_service=chat_service, todo_service=todo_service, limit=TODO_PROCESSING_BATCH_SIZE
    )


def get_contextual_prompt_service(
    db: AsyncSession = Depends(get_db_session),
    entity_link_service: EntityLinksService = Depends(get_entity_link_service),
) -> ContextualPromptService:
    return ContextualPromptService(
        db=db,
        entity_link_service=entity_link_service,
    )
