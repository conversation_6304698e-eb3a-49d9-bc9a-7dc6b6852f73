import json
import logging
import uuid

from fastapi import APIRouter, Depends
from sentry_sdk import capture_exception as sentry_capture_exception

from src import settings
from src.dependencies import (
    get_aioboto3_session,
    get_document_service_without_ai,
    get_hubspot_sync_service,
    get_todo_service,
    get_todos_for_abandoned_chats_service,
)
from src.services.auth import validate_admin_token
from src.services.documents import DocumentService
from src.services.hubspot_sync import HubSpotSyncService
from src.services.todos import TodoService
from src.services.todos_for_abandoned_chats import TodosForAbandonedChatsService

logger = logging.getLogger("uvicorn")

router = APIRouter(
    prefix="/admin",
    tags=["admin"],
)


async def safe_execute(operation_name: str, operation_func, *args, **kwargs):
    try:
        logger.info(operation_name)
        await operation_func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Error during {operation_name.lower()}: {e}")
        sentry_capture_exception(e)


@router.post("/pulse", status_code=200, dependencies=[Depends(validate_admin_token)])
async def admin_pulse(
    document_service: DocumentService = Depends(get_document_service_without_ai),
    hubspot_sync_service: HubSpotSyncService = Depends(get_hubspot_sync_service),
    todo_service: TodoService = Depends(get_todo_service),
    todos_for_abandoned_chats_service: TodosForAbandonedChatsService = Depends(get_todos_for_abandoned_chats_service),
):
    logger.info("Received admin pulse")

    await safe_execute("Timeout documents", document_service.timeout_documents)
    await safe_execute("Sync users", hubspot_sync_service.sync_users)
    await safe_execute("Sync jobs", hubspot_sync_service.sync_jobs)
    await safe_execute(
        "Create todos for abandoned chats",
        lambda: todos_for_abandoned_chats_service.create_todos_for_abandoned_chats(),
    )
    await safe_execute("Expire todos", lambda: todo_service.expire_todos())


@router.post("/sqs-enqueue-test", status_code=200, dependencies=[Depends(validate_admin_token)])
async def admin_pulse_tmep(aioboto3_session=Depends(get_aioboto3_session)):
    logger.info("Received sqs-enqueue-test")
    assert settings.ABANDONED_CHATS_PROCESSING_QUEUE_URL, "ABANDONED_CHATS_PROCESSING_QUEUE_URL is not set in settings."
    async with aioboto3_session.client("sqs") as sqs:
        chat_id = str(uuid.uuid4())
        message = {"chat_id": chat_id}
        await sqs.send_message(
            QueueUrl=settings.ABANDONED_CHATS_PROCESSING_QUEUE_URL,
            MessageBody=json.dumps(message),
            MessageDeduplicationId=str(f"abandoned-chat-{chat_id}"),
            MessageGroupId="default",
        )
