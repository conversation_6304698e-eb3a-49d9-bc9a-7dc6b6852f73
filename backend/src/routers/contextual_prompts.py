from fastapi import APIRouter, Depends

from src.dependencies import get_contextual_prompt_service
from src.schemas import ContextualPromptInfo
from src.services.contextual_prompts import ContextualPromptService
from typing import Annotated
from src.services.auth import get_current_user
from src.db_models.user import User

router = APIRouter(
    prefix="/contextual-prompts",
    tags=["contextual-prompts"],
)


@router.get("/todo/{todo_id}", response_model=list[ContextualPromptInfo])
async def get_contextual_prompts_for_todo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_id: int,
    contextual_prompt_service: ContextualPromptService = Depends(get_contextual_prompt_service),
):
    """Get all contextual prompts linked to a specific todo."""
    prompts = await contextual_prompt_service.get_contextual_prompts_for_todo(todo_id, current_user.id)
    return prompts
