from typing import Annotated, List

from fastapi import APIRouter, Depends, HTTPException

from src.db_models.entity_link import LinkedEntityType
from src.db_models.user import User
from src.dependencies import get_entity_link_service
from src.schemas import (
    LinkedEntity,
    LinkedEntityInfo,
)
from src.services.auth import get_current_user, get_current_user_or_guest
from src.services.entity_links import EntityLinksService

router = APIRouter(
    prefix="/entity-links",
    tags=["entity-links"],
)


@router.get("/{entity_type}/{entity_id}", response_model=List[LinkedEntityInfo])
async def get_entity_links_endpoint(
    entity_type: LinkedEntityType,
    entity_id: int,
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    entity_link_service: EntityLinksService = Depends(get_entity_link_service),
):
    return await entity_link_service.get_links(LinkedEntity(entityType=entity_type, id=entity_id), current_user.id)


@router.delete("/{entity_type_a}/{entity_id_a}/{entity_type_b}/{entity_id_b}", status_code=204)
async def delete_link(
    entity_type_a: LinkedEntityType,
    entity_id_a: int,
    entity_type_b: LinkedEntityType,
    entity_id_b: int,
    current_user: Annotated[User, Depends(get_current_user)],
    entity_link_service: EntityLinksService = Depends(get_entity_link_service),
):
    try:
        await entity_link_service.unlink(
            a=LinkedEntity(entityType=entity_type_a, id=entity_id_a),
            b=LinkedEntity(entityType=entity_type_b, id=entity_id_b),
            user_id=current_user.id,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Link deleted successfully"}


@router.delete("/{entity_type}/{entity_id}", status_code=204)
async def delete_links(
    entity_type: LinkedEntityType,
    entity_id: int,
    current_user: Annotated[User, Depends(get_current_user)],
    entity_link_service: EntityLinksService = Depends(get_entity_link_service),
):
    try:
        await entity_link_service.unlink_all(LinkedEntity(entityType=entity_type, id=entity_id), current_user.id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Links deleted successfully"}


@router.post(
    "/{entity_type_a}/{entity_id_a}/{entity_type_b}/{entity_id_b}",
    status_code=201,
)
async def create_link(
    entity_type_a: LinkedEntityType,
    entity_id_a: int,
    entity_type_b: LinkedEntityType,
    entity_id_b: int,
    current_user: Annotated[User, Depends(get_current_user)],
    entity_link_service: EntityLinksService = Depends(get_entity_link_service),
):
    try:
        await entity_link_service.link(
            a=LinkedEntity(entityType=entity_type_a, id=entity_id_a),
            b=LinkedEntity(entityType=entity_type_b, id=entity_id_b),
            user_id=current_user.id,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Link created successfully"}
