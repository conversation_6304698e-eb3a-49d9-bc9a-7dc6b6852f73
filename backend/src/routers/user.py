from typing import Annotated

from fastapi import Depends, APIRouter, HTTPException

from src.db_models.user import User
from src.dependencies import get_user_service
from src.schemas import (
    UserDetails,
    B2BDemoRequest,
    CreateGuestUserResponse,
    ConvertGuestUserToRegisteredRequest,
    ConvertGuestUserToRegisteredResponse,
)
from src.services.auth import get_current_user, get_current_user_or_guest, get_guest_user
from src.services.auth import issue_guest_user_token
from src.services.users import UserService, EmailAlreadyExistsError

router = APIRouter(
    prefix="/user",
    tags=["user"],
)


@router.get("/", response_model=UserDetails)
async def get_current_user_details_endpoint(
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
):
    return UserDetails.model_validate(current_user)


@router.post("/request-b2b-demo/")
async def request_b2b_demo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    b2b_demo_request: B2BDemoRequest,
    user_service: UserService = Depends(get_user_service),
):
    await user_service.request_b2b_demo(current_user, b2b_demo_request)


@router.post("/guest/", response_model=CreateGuestUserResponse)
async def create_guest_user_endpoint(
    user_service: UserService = Depends(get_user_service),
):
    guest_user = await user_service.create_guest_user()
    token = issue_guest_user_token(guest_user)
    return CreateGuestUserResponse(
        userId=guest_user.id,
        token=token,
    )


@router.post("/guest/convert/", response_model=ConvertGuestUserToRegisteredResponse)
async def convert_guest_user_to_authenticated(
    guest_user: Annotated[User, Depends(get_guest_user)],
    user_details: ConvertGuestUserToRegisteredRequest,
    user_service: UserService = Depends(get_user_service),
):
    if guest_user.clerkId:
        raise HTTPException(
            status_code=400,
            detail="Guest user already has a Clerk ID. Cannot convert again.",
        )
    try:
        user = await user_service.convert_guest_user_to_authenticated(guest_user, user_details)
    except EmailAlreadyExistsError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e),
        )
    sign_in_token = await user_service.get_clerk_sign_in_token_for_user(user)
    return ConvertGuestUserToRegisteredResponse(userId=user.id, clerkId=user.clerkId, signInToken=sign_in_token)
