from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.dependencies import get_db_session, get_notification_service
from src.schemas import NotificationInfo
from src.services.auth import get_current_user
from src.services.notifications import NotificationService, NotificationDoesNotExist

router = APIRouter(
    prefix="/notifications",
    tags=["notifications"],
)


@router.get("/", response_model=Page[NotificationInfo])
async def get_notifications_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    notification_service: NotificationService = Depends(get_notification_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=notification_service.get_notifications_for_user_query(current_user), conn=db_session)


@router.get("/{context}", response_model=Page[NotificationInfo])
async def get_notifications_by_context_endpoint(
    context: str,
    current_user: Annotated[User, Depends(get_current_user)],
    notification_service: NotificationService = Depends(get_notification_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(
        query=notification_service.get_notifications_for_user_by_context_query(current_user, context), conn=db_session
    )


@router.delete("/", status_code=204)
async def delete_all_notifications_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    notification_service: NotificationService = Depends(get_notification_service),
):
    await notification_service.delete_all_notifications(current_user)


@router.delete("/{context}", status_code=204)
async def delete_notifications_by_context_endpoint(
    context: str,
    current_user: Annotated[User, Depends(get_current_user)],
    notification_service: NotificationService = Depends(get_notification_service),
):
    await notification_service.delete_notifications_by_context(context, current_user)


@router.delete("/-/{notification_id}", status_code=204)
async def delete_notification_by_context_and_id_endpoint(
    notification_id: int,
    current_user: Annotated[User, Depends(get_current_user)],
    notification_service: NotificationService = Depends(get_notification_service),
):
    try:
        await notification_service.delete_notification_by_id(notification_id, current_user)
    except NotificationDoesNotExist as e:
        raise HTTPException(status_code=404, detail=str(e))
