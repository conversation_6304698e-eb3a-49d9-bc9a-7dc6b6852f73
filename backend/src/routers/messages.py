import logging
from typing import Annotated

from fastapi import Depends, APIRouter

from src.db_models.user import User
from src.dependencies import get_message_service
from src.schemas import SendMessageRequest, SendMessageResponse
from src.services.auth import get_current_user_or_guest
from starlette.responses import StreamingResponse
from src.services.messages import MessageService

logger = logging.getLogger("uvicorn")

router = APIRouter(
    prefix="/messages",
    tags=["messages"],
)


@router.post("/", response_model=SendMessageResponse)
async def send_user_message(
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    send_message_request: SendMessageRequest,
    message_service=Depends(get_message_service),
):
    logger.info(f"Received message from user {current_user.id}.")
    response = await message_service.send_user_message_and_get_system_response(send_message_request, current_user)
    return response


@router.post("/stream")
async def send_user_message_stream(
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    send_message_request: SendMessageRequest,
    message_service: MessageService = Depends(get_message_service),
):
    logger.info(f"Received streaming message request from user {current_user.id}.")

    async def response_generator():
        async for chunk in message_service.send_user_message_and_get_system_response_stream(
            send_message_request, current_user
        ):
            yield chunk

    return StreamingResponse(content=response_generator(), media_type="text/event-stream")
