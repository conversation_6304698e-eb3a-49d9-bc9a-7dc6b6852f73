import logging
from typing import Annotated

from fastapi import APIRouter, UploadFile, Depends, HTTPException
from fastapi.responses import StreamingResponse
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.document import DocumentStatusType
from src.db_models.user import User
from src.dependencies import get_document_service_without_ai, get_db_session, get_document_service_with_ai
from src.schemas import DocumentInfo
from src.services.auth import get_current_user_or_guest
from src.services.documents import (
    DocumentService,
    DocumentDoesNotExist,
    UnsupportedDocumentType,
    DocumentSizeLimitExceeded,
    UserDocumentSizeLimitExceeded,
)

logger = logging.getLogger("uvicorn")

router = APIRouter(
    prefix="/documents",
    tags=["documents"],
)


@router.post("/", response_model=DocumentInfo)
async def upload_document(
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    file: UploadFile,
    uploadContext: str | None = None,
    document_service: DocumentService = Depends(get_document_service_with_ai),
):
    logger.info(f"Received file {file}.")
    try:
        saved_document = await document_service.save_document(file, current_user, uploadContext)
        return saved_document
    except UnsupportedDocumentType:
        raise HTTPException(status_code=400, detail="Unsupported document type")
    except DocumentSizeLimitExceeded:
        raise HTTPException(status_code=400, detail="Document size limit exceeded")
    except UserDocumentSizeLimitExceeded:
        raise HTTPException(status_code=400, detail="User document size limit exceeded")
    except Exception as e:
        logger.error(f"An error occurred while saving document: {e}")
        raise HTTPException(status_code=500)


@router.get("/", response_model=Page[DocumentInfo])
async def list_documents_for_user(
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    uploadContext: str | None = None,
    status: DocumentStatusType | None = None,
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(
        query=DocumentService.list_documents_for_user_query(current_user, uploadContext, status), conn=db_session
    )


@router.get("/{document_id}/", response_class=StreamingResponse)
async def download_document(
    document_id: int,
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    document_service: DocumentService = Depends(get_document_service_without_ai),
):
    try:
        document = await document_service.get_document_for_user(document_id, current_user)
        await document_service.close_db_session()  # close the db session before streaming the response
        return StreamingResponse(
            content=document_service.get_document_content_from_s3(document.s3Bucket, document.s3Key),
            media_type=document.browserMimeType,
        )
    except DocumentDoesNotExist:
        raise HTTPException(status_code=404, detail="Document does not exist")
    except Exception as e:
        logger.error(f"An error occurred while downloading document {document_id}: {e}")
        raise HTTPException(status_code=500)


@router.get("/{document_id}/details", response_model=DocumentInfo)
async def get_document_details(
    document_id: int,
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    document_service: DocumentService = Depends(get_document_service_without_ai),
):
    return await document_service.get_document_for_user(document_id, current_user)


@router.delete("/{document_id}/", status_code=204)
async def delete_document(
    document_id: int,
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    document_service: DocumentService = Depends(get_document_service_without_ai),
):
    try:
        await document_service.delete_document(document_id, current_user)
    except DocumentDoesNotExist:
        raise HTTPException(status_code=404, detail="Document does not exist")
    except Exception as e:
        logger.error(f"An error occurred while deleting document {document_id}: {e}")
        raise HTTPException(status_code=500)

    # TODO notify ai engine about deleted document
