from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession

from src.db_models.user import User
from src.dependencies import get_db_session, get_todo_service
from src.schemas import TodoShortInfo, TodoCreate, TodoUpdate, TodoFullInfo
from src.services.auth import get_current_user
from src.services.todos import TodoService, TodoDoesNotExist

router = APIRouter(
    prefix="/todos",
    tags=["todos"],
)


@router.get("/", response_model=Page[TodoShortInfo])
async def get_all_todos_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_service: TodoService = Depends(get_todo_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=todo_service.get_todos_for_user_query(current_user), conn=db_session)


@router.get("/accepted", response_model=Page[TodoShortInfo])
async def get_user_accepted_todos(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_service: TodoService = Depends(get_todo_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=todo_service.get_accepted_todos_query(current_user), conn=db_session)


@router.get("/suggested", response_model=Page[TodoShortInfo])
async def get_suggested_todos(
    current_user: Annotated[User, Depends(get_current_user)],
    limit: int | None = Query(gt=0, description="Number of todos to return", default=None),
    todo_service: TodoService = Depends(get_todo_service),
    db_session: AsyncSession = Depends(get_db_session),
):
    return await paginate(query=todo_service.get_suggested_todos_query(current_user, limit), conn=db_session)


@router.get("/{todo_id}", response_model=TodoFullInfo)
async def get_todo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_id: int,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        return await todo_service.get_todo(todo_id, current_user)
    except TodoDoesNotExist as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/", response_model=TodoShortInfo)
async def create_todo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_create: TodoCreate,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        return await todo_service.create_todo_by_user(todo_create, current_user)
    except TodoDoesNotExist as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{todo_id}", response_model=TodoShortInfo)
async def update_todo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_id: int,
    todo_data: TodoUpdate,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        return await todo_service.update_todo_by_user(todo_id, todo_data, current_user)
    except TodoDoesNotExist as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.delete("/{todo_id}", status_code=204)
async def delete_todo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_id: int,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        await todo_service.delete_todo_by_user(todo_id, current_user)
    except TodoDoesNotExist as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/{todo_id}/accept", status_code=204)
async def accept_todo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_id: int,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        await todo_service.accept_todo_by_user(todo_id, current_user)
    except TodoDoesNotExist as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/{todo_id}/reject", status_code=204)
async def reject_todo_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    todo_id: int,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        await todo_service.reject_todo_by_user(todo_id, current_user)
    except TodoDoesNotExist as e:
        raise HTTPException(status_code=404, detail=str(e))
