from typing import Annotated

from fastapi import APIRouter, status, Depends, HTTPException
from fastapi_pagination import Page

from src.db_models.user import User
from src.dependencies import get_property_service
from src.schemas import PropertyInfo, PropertyCreate, PropertyUpdate
from src.services.auth import get_current_user, get_current_user_or_guest
from src.services.properties import PropertyService, UserAlreadyHasProperty, PropertyDoesNotExist

router = APIRouter(
    prefix="/properties",
    tags=["properties"],
)


@router.post("/", response_model=PropertyInfo, status_code=status.HTTP_201_CREATED)
async def create_property_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    property_create: PropertyCreate,
    property_service: PropertyService = Depends(get_property_service),
):
    try:
        created_property = await property_service.create_property_for_user(current_user, property_create)
    except UserAlreadyHasProperty as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    return created_property


@router.get("/", response_model=Page[PropertyInfo])
async def get_properties_endpoint(
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    property_service: PropertyService = Depends(get_property_service),
):
    items = await property_service.get_properties_for_user(current_user)
    return {"items": items, "total": len(items), "page": 1, "size": len(items) or 1, "pages": 1}


@router.get("/{property_id}", response_model=PropertyInfo)
async def get_property_by_id_endpoint(
    current_user: Annotated[User, Depends(get_current_user_or_guest)],
    property_id: int,
    property_service: PropertyService = Depends(get_property_service),
):
    property = await property_service.get_property_by_id_for_user(property_id, current_user)
    if not property:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Property not found")
    return property


@router.patch("/{property_id}", response_model=PropertyInfo)
async def update_property_by_id_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    property_id: int,
    property_update: PropertyUpdate,
    property_service: PropertyService = Depends(get_property_service),
):
    try:
        updated_property = await property_service.update_property_by_id_for_user(
            property_id, property_update, current_user
        )
    except PropertyDoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Property not found")
    return updated_property


@router.delete("/{property_id}", status_code=204)
async def delete_property_by_id_endpoint(
    current_user: Annotated[User, Depends(get_current_user)],
    property_id: int,
    property_service: PropertyService = Depends(get_property_service),
):
    await property_service.delete_property_by_id_for_user(property_id, current_user)
