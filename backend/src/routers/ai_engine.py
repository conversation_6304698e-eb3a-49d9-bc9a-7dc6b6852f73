from fastapi import APIRouter, Depends, status, HTTPException

from src.dependencies import (
    get_document_service_without_ai,
    get_appliance_service,
    get_todo_service,
    get_property_service,
)
from src.schemas import (
    DocumentAiUpdate,
    ApplianceAiCreate,
    ApplianceAiUpdate,
    TodoAiCreate,
    TodoAiUpdate,
    PropertyInfo,
    PropertyAiUpdate,
)

from src.services.appliances import ApplianceService
from src.services.auth import validate_ai_engine_token
from src.services.documents import DocumentService, DocumentDoesNotExist
from src.services.properties import PropertyService, PropertyDoesNotExist
from src.services.todos import TodoService

router = APIRouter(
    prefix="/ai-engine",
    tags=["ai-engine"],
)


@router.patch("/documents/{document_id}", status_code=200, dependencies=[Depends(validate_ai_engine_token)])
async def update_document(
    document_id: int,
    document_update: DocumentAiUpdate,
    document_service: DocumentService = Depends(get_document_service_without_ai),
):
    try:
        await document_service.update_document_by_ai(document_id, document_update)
    except DocumentDoesNotExist:
        raise HTTPException(status_code=404, detail="Document does not exist")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/appliances", status_code=201, dependencies=[Depends(validate_ai_engine_token)])
async def create_appliance(
    appliance_create: ApplianceAiCreate,
    appliance_service: ApplianceService = Depends(get_appliance_service),
):
    try:
        await appliance_service.create_appliance_by_ai(appliance_create)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Appliance created successfully"}


@router.patch("/appliances/{appliance_id}", status_code=200, dependencies=[Depends(validate_ai_engine_token)])
async def update_appliance(
    appliance_id: int,
    appliance_update: ApplianceAiUpdate,
    appliance_service: ApplianceService = Depends(get_appliance_service),
):
    try:
        await appliance_service.update_appliance_by_ai(appliance_id, appliance_update)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Appliance updated successfully"}


@router.post("/todos", status_code=201, dependencies=[Depends(validate_ai_engine_token)])
async def create_todo(
    todo_create: TodoAiCreate,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        await todo_service.create_todo_by_ai(todo_create)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Todo created successfully"}


@router.patch("/todos/{todo_id}", status_code=200, dependencies=[Depends(validate_ai_engine_token)])
async def update_todo(
    todo_id: int,
    todo_update: TodoAiUpdate,
    todo_service: TodoService = Depends(get_todo_service),
):
    try:
        await todo_service.update_todo_by_ai(todo_id, todo_update)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Todo updated successfully"}


@router.patch(
    "/property_details/{property_id}",
    response_model=PropertyInfo,
    status_code=200,
    dependencies=[Depends(validate_ai_engine_token)],
)
async def update_property_details_by_ai_endpoint(
    property_id: int,
    property_update: PropertyAiUpdate,
    property_service: PropertyService = Depends(get_property_service),
):
    """Update property details by AI engine"""
    try:
        updated_property = await property_service.update_property_by_id_for_ai(property_id, property_update)
        return updated_property
    except PropertyDoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Property not found")
