import logging
import os
from typing import Literal

from fastapi import APIRouter, status, Request, Response, Depends
from pydantic import BaseModel, ValidationError
from svix.webhooks import Webhook, WebhookVerificationError

from src.dependencies import get_user_service
from src.integrations.clerk import (
    <PERSON><PERSON><PERSON>,
    ClerkWebhookUserCreateEvent,
    ClerkWebhookUserUpdateEvent,
    ClerkWebhookUserDeleteEvent,
)
from src.services.users import UserService

logger = logging.getLogger("uvicorn")

router = APIRouter(
    prefix="/webhooks",
    tags=["webhooks"],
)


class ClerkWebhookEvent(BaseModel):
    data: dict
    object: Literal["event"]
    type: str


CLERK_WEBHOOK_SECRET = os.environ["CLERK_WEBHOOK_SECRET"]


@router.post("/clerk", status_code=status.HTTP_204_NO_CONTENT)
async def receive_clerk_event(
    request: Request, response: Response, user_service: UserService = Depends(get_user_service)
):
    headers = request.headers
    payload = await request.body()

    try:
        webhook = Webhook(CLERK_WEBHOOK_SECRET)
        message = webhook.verify(payload, headers)
    except WebhookVerificationError:
        response.status_code = status.HTTP_400_BAD_REQUEST
        logger.info("Clerk event signature verification failed")
        return
    except Exception as e:
        response.status_code = status.HTTP_400_BAD_REQUEST
        logger.error(f"Error verifying clerk event signature: {e}")
        return

    event = ClerkWebhookEvent.model_validate(message)
    logger.info(f"Received {event.type} from clerk.")

    def validate(
        event_model: type[ClerkWebhookUserCreateEvent | ClerkWebhookUserUpdateEvent | ClerkWebhookUserDeleteEvent],
    ) -> ClerkWebhookUserCreateEvent | ClerkWebhookUserUpdateEvent | ClerkWebhookUserDeleteEvent | None:
        try:
            return event_model.model_validate(event.data)
        except ValidationError as e:
            response.status_code = status.HTTP_400_BAD_REQUEST
            logger.warning(f"Received invalid {event.type} event from clerk. Ignoring.")
            logger.warning(str(e))
            return None

    async def upsert(event_model: type[ClerkWebhookUserCreateEvent | ClerkWebhookUserUpdateEvent]):
        clerk_event = validate(event_model)
        if clerk_event is None:
            return None
        clerk_user = ClerkUser.from_webhook(clerk_event)
        try:
            return await user_service.upsert_user_from_clerk_user(clerk_user)
        except RuntimeError as e:
            logger.warning(f"Error processing user: {e}")
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            return None

    match event.type:
        case "user.created":
            logger.info("Creating a new user.")
            user = await upsert(event_model=ClerkWebhookUserCreateEvent)
            if user:
                logger.info(f"User {user.id} created.")
        case "user.updated":
            logger.info("Updating a user.")
            user = await upsert(event_model=ClerkWebhookUserUpdateEvent)
            if user:
                logger.info(f"User {user.id} updated.")
        case "user.deleted":
            logger.info("Deleting a user.")
            clerk_event = validate(ClerkWebhookUserDeleteEvent)
            event_id = clerk_event.id
            await user_service.delete_user_by_clerk_id(event_id)
            logger.info(f"Deleted user (clerk id: {event_id}.")
        case _:
            logger.warning(f"Received unknown event type from clerk: {event.type}. Ignoring.")
