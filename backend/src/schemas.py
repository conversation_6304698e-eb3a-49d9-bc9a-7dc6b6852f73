import abc
from datetime import datetime, date
from typing import Any, Literal, Union

from pydantic import BaseModel, EmailStr, ConfigDict, model_validator, constr, Field

from src.db_models.document import DocumentCategoryType, DocumentStatusType
from src.db_models.entity_link import LinkedEntityType
from src.db_models.notification import NotificationPayload
from src.db_models.property import (
    PropertyType,
    PropertyTenureType,
    PropertySubgroupType,
    PropertyEpcRatingType,
    PropertyConservationStatusType,
    PropertyConditionType,
)
from src.db_models.relationships import UserPropertyRelationType
from src.db_models.source_link import SourceType
from src.db_models.todo import TodoType
from src.integrations.idealpostcodes import FindAddressesHit


class UserTypeCreate(BaseModel):
    type: str


class UserType(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    type: str


class UserCreate(BaseModel):
    firstName: str
    clerkId: str
    email: EmailStr
    user_type_ids: list[int] | None = None


class UserUpdate(BaseModel):
    mainUsage: UserPropertyRelationType


class UserDetails(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    clerkId: str | None
    email: str | None
    firstName: str | None
    lastName: str | None
    phoneNumber: str | None
    mainUsage: UserPropertyRelationType | None
    isPhoneNumberVerified: bool | None
    isEmailVerified: bool
    canUserAcceptJobs: bool


class CreateGuestUserResponse(BaseModel):
    userId: int
    token: str


class BookingCreate(BaseModel):
    time: datetime
    address_id: int


class Booking(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    time: datetime
    address_id: int


class ManualAddressCreate(BaseModel):
    streetLine1: str
    streetLine2: str | None = None
    townOrCity: str
    postcode: constr(to_upper=True)
    country: str = "UK"


class PropertyCreate(BaseModel):
    manualAddress: ManualAddressCreate | None = None
    idealPostcodesAddressId: str = None
    type: PropertyType | None = None
    tenureType: PropertyTenureType | None = None
    userPropertyRelationshipType: UserPropertyRelationType | None = None

    @model_validator(mode="after")
    def check_if_any_address_provided(self) -> "PropertyCreate":
        if not self.manualAddress and not self.idealPostcodesAddressId:
            raise ValueError("Either manualAddress or idealPostcodesAddressId must be provided")
        return self


class Address(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int | None = None  # ai engine does not to provide this
    streetLine1: str
    streetLine2: str | None = None
    townOrCity: str
    postcode: str
    houseAccess: str | None = None
    parkingInstructions: str | None = None
    country: str


class PropertyDetailsData(BaseModel):
    tenureType: PropertyTenureType | None = None
    type: PropertyType | None = None
    subgroupType: PropertySubgroupType | None = None
    numberOfBedrooms: int | None = None
    numberOfBathrooms: int | None = None
    numberOfFloors: int | None = None
    sizeInSqft: int | None = None
    hasBalconyTerrace: bool | None = None
    hasGarden: bool | None = None
    hasSwimmingPool: bool | None = None
    onFloorLevel: int | None = None
    userPropertyRelationshipType: UserPropertyRelationType | None = None
    balconyTerraceDetails: str | None = None
    gardenDetails: str | None = None
    swimmingPoolDetails: str | None = None
    lastSoldPriceInGbp: int | None = None
    condition: PropertyConditionType | None = None
    yearsOfOwnership: int | None = None
    architecturalType: str | None = None
    valuationInGbp: int | None = None
    conservationStatus: PropertyConservationStatusType | None = None
    typeOfLock: str | None = None
    typeOfConstruction: str | None = None
    proportionOfFlatRoof: int | None = None
    epcRating: PropertyEpcRatingType | None = None


class AddressUpdate(BaseModel):
    houseAccess: str | None = None
    parkingInstructions: str | None = None


class PropertyUpdate(PropertyDetailsData):
    manualAddress: ManualAddressCreate | None = None
    idealPostcodesAddressId: str = None
    address: AddressUpdate | None = None


class DocumentInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    originalFileName: str = Field(serialization_alias="fileName")
    sizeInKiloBytes: int
    browserMimeType: str | None
    created_at: datetime = Field(serialization_alias="createdAt")
    uploadContext: str | None = None
    status: DocumentStatusType
    category: DocumentCategoryType | None = None
    label: str | None = None


class PropertyInfo(PropertyDetailsData):
    model_config = ConfigDict(from_attributes=True)

    id: int
    address: Address | None = None
    sourcedDocuments: list[DocumentInfo] = []


class MeanHouseHoldIncomeResponse(BaseModel):
    class MeanHouseHoldIncome(BaseModel):
        totalMeanAnnualIncome: int
        totalMeanAnnualIncomeRank: int

    data: MeanHouseHoldIncome
    dataDate: str
    refreshedDate: str


class AverageHousePriceForPostcodeResponse(BaseModel):
    postcode: str
    avg: int | None


class AverageHousePriceForStreetResponse(BaseModel):
    street: str
    town: str
    postcode: str
    avg: int | None


class AverageHousePriceForFlatForTownResponse(BaseModel):
    town: str
    avg: int | None


class AverageHousePriceChangeOverYearsForTownResponse(BaseModel):
    town: str
    changeInPercent: int | None
    years: int


class MostExpensivePostcodesForTownResponse(BaseModel):
    class AverageHousePriceForPostcode(BaseModel):
        postcode: str
        avg: int
        numberOfTransactions: int

    town: str
    postcodes: list[AverageHousePriceForPostcode]


class FindAddressesResponse(BaseModel):
    hits: list[FindAddressesHit]


class AbstractMessage(BaseModel, abc.ABC):
    content: str
    type: Any
    additionalData: Any


class SendMessageMetadata(BaseModel):
    device: str
    location: str | None = None


class SendMessage(AbstractMessage):
    type: Literal["text"] = "text"
    additionalData: SendMessageMetadata


class ResponseMessageMetadata(BaseModel):
    timestamp: datetime
    category: str | None = None
    confidence: float | None = None
    imageUrls: Any = None
    imageClickableUrls: Any = None
    suggestedActions: Any = None
    jobSummary: Any = None


class ResponseMessage(AbstractMessage):
    type: Literal["text", "diagnostic_report"]
    additionalData: ResponseMessageMetadata


class Attachment(BaseModel):
    documentId: int


class SendMessageRequest(BaseModel):
    chatId: int | None = None
    message: SendMessage
    attachments: list[Attachment] | None = None


class SendMessageResponse(BaseModel):
    chatId: int
    userMessageId: int
    systemMessageId: int
    message: ResponseMessage
    attachments: list[Attachment]


class StreamSendMessageResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    chatId: int
    userMessageId: int
    systemMessageId: int


class ChatInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    status: Literal["active", "closed"]


class Message(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    content: str
    type: str
    senderType: str
    timestamp: datetime
    additionalData: dict[str, Any] | None
    attachments: list[DocumentInfo] | None = Field(alias="documents")


class DocumentAiUpdate(BaseModel):
    status: DocumentStatusType
    aiGeneratedFileName: str | None = None
    category: DocumentCategoryType | None = None
    label: str | None = None
    errorMessage: str | None = None


class JobInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    headline: str
    subTitle: str
    details: str
    urgency: str
    availability: str | None
    status: str
    timestamp: datetime


class B2BDemoRequest(BaseModel):
    companyName: str
    businessEmail: EmailStr
    numberOfPropertiesManaged: str


class ApplianceInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    type: str
    brand: str | None
    model: str | None
    serialNumber: str | None
    warranty: str | None
    dateOfPurchase: date | None
    otherDetails: str | None
    propertyId: int
    invoiceReceiptDocumentId: int | None
    sourcedDocuments: list[DocumentInfo] | None = None


class ApplianceUpdate(BaseModel):
    type: str | None = None
    brand: str | None = None
    model: str | None = None
    serialNumber: str | None = None
    warranty: str | None = None
    dateOfPurchase: date | None = None
    otherDetails: str | None = None
    invoiceReceiptDocumentId: int | None = None


class ApplianceCreate(ApplianceUpdate):
    type: str
    propertyId: int


class ApplianceCreateWithOptionalProperty(ApplianceCreate):
    propertyId: int | None = None
    userId: int | None = None

    @model_validator(mode="after")
    def check_if_any_id_provided(self) -> "ApplianceCreateWithOptionalProperty":
        if not self.propertyId and not self.userId:
            raise ValueError("Either propertyId or userId must be provided")
        if self.propertyId and self.userId:
            raise ValueError("Only one of propertyId or userId should be provided, not both")
        return self


class ApplianceAiCreate(BaseModel):
    appliance: ApplianceCreateWithOptionalProperty
    srcType: SourceType
    srcId: int
    userId: int


class ApplianceAiUpdate(BaseModel):
    appliance: ApplianceUpdate
    srcType: SourceType
    srcId: int


class ChatSource(BaseModel):
    srcType: Literal["chat"] = "chat"
    id: int
    title: str


class DocumentSource(BaseModel):
    srcType: Literal["document"] = "document"
    id: int


class UserSource(BaseModel):
    srcType: Literal["userInput"] = "userInput"
    id: int


class FieldSource(BaseModel):
    destinationField: str
    source: Union[ChatSource, DocumentSource, UserSource] = Field(discriminator="srcType")


class LinkedEntity(BaseModel):
    entityType: LinkedEntityType
    id: int


class TodoUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    dueDate: datetime | None = None
    doneDate: datetime | None = None


class TodoShortInfo(TodoUpdate):
    model_config = ConfigDict(from_attributes=True)

    id: int
    type: TodoType
    deletedDate: datetime | None = None


class UserLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.users] = LinkedEntityType.users
    entity: UserDetails


class JobLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.jobs] = LinkedEntityType.jobs
    entity: JobInfo


class ApplianceLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.appliances] = LinkedEntityType.appliances
    entity: ApplianceInfo


class ChatLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.chats] = LinkedEntityType.chats
    entity: ChatInfo
    lastMessages: list[Message] | None = None


class DocumentLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.documents] = LinkedEntityType.documents
    entity: DocumentInfo


class InsuranceLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.insurances] = LinkedEntityType.insurances


class LegalLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.legals] = LinkedEntityType.legals


class ProjectLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.projects] = LinkedEntityType.projects


class TodoLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.todos] = LinkedEntityType.todos
    entity: TodoShortInfo


class ContextualPromptLinkedEntityInfo(LinkedEntity):
    model_config = ConfigDict(from_attributes=True)

    entityType: Literal[LinkedEntityType.contextual_prompts] = LinkedEntityType.contextual_prompts
    type: str
    prompt: str


LinkedEntityInfo = (
    UserLinkedEntityInfo
    | JobLinkedEntityInfo
    | ApplianceLinkedEntityInfo
    | ChatLinkedEntityInfo
    | DocumentLinkedEntityInfo
    | InsuranceLinkedEntityInfo
    | LegalLinkedEntityInfo
    | ProjectLinkedEntityInfo
    | ContextualPromptLinkedEntityInfo
    | TodoLinkedEntityInfo
)


class TodoFullInfo(TodoShortInfo):
    sources: list[FieldSource]


class TodoCreate(TodoUpdate):
    pass


class TodoAiCreate(BaseModel):
    todo: TodoCreate
    srcType: SourceType
    srcId: int


class TodoAiUpdate(BaseModel):
    todo: TodoUpdate
    srcType: SourceType
    srcId: int


class PropertyAiUpdate(BaseModel):
    propertyDetails: PropertyDetailsData
    srcType: SourceType = SourceType.document
    srcId: int


class FindTodo(BaseModel):
    title: str
    description: str | None
    dueDate: datetime | None


class NotificationCreate(BaseModel):
    context: str | None = None
    payload: NotificationPayload


class NotificationInfo(BaseModel):
    id: int
    context: str | None = None
    payload: NotificationPayload
    created_at: datetime = Field(serialization_alias="createdAt")
    updated_at: datetime = Field(serialization_alias="updatedAt")


class FindTodoChatMessage(BaseModel):
    content: str
    senderType: str

    class Config:
        from_attributes = True


class ConvertGuestUserToRegisteredRequest(BaseModel):
    email: EmailStr
    firstName: str
    lastName: str


class ConvertGuestUserToRegisteredResponse(BaseModel):
    userId: int
    clerkId: str
    signInToken: str


class ContextualPromptCreate(BaseModel):
    prompts: list[str]
    todo_id: int = Field(validation_alias="todoId", serialization_alias="todoId")
    prompt_type: str = Field(validation_alias="promptType", serialization_alias="promptType")


class ContextualPromptInfo(BaseModel):
    id: int
    type: str
    prompt: str
    created_at: datetime = Field(serialization_alias="createdAt")
    updated_at: datetime = Field(serialization_alias="updatedAt")

    model_config = ConfigDict(from_attributes=True)
