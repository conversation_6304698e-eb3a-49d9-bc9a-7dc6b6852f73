import asyncio
import logging
import os
import uuid
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import List, Dict, Any, Union, Mapping, Optional

from qdrant_client import AsyncQdrantClient, models
from qdrant_client.conversions import common_types as types

from src.ai_clients.OpenAIClient import OpenAIClient


@dataclass
class SearchResult:
    score: float
    userId: str
    documentId: str
    bucketName: str
    fileKey: str
    content: str


@dataclass
class FileChunkSearchResult:
    score: float
    userId: str
    documentId: str
    chunkId: str
    metadata: Dict[str, Any]
    content: str
    bucketName: str
    fileKey: str


@dataclass
class ChatSearchResult:
    score: float
    userId: str
    chatId: str
    chatSummary: str
    conversationHistory: list


class QdrantDAO:
    def __init__(self, qdrant_client_asynch=None, openai_client=None):
        self.logger = logging.getLogger("uvicorn")
        self.logger.info("Initializing QdrantDAO")
        self._qdrant_url = os.environ["QDRANT_DB_URL"]
        self._qdrant_api_key = os.environ["QDRANT_API_KEY"]
        self._collection_name = "users_files"
        self._collection_name_file_chunks = "users_files_chunks"
        self._collection_name_chats = "users_chats"
        self._collection_name_tools_cache = "tools_cache"
        self._cache_expiry_hours = 2  # Cache expiry time in hours
        self._collection_name_chats_jobs = "users_chats_jobs"
        self._collection_name_chats_search = "users_chats_search"  # New collection for searchable chats
        self._vector_size = 1536
        self._distance = models.Distance.COSINE
        self._qdrant_client_asynch = qdrant_client_asynch or AsyncQdrantClient(
            url=self._qdrant_url, api_key=self._qdrant_api_key
        )
        self._openai_client = openai_client or OpenAIClient()

    async def init_collections(self):
        await asyncio.gather(
            self._create_collection_if_not_exists(
                collection_name=self._collection_name,
                vectors_config=models.VectorParams(size=self._vector_size, distance=models.Distance.COSINE),
            ),
            self._create_collection_if_not_exists(
                collection_name=self._collection_name_file_chunks,
                vectors_config=models.VectorParams(size=self._vector_size, distance=models.Distance.COSINE),
            ),
            self._create_collection_if_not_exists(
                collection_name=self._collection_name_chats,
                vectors_config=models.VectorParams(size=1, distance=models.Distance.COSINE),
            ),
            self._create_collection_if_not_exists(
                collection_name=self._collection_name_tools_cache,
                vectors_config=models.VectorParams(size=1, distance=models.Distance.COSINE),
            ),
            self._create_collection_if_not_exists(
                collection_name=self._collection_name_chats_jobs,
                vectors_config=models.VectorParams(size=1, distance=models.Distance.COSINE),
            ),
            self._create_collection_if_not_exists(
                collection_name=self._collection_name_chats_search,
                vectors_config=models.VectorParams(size=self._vector_size, distance=models.Distance.COSINE),
            ),
        )
        # Create indexes for all collections and their filter fields
        await self._create_payload_indexes()

    async def _create_collection_if_not_exists(
        self,
        collection_name: str,
        vectors_config: Optional[Union[types.VectorParams, Mapping[str, types.VectorParams]]] = None,
    ):
        if not await self._qdrant_client_asynch.collection_exists(collection_name):
            await self._qdrant_client_asynch.create_collection(
                collection_name=collection_name, vectors_config=vectors_config
            )

    async def _check_index_exists(self, collection_name: str, field_name: str) -> bool:
        """Check if a payload index exists for a given field in a collection."""
        try:
            collection_info = await self._qdrant_client_asynch.get_collection(collection_name)

            # Check if collection has payload schema with indexed fields
            if hasattr(collection_info, "payload_schema") and collection_info.payload_schema:
                # payload_schema is a dict where keys are field names and values are PayloadIndexInfo
                if field_name in collection_info.payload_schema:
                    self.logger.info(f"Index exists for {collection_name}.{field_name}")
                    return True

            # Fallback: check config params for older Qdrant versions
            if hasattr(collection_info, "config") and hasattr(collection_info.config, "params"):
                params = collection_info.config.params

                # Check for payload index configuration
                if hasattr(params, "payload_index_fields") and params.payload_index_fields:
                    return field_name in params.payload_index_fields

                # Alternative check for indexed fields
                if hasattr(params, "index_fields") and params.index_fields:
                    for field in params.index_fields:
                        if isinstance(field, str) and field == field_name:
                            return True
                        elif isinstance(field, dict) and field.get("field_name") == field_name:
                            return True

            return False

        except Exception as e:
            self.logger.warning(f"Error checking index existence for {collection_name}.{field_name}: {e}")
            # On exception assume it doesn't exist and try to create it
            return False

    async def _create_index_if_not_exists(self, collection_name: str, field_name: str, field_type: str = "keyword"):
        """Create a payload index if it doesn't already exist."""
        if not await self._check_index_exists(collection_name, field_name):
            try:
                self.logger.info(f"Creating index for {collection_name}.{field_name} with type {field_type}")
                await self._qdrant_client_asynch.create_payload_index(
                    collection_name=collection_name,
                    field_name=field_name,
                    field_schema=field_type,
                )
                self.logger.info(f"Successfully created index for {collection_name}.{field_name}")
            except Exception as e:
                # Check if the error is due to index already existing
                error_message = str(e).lower()
                if "already exists" in error_message or "duplicate" in error_message:
                    self.logger.info(f"Index already exists for {collection_name}.{field_name}")
                else:
                    self.logger.error(f"Error creating index for {collection_name}.{field_name}: {e}")
        else:
            self.logger.debug(f"Index already exists for {collection_name}.{field_name}")

    async def _create_payload_indexes(self):
        """Create payload indexes for all collections and their filter fields."""
        indexes_to_create = [
            # users_files collection
            (self._collection_name, "userId", "keyword"),
            (self._collection_name, "documentId", "keyword"),
            (self._collection_name, "bucketName", "keyword"),
            (self._collection_name, "fileKey", "keyword"),
            # users_files_chunks collection
            (self._collection_name_file_chunks, "userId", "keyword"),
            (self._collection_name_file_chunks, "documentId", "keyword"),
            (self._collection_name_file_chunks, "chunkId", "keyword"),
            (self._collection_name_file_chunks, "bucketName", "keyword"),
            (self._collection_name_file_chunks, "fileKey", "keyword"),
            # users_chats collection
            (self._collection_name_chats, "userId", "keyword"),
            (self._collection_name_chats, "chatId", "keyword"),
            # tools_cache collection
            (self._collection_name_tools_cache, "userId", "keyword"),
            (self._collection_name_tools_cache, "chatId", "keyword"),
            (self._collection_name_tools_cache, "created_at", "datetime"),
            (self._collection_name_tools_cache, "expires_at", "datetime"),
            # users_chats_jobs collection
            (self._collection_name_chats_jobs, "userId", "keyword"),
            (self._collection_name_chats_jobs, "chatId", "keyword"),
            # users_chats_search collection
            (self._collection_name_chats_search, "userId", "keyword"),
            (self._collection_name_chats_search, "chatId", "keyword"),
        ]

        await asyncio.gather(
            *[
                self._create_index_if_not_exists(collection_name, field_name, field_type)
                for collection_name, field_name, field_type in indexes_to_create
            ]
        )

    async def upsert(
        self,
        user_id: str,
        document_id: str,
        bucket_name: str,
        content: str,
        file_key: str,
        embedding: list[float] = None,
    ):
        if embedding is None:
            embedding = self._openai_client.get_embedding(content)

        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{document_id}_{file_key}_{content}")),
            payload={
                "userId": user_id,
                "documentId": document_id,
                "bucketName": bucket_name,
                "fileKey": file_key,
                "content": content,
            },
            vector=embedding,
        )

        await self._qdrant_client_asynch.upsert(collection_name=self._collection_name, points=[point])

    async def query(
        self, user_id: str, query: str, limit: int = 5, query_embedding: list[float] = None
    ) -> List[SearchResult]:
        if query_embedding is None:
            query_embedding = self._openai_client.get_embedding(query)

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name,
            query_vector=query_embedding,
            query_filter=models.Filter(
                must=[models.FieldCondition(key="userId", match=models.MatchValue(value=user_id))]
            ),
            limit=limit,
        )

        return [
            SearchResult(
                score=result.score,
                userId=result.payload["userId"],
                documentId=result.payload["documentId"],
                bucketName=result.payload["bucketName"],
                fileKey=result.payload["fileKey"],
                content=result.payload["content"],
            )
            for result in results
        ]

    async def upsert_file_chunk(
        self,
        user_id: str,
        document_id: str,
        chunk_id: str,
        metadata: Dict[str, Any],
        content: str,
        bucket_name: str,
        file_key: str,
        embedding: list[float] = None,
    ):
        if embedding is None:
            embedding = self._openai_client.get_embedding(content)

        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{document_id}_{file_key}_{content}")),
            payload={
                "userId": str(user_id),
                "documentId": str(document_id),
                "chunkId": str(chunk_id),
                "metadata": str(metadata),
                "content": str(content),
                "bucketName": str(bucket_name),
                "fileKey": str(file_key),
            },
            vector=embedding,
        )

        await self._qdrant_client_asynch.upsert(collection_name=self._collection_name_file_chunks, points=[point])

    async def query_file_chunk(
        self, user_id: str, query: str, limit: int = 5, query_embedding: list[float] = None
    ) -> List[FileChunkSearchResult]:
        if query_embedding is None:
            query_embedding = self._openai_client.get_embedding(query)

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name_file_chunks,
            query_vector=query_embedding,
            query_filter=models.Filter(
                must=[models.FieldCondition(key="userId", match=models.MatchValue(value=str(user_id)))]
            ),
            limit=limit,
        )

        return [
            FileChunkSearchResult(
                score=result.score,
                userId=result.payload["userId"],
                documentId=result.payload["documentId"],
                chunkId=result.payload["chunkId"],
                metadata=result.payload["metadata"],
                content=result.payload["content"],
                bucketName=result.payload["bucketName"],
                fileKey=result.payload["fileKey"],
            )
            for result in results
        ]

    async def upsert_chat_history(self, user_id: str, chat_id: str, conversation_history):
        self.logger.info(
            f"upsert_chat_history(): user_id: {user_id}, chat_id: {chat_id} conversation_history: \n\n {conversation_history} \n\n"
        )
        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{user_id}_{chat_id}")),
            payload={"userId": user_id, "chatId": chat_id, "conversation_history": conversation_history},
            vector=[0],
        )

        await self._qdrant_client_asynch.upsert(collection_name=self._collection_name_chats, points=[point])

    async def get_chat_history(self, user_id: str, chat_id: str):
        my_filter = models.Filter(
            must=[
                models.FieldCondition(key="userId", match=models.MatchValue(value=user_id)),
                models.FieldCondition(key="chatId", match=models.MatchValue(value=chat_id)),
            ]
        )

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name_chats,
            query_vector=[0],  # Dummy vector since we're only using filters
            query_filter=my_filter,
            limit=1,
        )

        if results:
            self.logger.info(
                f"get_chat_history(): user_id: {user_id}, chat_id: {chat_id} \n\n {results[0].payload["conversation_history"]} \n\n"
            )
            return results[0].payload["conversation_history"]

        self.logger.info(f"get_chat_history(): user_id: {user_id}, chat_id: {chat_id} \n\n [] \n\n")
        return []

    async def upsert_tools_cache(self, user_id: str, chat_id: str, tools_cache: str):
        self.logger.info(
            f"upsert_tools_cache(): user_id: {user_id}, chat_id: {chat_id} tools_cache: \n\n {tools_cache} \n\n"
        )

        # Add timestamp to the cache entry
        current_time = datetime.now()

        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{user_id}_{chat_id}")),
            payload={
                "userId": str(user_id),
                "chatId": str(chat_id),
                "tools_cache": tools_cache,
                "created_at": current_time.isoformat(),
                "expires_at": (current_time + timedelta(hours=self._cache_expiry_hours)).isoformat(),
            },
            vector=[0],
        )

        await self._qdrant_client_asynch.upsert(collection_name=self._collection_name_tools_cache, points=[point])

    async def get_tools_cache(self, user_id: str, chat_id: str):
        my_filter = models.Filter(
            must=[
                models.FieldCondition(key="userId", match=models.MatchValue(value=str(user_id))),
                models.FieldCondition(key="chatId", match=models.MatchValue(value=str(chat_id))),
            ]
        )

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name_tools_cache,
            query_vector=[0],  # Dummy vector since we're only using filters
            query_filter=my_filter,
            limit=1,
        )

        if results:
            result = results[0]
            payload = result.payload

            # Check if the cache has expired
            if "expires_at" in payload:
                expires_at = datetime.fromisoformat(payload["expires_at"])
                current_time = datetime.now()

                if current_time > expires_at:
                    self.logger.info(f"get_tools_cache(): Cache expired for user_id: {user_id}, chat_id: {chat_id}")
                    # Optionally delete the expired cache entry
                    return "TOOLS CACHE EXPIRED"

            self.logger.info(
                f"get_tools_cache(): user_id: {user_id}, chat_id: {chat_id} \n\n {payload['tools_cache']} \n\n"
            )
            return payload["tools_cache"]

        self.logger.info(f"get_tools_cache(): user_id: {user_id}, chat_id: {chat_id} \n\n [] \n\n")
        return []

    def _create_chat_summary(self, conversation_history: list) -> str:
        if not conversation_history:
            return ""

        # Extract user messages and assistant responses
        messages = []

        if not conversation_history:
            return ""

        for msg in conversation_history:
            messages.append(f"Human: {msg[0]}\nAssistant's response: {msg[1]}\n\n")

        # Join messages and truncate if too long (to avoid token limits)
        summary = " | ".join(messages)
        if len(summary) > 8000:  # Rough token limit consideration
            summary = summary[:8000] + "..."

        return summary

    async def upsert_searchable_chat(
        self,
        user_id: str,
        chat_id: str,
        conversation_history: list,
        chat_summary: str = None,
        embedding: list[float] = None,
    ):
        if chat_summary is None:
            chat_summary = self._create_chat_summary(conversation_history)

        if not chat_summary:
            self.logger.warning(f"Empty chat summary for user_id: {user_id}, chat_id: {chat_id}")
            return

        if embedding is None:
            embedding = self._openai_client.get_embedding(chat_summary)

        point = models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{user_id}_{chat_id}_search")),
            payload={
                "userId": user_id,
                "chatId": chat_id,
                "chatSummary": chat_summary,
                "conversationHistory": conversation_history,
            },
            vector=embedding,
        )

        await self._qdrant_client_asynch.upsert(collection_name=self._collection_name_chats_search, points=[point])

        self.logger.info(
            f"upsert_searchable_chat(): user_id: {user_id}, chat_id: {chat_id}, summary length: {len(chat_summary)}"
        )

    async def search_chat_history(
        self, query: str, user_id: str, limit: int = 5, query_embedding: list[float] = None
    ) -> List[ChatSearchResult]:
        if query_embedding is None:
            query_embedding = self._openai_client.get_embedding(query)

        results = await self._qdrant_client_asynch.search(
            collection_name=self._collection_name_chats_search,
            query_vector=query_embedding,
            query_filter=models.Filter(
                must=[models.FieldCondition(key="userId", match=models.MatchValue(value=user_id))]
            ),
            limit=limit,
        )

        search_results = [
            ChatSearchResult(
                score=result.score,
                userId=result.payload["userId"],
                chatId=result.payload["chatId"],
                chatSummary=result.payload["chatSummary"],
                conversationHistory=result.payload["conversationHistory"],
            )
            for result in results
        ]

        self.logger.info(
            f"search_chat_history(): query: '{query}', user_id: {user_id}, found {len(search_results)} results"
        )

        return search_results

    async def update_chat_search_on_history_change(self, user_id: str, chat_id: str, conversation_history: list):
        # Update regular chat history
        await self.upsert_chat_history(user_id, chat_id, conversation_history)

        # Update searchable chat
        await self.upsert_searchable_chat(user_id, chat_id, conversation_history)
