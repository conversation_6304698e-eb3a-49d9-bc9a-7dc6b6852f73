#!/bin/bash
set -e

run_web() {
  echo "Starting web server..."
  exec /usr/src/.venv/bin/uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 4
}

run_abandoned_chats_worker_lambda() {
  echo "Starting abandon chat worker (Lambda RIC)..."
  exec /usr/src/.venv/bin/python -m awslambdaric src.sqs_consumers.process_abandoned_chats.handler
}


main() {
  case "$1" in
    web)
      run_web
      ;;
    abandoned_chats_worker_lambda)
      run_abandoned_chats_worker_lambda
      ;;
    *)
      echo "Unknown command: $1"
      exit 1
      ;;
  esac
}

main "$@"