services:
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: dev-user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dev_db
    ports:
      - "5432:5432"
    volumes:
      - db-data:/var/lib/postgresql/data:cached
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    command: web
    tty: true
    environment:
      PYTHONPATH: .
      DATABASE_URL: "dev-user:password@postgres:5432/dev_db"
      SENTRY_ENVIRONMENT: "local"
      SENTRY_DSN: "https://<EMAIL>/4508080474751056"
      CRYSTAL_ROOF_API_KEY: "DEMO"
      AI_ENGINE_API_BASE_URL: https://pmvapnprtw.eu-west-2.awsapprunner.com/
      AI_ENGINE_API_KEY: dev
      ADMIN_API_KEY: dev-admin
      AWS_DEFAULT_REGION: eu-west-2
      AWS_PROFILE: dev
      CLERK_API_KEY: dev
      CLERK_PEM_PUBLIC_KEY: dev
      CLERK_WEBHOOK_SECRET: dev
      DOCUMENTS_S3_BUCKET_NAME: heyalfie-backend-documents-development
      IDEALPOSTCODES_API_KEY: dev
      CLERK_PERMITTED_ORIGINS: https://app.heyalfie.com
    depends_on:
      - "postgres"
    ports:
      - "8000:8000"
  postgres-tests:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: test-user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: test_db
    ports:
      - "5433:5432"
    volumes:
      - test-db-data:/var/lib/postgresql/data:cached
  backend-tests:
    build:
      context: .
      dockerfile: Dockerfile
      target: test
    tty: true
    environment:
      PYTHONPATH: .
      DATABASE_URL: "test-user:password@postgres-tests:5432/test_db"
    depends_on:
      - "postgres-tests"

volumes:
  db-data:
  test-db-data: