[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = {extras = ["standard"], version = "==0.115.6"}
fastapi-pagination = "==0.12.32"
email-validator = "==2.2.0"
uvicorn = "==0.32.1"
alembic = "==1.14.0"
asyncpg = "==0.30.0"
greenlet = "==3.1.1"
sqlalchemy = {extras = ["asyncio"], version = "==2.0.36"}
sentry-sdk = {extras = ["fastapi"], version = "==2.19.2"}
aiohttp = "==3.11.10"
pyproj = "==3.7.0"
pyjwt = {extras = ["crypto"], version = "==2.10.1"}
svix = "==1.43.0"
sendgrid = "==6.11.0"
aioboto3 = "==14.1.0"
tenacity = ">=9.1.2,<10"
langchain-qdrant = "==0.2.0"
langchain-community = "==0.3.27"
langchain-openai = "==0.3.27"
tiktoken = "==0.9.0"
langchain = "==0.3.26"
langchain-core = "==0.3.68"
qdrant-client = "==1.14.3"
pandas = "==2.3.1"
pdf2image = "==1.17.0"
duckduckgo-search = "==8.1.1"
pydantic = "==2.11.7"
typing = "==3.7.4.3"
anthropic = "==0.57.1"
openai="==1.93.1"
together = "==1.5.18"
requests = "==2.32.4"
langchain_together ="==0.3.0"
langsmith="==0.4.4"
beautifulsoup4="==4.13.4"
aiofiles = "==24.1.0"
google-genai="==1.20.0"
langchain-anthropic="==0.3.17"
google-generativeai="==0.8.5"
python-magic = "==0.4.27"
python-dotenv = "1.1.1"
clerk-backend-api = "==1.8.0"

[dev-packages]
pytest = "==8.3.4"
faker = "==33.1.0"
black = "==24.10.0"
flake8 = "==7.1.1"
pytest-cov = "==6.0.0"
tox = "==4.23.2"
psycopg = {extras = ["binary"], version = "==3.2.3"}
aioresponses = "==0.7.7"
pytest-asyncio = "==0.24.0"
pytest-mock = "==3.14.0"
moto = {extras = ["server"], version = "==5.1.1"}

[requires]
python_version = "3.12"
python_full_version = "3.12.11"
