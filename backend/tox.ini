[tox]
envlist = interation-tests, black, flake8, migrations-lint
skipsdist = true

[pytest]
;asyncio_default_fixture_loop_scope=session
asyncio_mode=auto
filterwarnings=ignore::DeprecationWarning:botocore.*:

[tool.black]
line-length = 120

[testenv]
allowlist_externals =
    black
    flake8
    pytest

[testenv:interation-tests]
commands =
    pytest \
    --cov=src \
    --cov-config=.coveragerc \
    src
passenv = PYTHONPATH,DATABASE_URL
sitepackages = true

[testenv:black]
commands = black -l 120 --check src --extend-exclude="src/(db_models|alembic|agents|ai_base_rag|ai_dao)"

[testenv:flake8]
commands = flake8 --exclude src/alembic,src/tests,src/db_models,src/agents,src/ai_base_rag,src/ai_dao --max-line-length=120 src

[testenv:migrations-lint]
commands = python migrations-check.py
