import ast
import sys
from pathlib import Path

MIGRATIONS_DIR = Path("src/alembic/versions")

def is_not_nullable_without_default(call: ast.Call) -> tuple[bool, str]:
    """
    Returns (True, column_name) if column has nullable=False and no server_default.
    Otherwise returns (False, column_name or '<unknown>')
    """
    args = call.args
    keywords = {kw.arg: kw.value for kw in call.keywords}

    col_name = "<unknown>"
    if args and isinstance(args[0], ast.Constant):
        col_name = args[0].value

    nullable = keywords.get("nullable")
    server_default = keywords.get("server_default")

    is_not_nullable = isinstance(nullable, ast.Constant) and nullable.value is False
    has_server_default = server_default is not None

    return (is_not_nullable and not has_server_default), col_name


def extract_add_column_calls(tree: ast.Module) -> list[ast.Call]:
    calls = []

    for node in ast.walk(tree):
        if isinstance(node, ast.Call):
            # Match op.add_column(...)
            if (
                isinstance(node.func, ast.Attribute)
                and node.func.attr == "add_column"
                and isinstance(node.func.value, ast.Name)
                and node.func.value.id == "op"
            ):
                # Look inside args for sa.Column(...)
                for arg in node.args:
                    if isinstance(arg, ast.Call):
                        if (
                            isinstance(arg.func, ast.Attribute)
                            and isinstance(arg.func.value, ast.Name)
                            and arg.func.value.id == "sa"
                            and arg.func.attr == "Column"
                        ):
                            calls.append(arg)
    return calls


def check_migration_file(filepath: Path) -> list[str]:
    issues = []

    try:
        with open(filepath, "r") as f:
            content = f.read()
        tree = ast.parse(content)
    except Exception as e:
        return [f"{filepath.name}: ⚠️ Cannot parse file: {e}"]

    for call in extract_add_column_calls(tree):
        is_bad, col_name = is_not_nullable_without_default(call)
        if is_bad and 'I have verified that all non-nullable columns do not need a default value' not in content:
            issues.append(f"{filepath.name}: NOT NULL column '{col_name}' without server_default")

    return issues


def main():
    if not MIGRATIONS_DIR.exists():
        print(f"❌ Directory not found: {MIGRATIONS_DIR}")
        sys.exit(1)

    all_issues = []
    for file in MIGRATIONS_DIR.glob("*.py"):
        all_issues.extend(check_migration_file(file))

    if all_issues:
        print("❌ Potentially unsafe migrations found:\n")
        for issue in all_issues:
            print(issue)
        sys.exit(1)
    else:
        print("✅ All migrations safe.")

if __name__ == "__main__":
    main()
