services:
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data:cached
    environment:
      POSTGRES_USER: dev-user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dev_db
  qdrant:
    image: qdrant/qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant-data:/qdrant/storage:z
    environment:
      QDRANT__SERVICE__API_KEY: qdrant-key
  backend:
    build:
      context: ${BACKEND_CONTEXT}
      dockerfile: Dockerfile
      target: runtime
    command: web
    tty: true
    depends_on:
      - "postgres"
    ports:
      - "8000:8000"
    environment:
      PYTHONPATH: .
      DATABASE_URL: "dev-user:password@postgres:5432/dev_db"
      CORS_ALLOWED_ORIGINS: http://localhost:3000
      AI_ENGINE_API_KEY: dev
      CLERK_PERMITTED_ORIGINS: http://localhost:3000
      SENTRY_ENVIRONMENT: ${SENTRY_ENVIRONMENT}
      SENTRY_DSN: ${SENTRY_DSN}
      CRYSTAL_ROOF_API_KEY: ${CRYSTAL_ROOF_API_KEY}
      SENDGRID_DEFAULT_FROM_EMAIL: <EMAIL>
      CLERK_API_KEY: ${CLERK_SECRET_KEY}
      CLERK_PEM_PUBLIC_KEY: ${CLERK_PEM_PUBLIC_KEY}
      CLERK_WEBHOOK_SECRET: ${CLERK_WEBHOOK_SECRET}
      IDEALPOSTCODES_API_KEY: ${IDEALPOSTCODES_API_KEY}
      OS_DATA_PLACES_API_KEY: ${OS_DATA_PLACES_API_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      STAGE: ${STAGE}
      DOCUMENTS_S3_BUCKET_NAME: ${DOCUMENTS_S3_BUCKET_NAME}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      HUBSPOT_API_KEY: ${HUBSPOT_API_KEY}
      ADMIN_API_KEY: dev-admin
      IS_AWS: true
      POSTGRES_USER: dev-user
      POSTGRES_PASSWORD: password
      IS_LOCAL_TESTING: true
      QDRANT_DB_URL: http://qdrant:6333
      QDRANT_API_KEY: qdrant-key
      OPEN_AI_API_KEY: ${OPEN_AI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      TOGETHER_AI_API_KEY: ${TOGETHER_AI_API_KEY}
      LANGSMITH_API_KEY: ${LANGSMITH_API_KEY}
      GEMINI_API_KEY: ${GEMINI_API_KEY}
      SERPR_API_KEY: ${SERPR_API_KEY}
      PSE_API_KEY: ${PSE_API_KEY}
      PSE_CX: ${PSE_CX}
      LANGCHAIN_PROJECT: ${LANGCHAIN_PROJECT}
      BACKEND_API_BASE_URL: http://backend:8000
      GOOGLE_API_KEY: ${GOOGLE_API_KEY}
      TODO_EXPIRATION_AFTER_MINUTES: 1
      GUEST_USER_TOKEN_SIGNING_KEY: local-testing-key
      BASE_URL: http://localhost:8000
  frontend:
    build:
      context: ${FRONTEND_CONTEXT}
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
        NEXT_PUBLIC_CUSTOM_API_URL: http://localhost:8000
    tty: true
    depends_on:
      - "postgres"
      - "backend"
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_VERCEL_ENV: staging
      CLERK_SECRET_KEY: ${CLERK_SECRET_KEY}
      NEXT_PUBLIC_TEMP_TOKEN: ${NEXT_PUBLIC_TEMP_TOKEN}
  elasticmq:
    image: softwaremill/elasticmq
    ports:
      - "9324:9324"
    volumes:
      - ./elasticmq.conf:/opt/elasticmq.conf
volumes:
  postgres-data:
  qdrant-data: