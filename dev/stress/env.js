export function getEnvs(getDefinition, env = process.env) {
    const result = {};
    const definition = getDefinition(env);
    for (const [name, description] of Object.entries(definition)) {
        if (!description) continue;
        const value = env[name];
        if (!value) {
            console.log(`\n*** REQUIRED ENVIRONMENTAL VARIABLES:\n`);
            for (const [key, description] of Object.entries(definition)) {
                if (!description) continue;
                console.log(`${key}: ${description}`);
            }
            console.log(`\n`);
            throw new Error(`Missing required environment variable: ${name}`);
        }
        result[name] = value;
    }
    return result;
}