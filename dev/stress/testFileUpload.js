import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
    stages: [
        {duration: '1s', target: __ENV.VIRTUAL_USERS},
        {duration: '30s', target: __ENV.VIRTUAL_USERS}
    ],
    thresholds: {
        http_req_failed: ['rate==0'],
    },
};

// Function to detect MIME type based on file extension
function getMimeType(fileName) {
    const extension = fileName.toLowerCase().split('.').pop();

    const mimeTypes = {
        // Images
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'bmp': 'image/bmp',
        'webp': 'image/webp',
        'svg': 'image/svg+xml',
        'tiff': 'image/tiff',
        'tif': 'image/tiff',
        'ico': 'image/x-icon',

        // PDFs
        'pdf': 'application/pdf'
    };

    return mimeTypes[extension] || 'application/octet-stream';
}

// Function to validate file type
function isSupportedFileType(fileName) {
    const extension = fileName.toLowerCase().split('.').pop();
    const supportedExtensions = [
        'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'tif', 'ico', 'pdf'
    ];

    return supportedExtensions.includes(extension);
}

// Load file in init stage (global scope)
const filePath = __ENV.FILE_PATH;
if (!filePath) {
    throw new Error('FILE_PATH environment variable is required');
}

const fileName = filePath.split('/').pop();
if (!isSupportedFileType(fileName)) {
    throw new Error(`Unsupported file type: ${fileName}. Supported types: PNG, JPG, JPEG, GIF, BMP, WEBP, SVG, TIFF, ICO, PDF`);
}

// Load the file data
const fileData = open(filePath, 'b');
if (!fileData) {
    throw new Error(`File not found: ${filePath}`);
}

const mimeType = getMimeType(fileName);
console.log(`File loaded successfully: ${filePath} (${fileData.length} bytes)`);
console.log(`Detected MIME type: ${mimeType}`);

export default function () {
    const token = __ENV.TOKEN;
    const apiUrl = __ENV.API_URL;

    if (!token) {
        throw new Error('TOKEN environment variable is required');
    }

    if (!apiUrl) {
        throw new Error('API_URL environment variable is required');
    }

    const url = `${apiUrl}/documents/?uploadContext=chat`;

    const formData = {
        file: http.file(fileData, fileName, mimeType)
    };

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
    };

    const response = http.post(url, formData, { headers });
    console.log(`Received response: ${response.body}`);

    // Basic response checks
    check(response, {
        'status is 200': (r) => r.status === 200,
        'response time < 10s': (r) => r.timings.duration < 10000,
        'response is valid JSON': (r) => {
            try {
                JSON.parse(r.body);
                return true;
            } catch {
                return false;
            }
        },
    });

    // Validate response structure
    if (response.status === 200) {
        try {
            const data = JSON.parse(response.body);

            check(data, {
                'has id': (d) => typeof d.id === 'number',
                'has fileName': (d) => typeof d.fileName === 'string' && d.fileName.length > 0,
                'has sizeInKiloBytes': (d) => typeof d.sizeInKiloBytes === 'number' && d.sizeInKiloBytes > 0,
                'has browserMimeType': (d) => typeof d.browserMimeType === 'string' && d.browserMimeType.length > 0,
                'has createdAt': (d) => typeof d.createdAt === 'string' && d.createdAt.length > 0,
                'has uploadContext': (d) => d.uploadContext === 'chat',
                'has status': (d) => d.status === 'saved',
                'has category field': (d) => d.hasOwnProperty('category'),
                'has label field': (d) => d.hasOwnProperty('label'),
            });

            // Validate createdAt format (ISO 8601)
            check(data, {
                'createdAt is valid ISO date': (d) => {
                    const date = new Date(d.createdAt);
                    return !isNaN(date.getTime()) && d.createdAt.includes('T');
                }
            });

            // Additional validation for MIME type consistency
            check(data, {
                'MIME type matches expectation': (d) => {
                    // Server might return a different but valid MIME type
                    if (mimeType === 'application/pdf') {
                        return d.browserMimeType === 'application/pdf';
                    }
                    // For images, server might detect more specific MIME types
                    return d.browserMimeType.startsWith('image/') || d.browserMimeType === mimeType;
                }
            });

            console.log(`Successfully uploaded ${mimeType} file: ${data.fileName} (ID: ${data.id}, Size: ${data.sizeInKiloBytes}KB, MIME: ${data.browserMimeType})`);
        } catch (e) {
            console.error('Failed to parse response JSON:', e.message);
        }
    } else {
        console.error(`Upload failed with status ${response.status}: ${response.body}`);
    }

    sleep(1);
}