import http from 'k6/http';
import {check, sleep} from 'k6';

export const options = {
    stages: [
        {duration: '1s', target: __ENV.VIRTUAL_USERS},
        {duration: '30s', target: __ENV.VIRTUAL_USERS}
    ],
    thresholds: {
        http_req_failed: ['rate==0'], // Less than 10% of requests should fail
        http_req_duration: ['p(95)<2000'], // 95% of requests should be under 2s
    },
};

// Function to get a valid property ID
function getPropertyId(token, apiUrl, baseHeaders) {
    console.log('Fetching available properties...');
    const propertiesResponse = http.get(
        `${apiUrl}/properties/?page=1`,
        {headers: baseHeaders}
    );

    check(propertiesResponse, {
        'PROPERTIES: status is 200': (r) => r.status === 200,
        'PROPERTIES: response time < 5s': (r) => r.timings.duration < 5000,
        'PROPERTIES: response is valid JSON': (r) => {
            try {
                JSON.parse(r.body);
                return true;
            } catch {
                return false;
            }
        },
    });

    if (propertiesResponse.status === 200) {
        try {
            const propertiesData = JSON.parse(propertiesResponse.body);

            check(propertiesData, {
                'PROPERTIES: has items array': (d) => Array.isArray(d.items),
                'PROPERTIES: has total': (d) => typeof d.total === 'number',
                'PROPERTIES: has page': (d) => typeof d.page === 'number',
                'PROPERTIES: has size': (d) => typeof d.size === 'number',
                'PROPERTIES: has pages': (d) => typeof d.pages === 'number',
                'PROPERTIES: has at least one property': (d) => d.items.length > 0,
            });

            if (propertiesData.items && propertiesData.items.length > 0) {
                const firstProperty = propertiesData.items[0];

                check(firstProperty, {
                    'PROPERTIES: property has id': (d) => typeof d.id === 'number',
                    'PROPERTIES: property has address': (d) => d.address && typeof d.address === 'object',
                    'PROPERTIES: address has id': (d) => d.address && typeof d.address.id === 'number',
                    'PROPERTIES: address has streetLine1': (d) => d.address && typeof d.address.streetLine1 === 'string',
                    'PROPERTIES: address has townOrCity': (d) => d.address && typeof d.address.townOrCity === 'string',
                    'PROPERTIES: address has postcode': (d) => d.address && typeof d.address.postcode === 'string',
                    'PROPERTIES: address has country': (d) => d.address && typeof d.address.country === 'string',
                });

                const propertyId = firstProperty.id;
                console.log(`Successfully retrieved property ID: ${propertyId} (Address: ${firstProperty.address.streetLine1}, ${firstProperty.address.townOrCity}, ${firstProperty.address.postcode})`);
                return propertyId;
            } else {
                console.error('No properties found in the response');
                return null;
            }
        } catch (e) {
            console.error('Failed to parse properties response JSON:', e.message);
            return null;
        }
    } else {
        console.error(`Failed to fetch properties with status ${propertiesResponse.status}: ${propertiesResponse.body}`);
        return null;
    }
}

export default function () {
    const token = __ENV.TOKEN;
    const apiUrl = __ENV.API_URL;

    if (!token) {
        throw new Error('TOKEN environment variable is required');
    }

    if (!apiUrl) {
        throw new Error('API_URL environment variable is required');
    }

    const baseHeaders = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9,pl;q=0.8',
        'authorization': `Bearer ${token}`,
        'origin': 'https://app.staging.heyalfie.com',
        'priority': 'u=1, i',
        'referer': 'https://app.staging.heyalfie.com/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    };

    // Get a valid property ID from the API
    const propertyId = getPropertyId(token, apiUrl, baseHeaders);

    if (!propertyId) {
        console.error('Failed to retrieve a valid property ID. Skipping appliance operations.');
        return;
    }

    sleep(1);

    // Generate random data for appliance creation
    const randomId = Math.random().toString(36).substring(2, 15);
    const applianceData = {
        type: `test-appliance-${randomId}`,
        brand: `TestBrand-${randomId}`,
        model: `TestModel-${randomId}`,
        serialNumber: `SN-${randomId}`,
        warranty: `Warranty-${randomId}`,
        propertyId: propertyId
    };

    // 1. CREATE Operation
    console.log('Testing CREATE operation...');
    const createResponse = http.post(
        `${apiUrl}/appliances/`,
        JSON.stringify(applianceData),
        {
            headers: {
                ...baseHeaders,
                'content-type': 'application/json'
            }
        }
    );

    check(createResponse, {
        'CREATE: status is 200 or 201': (r) => r.status === 200 || r.status === 201,
        'CREATE: response time < 5s': (r) => r.timings.duration < 5000,
        'CREATE: response is valid JSON': (r) => {
            try {
                JSON.parse(r.body);
                return true;
            } catch {
                return false;
            }
        },
    });

    let createdApplianceId = null;
    if (createResponse.status === 200 || createResponse.status === 201) {
        try {
            const createdAppliance = JSON.parse(createResponse.body);
            createdApplianceId = createdAppliance.id;

            check(createdAppliance, {
                'CREATE: has id': (d) => typeof d.id === 'number',
                'CREATE: has correct type': (d) => d.type === applianceData.type,
                'CREATE: has correct brand': (d) => d.brand === applianceData.brand,
                'CREATE: has correct model': (d) => d.model === applianceData.model,
                'CREATE: has correct serialNumber': (d) => d.serialNumber === applianceData.serialNumber,
                'CREATE: has correct warranty': (d) => d.warranty === applianceData.warranty,
                'CREATE: has correct propertyId': (d) => d.propertyId === applianceData.propertyId,
                'CREATE: has documents array': (d) => Array.isArray(d.documents),
            });

            console.log(`Successfully created appliance with ID: ${createdApplianceId} for property ID: ${propertyId}`);
        } catch (e) {
            console.error('Failed to parse CREATE response JSON:', e.message);
        }
    } else {
        console.error(`CREATE failed with status ${createResponse.status}: ${createResponse.body}`);
    }

    sleep(1);

    // 2. LIST Operation
    console.log('Testing LIST operation...');
    const listResponse = http.get(
        `${apiUrl}/appliances/?page=1&size=10`,
        {headers: baseHeaders}
    );

    check(listResponse, {
        'LIST: status is 200': (r) => r.status === 200,
        'LIST: response time < 3s': (r) => r.timings.duration < 3000,
        'LIST: response is valid JSON': (r) => {
            try {
                JSON.parse(r.body);
                return true;
            } catch {
                return false;
            }
        },
    });

    if (listResponse.status === 200) {
        try {
            const listData = JSON.parse(listResponse.body);

            check(listData, {
                'LIST: has items array': (d) => Array.isArray(d.items),
                'LIST: has total': (d) => typeof d.total === 'number',
                'LIST: has page': (d) => d.page === 1,
                'LIST: has size': (d) => d.size === 10,
                'LIST: has pages': (d) => typeof d.pages === 'number',
                'LIST: total is non-negative': (d) => d.total >= 0,
            });

            // Validate structure of items if any exist
            if (listData.items && listData.items.length > 0) {
                const firstItem = listData.items[0];
                check(firstItem, {
                    'LIST: item has id': (d) => typeof d.id === 'number',
                    'LIST: item has type': (d) => typeof d.type === 'string',
                    'LIST: item has brand': (d) => typeof d.brand === 'string',
                    'LIST: item has model': (d) => typeof d.model === 'string',
                    'LIST: item has serialNumber': (d) => typeof d.serialNumber === 'string',
                    'LIST: item has warranty': (d) => typeof d.warranty === 'string',
                    'LIST: item has propertyId': (d) => typeof d.propertyId === 'number',
                    'LIST: item has documents array': (d) => Array.isArray(d.documents),
                });

                // Check if our newly created appliance appears in the list
                const createdApplianceInList = listData.items.find(item => item.id === createdApplianceId);
                if (createdApplianceInList) {
                    check(createdApplianceInList, {
                        'LIST: created appliance found in list': (d) => d.id === createdApplianceId,
                        'LIST: created appliance has correct data': (d) =>
                            d.type === applianceData.type &&
                            d.brand === applianceData.brand &&
                            d.model === applianceData.model &&
                            d.serialNumber === applianceData.serialNumber &&
                            d.warranty === applianceData.warranty &&
                            d.propertyId === applianceData.propertyId,
                    });
                    console.log(`Verified: newly created appliance (ID: ${createdApplianceId}) appears in the list`);
                }
            }

            console.log(`Successfully retrieved ${listData.items.length} appliances out of ${listData.total} total`);
        } catch (e) {
            console.error('Failed to parse LIST response JSON:', e.message);
        }
    } else {
        console.error(`LIST failed with status ${listResponse.status}: ${listResponse.body}`);
    }
}