import puppeteer from 'puppeteer';
import {spawn} from 'child_process';
import fs from 'fs';
import path from 'path';
import {getEnvs} from "./env.js";

async function signInAndGetToken(appUrl, email, password) {
    console.log(`Signing in to ${appUrl} and getting a token for ${email}...`);
    const browser = await puppeteer.launch({});
    const page = await browser.newPage();
    await page.goto(appUrl);
    await page.setViewport({width: 1080, height: 1024});

    await page.waitForFunction(() => Array.from(document.querySelectorAll('button')).find(button => button.innerText.includes('Accept')))
    await page.waitForFunction(() => !!window.Clerk.client);
    const signInResult = await page.evaluate(async (email, password) => {
        const result = await window.Clerk.client.signIn.create({
            identifier: email, strategy: 'password', password,
        });
        const sessionId = result.createdSessionId;
        const sessions = window.Clerk.client.sessions;
        const session = sessions.find(s => s.id === sessionId);
        const token = await session.getToken();
        return {token};
    }, email, password);

    const token = signInResult.token;
    console.log(`Token obtained: ${token}`);
    await browser.close();

    return token;
}

function runTest(testFile, env) {
    console.log(`Starting k6 test '${testFile}' with env ${JSON.stringify(env)}`);
    console.log(`Test started at ${new Date().toUTCString()}`)

    // Create results directory if it doesn't exist
    const resultsDir = 'results';
    if (!fs.existsSync(resultsDir)) {
        fs.mkdirSync(resultsDir, {recursive: true});
    }

    // Generate output file name
    const testFileName = path.basename(testFile, path.extname(testFile));
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getDate()).padStart(2, '0')}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getHours()).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}-${String(now.getSeconds()).padStart(2, '0')}`;
    const outputFile = path.join(resultsDir, `${formattedDate}-${testFileName}-k6-results.txt`);

    // Create write stream for the output file
    const outputStream = fs.createWriteStream(outputFile);

    // Helper function to log both to console and file
    const logToFile = (message) => {
        console.log(message);
        outputStream.write(message + '\n');
    };

    const errorToFile = (message) => {
        console.error(message);
        outputStream.write(message + '\n');
    };

    const k6Process = spawn('k6', ['run', testFile, '-v', '--summary-mode', 'full'], {
        env: {...process.env, ...env}, stdio: ['ignore', 'pipe', 'pipe'] // stdin ignored, stdout and stderr piped
    });

    // Pipe both stdout and stderr to the file and console
    k6Process.stdout.pipe(outputStream);
    k6Process.stderr.pipe(outputStream);

    // Also show output in console
    k6Process.stdout.pipe(process.stdout);
    k6Process.stderr.pipe(process.stderr);

    k6Process.on('close', (code) => {
        const exitMessage = `k6 process exited with code ${code}`;
        const finishedMessage = `Finished test at ${new Date().toUTCString()}`;
        const savedMessage = `Results saved to: ${outputFile}`;

        logToFile(exitMessage);
        logToFile(finishedMessage);
        logToFile(savedMessage);

        outputStream.end();
    });

    k6Process.on('error', (error) => {
        const errorMessage = `Failed to start k6 process: ${error}`;
        errorToFile(errorMessage);
        outputStream.end();
    });
}

const envs = getEnvs(env => ({
    TEST_FILE: 'The test file to run (e.g. "testAppliances.js")',
    API_URL: 'The API URL (e.g. "https://backend.api.staging.heyalfie.com" or "http://localhost:8000")',
    APP_URL: 'The app URL (e.g. "https://app.staging.heyalfie.com" or "http://localhost:3000")',
    USER_EMAIL: 'The user email to use for signing in as the test user',
    USER_PASSWORD: 'The user password to use for signing in as the test user',
    VIRTUAL_USERS: 'The number of virtual users to use',
    PROMPT: env.TEST_FILE === 'testStreamResponse.js' ? 'Prompt used in the chats' : undefined,
    FILE_PATH: env.TEST_FILE === 'testFileUpload.js' ? 'File used in the file upload' : undefined,
}))
const token = await signInAndGetToken(envs.APP_URL, envs.USER_EMAIL, envs.USER_PASSWORD);
runTest(envs.TEST_FILE, {TOKEN: token, API_URL: envs.API_URL});
