import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
    stages: [
        {duration: '1s', target: __ENV.VIRTUAL_USERS},
        {duration: '45s', target: __ENV.VIRTUAL_USERS}
    ],
    thresholds: {
        http_req_failed: ['rate==0'],
    },
};

export default function () {
    const token = __ENV.TOKEN;
    const apiUrl = __ENV.API_URL;
    const prompt = __ENV.PROMPT;

    const url = `${apiUrl}/messages/stream`;

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
    };

    const payload = JSON.stringify({
        message: {
            content: prompt,
            type: 'text',
            additionalData: {
                device: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                location: 'https://app.staging.heyalfie.com/'
            }
        },
        attachments: []
    });

    const response = http.post(url, payload, { headers });
    console.log(`Received response: ${response.body}`);

    // Basic response checks
    check(response, {
        'status is 200': (r) => r.status === 200,
        'response time < 30s': (r) => r.timings.duration < 30000,
        'contains stream_send_message_response': (r) => r.body.includes('stream_send_message_response'),
        'contains content data': (r) => r.body.includes('"type": "content"'),
        'contains final_data': (r) => r.body.includes('"type": "final_data"'),
        'ends with event: end': (r) => r.body.includes('event: end'),
    });

    // Parse and validate the streaming response
    if (response.status === 200) {
        const lines = response.body.split('\n');
        let hasInitialResponse = false;
        let hasContent = false;
        let hasFinalData = false;
        let hasEndEvent = false;

        for (const line of lines) {
            if (line.startsWith('data: ')) {
                try {
                    const data = JSON.parse(line.substring(6));

                    // Check for initial response
                    if (data.type === 'stream_send_message_response') {
                        hasInitialResponse = true;
                        check(data.data, {
                            'has chatId': (d) => d.chatId !== undefined,
                            'has userMessageId': (d) => d.userMessageId !== undefined,
                            'has systemMessageId': (d) => d.systemMessageId !== undefined,
                        });
                    }

                    // Check for content
                    if (data.type === 'content') {
                        hasContent = true;
                        check(data, {
                            'content has data': (d) => d.data !== undefined && d.data.length > 0,
                        });
                    }

                    // Check for final data
                    if (data.type === 'final_data') {
                        hasFinalData = true;
                    }
                } catch (e) {
                    console.error('Failed to parse JSON:', line);
                }
            }

            if (line === 'event: end') {
                hasEndEvent = true;
            }
        }

        // Validate the complete streaming response structure
        check(response, {
            'has initial response': () => hasInitialResponse,
            'has content chunks': () => hasContent,
            'has final data': () => hasFinalData,
            'has end event': () => hasEndEvent,
        });
    }
}

// Optional: Add a setup function to validate the token
export function setup() {
    // You can add token validation here if needed
    console.log('Starting k6 test for Alfie streaming API');
}

// Optional: Add a teardown function
export function teardown() {
    console.log('k6 test completed');
}