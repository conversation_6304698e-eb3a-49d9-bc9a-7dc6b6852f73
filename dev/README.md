# dev

Hey<PERSON><PERSON><PERSON>'s developer environment. It contains HeyAlfie CLI that allows you to 
- run the whole HeyAlfie system locally (aka "developer local env"),
- run stress tests.

**Note: This project is dedicated to devs with Mac OS! If you use a different OS, please reach out
to [<PERSON><PERSON><PERSON>wa](<EMAIL>)**.

## Prerequisites

To use this, make sure you have the following tools installed:

- [Docker](https://docs.docker.com/desktop/setup/install/mac-install/)
- [Python](https://www.python.org/downloads/macos/)
- [Node](https://nodejs.org/en/download) (Note: it's needed for testing only - see below)
- Your personal `.env` file for local development (contact [Piotr Śliwa](<EMAIL>) if you don't have it)

also ensure you have the following repositories cloned:
- [Backend](https://github.com/Hey-Alfie/backend)
- [Frontend](https://github.com/Hey-Alfie/frontend)

## Run HeyAlfie locally

### 1. Copy your personal `.env` file to this directory

Copy the `.env` file to the directory here (contact [<PERSON>otr <PERSON>liwa](<EMAIL>) if you don't have it).

### 2. Start the whole HeyAlfie system

The HeyAlfie CLI wraps the Docker Compose executable and passes all arguments to it so you can use

```bash
./heyalfie up --build
```

to build and run the whole system locally (you can alternatively add `-d` flag to detach the process,
and other parameters as per [Docker Compose CLI reference](https://docs.docker.com/reference/cli/docker/compose/)).

Docker Compose will have to access `frontend` and `backend` repositories
which you should have cloned. It will build images based on the current version (you can checkout
any version and modify it however you need). The runner (see below) will ask you to provide paths to
the cloned repositories when you run it for the first time.

#### Services

Services started with the command above will be accessible via `localhost`:

- HeyAlfie Frontend: [`http://localhost:3000`](http://localhost:3000) (**Use this to access the HeyAlfie web app**)
- HeyAlfie Backend: `http://localhost:8000`
- Postgres: `dev-user:password@localhost:5432/dev_db`
- Qdrant: `http://localhost:6333` (API key: `qdrant-key`)

### 3. Stop HeyAlfie

Use Command+C or

```bash
./heyalfie down
```

to stop.

### Debug selected components

This local environment allows you to debug selected components, with all other components running in Docker.
For example, you can run either Frontend or Backend in debug mode in your IDE, and everything else
running in Docker.

First, replace `.env` files in the configured local `frontend` and `backend` repositories. To do this
automatically, run

```bash
./heyalfie env
```

**Note: Make sure you run `./heyalfie up --build` before (the services need to be built to extract the env variables
with the command above).**

Then, you can run the system in Docker Compose, excluding selected services with `--scale SERVICE_NAME=0` parameter.

For example, to run everything but `backend`, run

```bash
./heyalfie up --build --scale backend=0
```

Then, you can run/debug the components in your IDE, e.g.

```bash
python src/main.py
```

## Run stress tests

To run stress tests, run `./heyalfie stress` and provide test configuration 
with environment properties. To get the list of required variables run 

```bash
(dev) piotrsliwa@Piotrs-MacBook-Pro-HA dev % ./heyalfie stress
Stress testing...

*** REQUIRED ENVIRONMENTAL VARIABLES:

TEST_FILE: The test file to run (e.g. "testAppliances.js")
API_URL: The API URL (e.g. "https://backend.api.staging.heyalfie.com" or "http://localhost:8000")
APP_URL: The app URL (e.g. "https://app.staging.heyalfie.com" or "http://localhost:3000")
USER_EMAIL: The user email to use for signing in as the test user
USER_PASSWORD: The user password to use for signing in as the test user
VIRTUAL_USERS: The number of virtual users to use
```

Test files:

- `testAppliances.js` - performs sequences of simple CRUD operations (properties and appliances)
- `testFileUpload.js` - uploads a selected file from the local file system (provided to the test script with the `FILE_PATH` variable)
- `testStreamResponse.js` - sends a prompt (passed with the `PROMPT` env variable) and listens for streamed response

Example:

```bash
TEST_FILE='testStreamResponse.js' \
USER_EMAIL='<EMAIL>' \
USER_PASSWORD='o2i3r8u283' \
API_URL='https://backend.api.staging.heyalfie.com' \
APP_URL='https://app.staging.heyalfie.com' \
VIRTUAL_USERS=1 \
PROMPT='Help me with an issue!' \
./heyalfie stress
```